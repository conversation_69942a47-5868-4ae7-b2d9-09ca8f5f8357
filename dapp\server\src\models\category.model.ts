import mongoose, { Schema } from "mongoose";

import { CategoryDocument } from "../types/category.types";

/**
 * MongoDB schema for Category
 */
const categorySchema = new Schema<CategoryDocument>(
  {
    name: {
      type: String,
      required: true,
      trim: true,
      index: true,
    },
    description: {
      type: String,
      default: "",
      trim: true,
    },
    slug: {
      type: String,
      required: true,
      unique: true,
      lowercase: true,
      trim: true,
      index: true,
    },
    icon: {
      type: String,
      default: "📦",
    },
    color: {
      type: String,
      default: "#3B82F6",
      match: /^#[0-9A-F]{6}$/i, // Hex color validation
    },
    isActive: {
      type: Boolean,
      default: true,
      index: true,
    },
    productCount: {
      type: Number,
      default: 0,
      min: 0,
    },
    parentId: {
      type: Schema.Types.ObjectId,
      ref: "Category",
      default: null,
    },
    sortOrder: {
      type: Number,
      default: 0,
      index: true,
    },
  },
  {
    timestamps: true, // Adds createdAt and updatedAt
    versionKey: false, // Removes __v field
  }
);

// Add compound indexes for common queries
categorySchema.index({ isActive: 1, sortOrder: 1 });
categorySchema.index({ parentId: 1, sortOrder: 1 });

// Add text index for search functionality
categorySchema.index(
  {
    name: "text",
    description: "text",
  },
  {
    weights: {
      name: 10,
      description: 1,
    },
    name: "category_text_index",
  }
);

// Pre-save middleware to generate slug if not provided
categorySchema.pre("save", function (next) {
  if (this.isModified("name") && !this.slug) {
    this.slug = this.name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/(^-|-$)/g, "");
  }
  next();
});

// Create and export the model
export const CategoryModel = mongoose.model<CategoryDocument>(
  "Category",
  categorySchema
);

// Export as Category for consistency
export const Category = CategoryModel;

// Export CategoryDocument type for use in services
export type { CategoryDocument } from "../types/category.types";
