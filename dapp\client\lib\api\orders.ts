// Import consolidated types
import { ApiResponse } from "@/types/api";
import {
  Address,
  CreateOrderDto,
  CurrencyUnit,
  Customer,
  Order,
  OrderFilters,
  OrderItem,
  OrderStats,
  OrderStatus,
  OrdersResponse,
  Payment,
  PaymentMethod,
  PaymentStatus,
  Shipping,
  ShippingStatus,
  UpdateOrderDto,
} from "@/types/order";

const getApiBaseUrl = (): string => {
  if (typeof window !== "undefined") {
    // Client-side: use current origin or environment variable
    return (
      process.env.NEXT_PUBLIC_API_URL ||
      `${window.location.protocol}//${window.location.hostname}:3010`
    );
  }
  // Server-side: use environment variable or default
  return process.env.NEXT_PUBLIC_API_URL || "http://localhost:3010";
};

const API_BASE_URL = getApiBaseUrl();

// Re-export types for backward compatibility
export type {
  Order,
  OrderItem,
  Customer,
  Address,
  Shipping,
  Payment,
  CreateOrderDto,
  UpdateOrderDto,
  OrderFilters,
  OrderStats,
  OrdersResponse,
  OrderStatus,
  ShippingStatus,
  PaymentStatus,
  PaymentMethod,
  CurrencyUnit,
};

/**
 * Get all orders with optional filtering and pagination
 */
export const getOrders = async (
  filters: OrderFilters = {},
  page: number = 1,
  limit: number = 20,
  sortBy: string = "placedAt",
  sortOrder: "asc" | "desc" = "desc"
): Promise<OrdersResponse> => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    sortBy,
    sortOrder,
  });

  // Add filters to params
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== "") {
      if (Array.isArray(value)) {
        value.forEach((v) => params.append(key, v.toString()));
      } else {
        params.append(key, value.toString());
      }
    }
  });

  const response = await fetch(`${API_BASE_URL}/api/orders?${params}`);

  if (!response.ok) {
    throw new Error(`Failed to fetch orders: ${response.statusText}`);
  }

  const result: ApiResponse<OrdersResponse> = await response.json();

  if (!result.success) {
    throw new Error(result.error || "Failed to fetch orders");
  }

  return result.data;
};

/**
 * Get order by ID
 */
export const getOrderById = async (orderId: string): Promise<Order> => {
  const response = await fetch(`${API_BASE_URL}/api/orders/${orderId}`);

  if (!response.ok) {
    throw new Error(`Failed to fetch order: ${response.statusText}`);
  }

  const result: ApiResponse<Order> = await response.json();

  if (!result.success) {
    throw new Error(result.error || "Failed to fetch order");
  }

  return result.data;
};

/**
 * Get order by order number
 */
export const getOrderByNumber = async (orderNumber: string): Promise<Order> => {
  const response = await fetch(
    `${API_BASE_URL}/api/orders/number/${orderNumber}`
  );

  if (!response.ok) {
    throw new Error(`Failed to fetch order: ${response.statusText}`);
  }

  const result: ApiResponse<Order> = await response.json();

  if (!result.success) {
    throw new Error(result.error || "Failed to fetch order");
  }

  return result.data;
};

/**
 * Create a new order
 */
export const createOrder = async (
  orderData: CreateOrderDto
): Promise<Order> => {
  const response = await fetch(`${API_BASE_URL}/api/orders`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(orderData),
  });

  if (!response.ok) {
    throw new Error(`Failed to create order: ${response.statusText}`);
  }

  const result: ApiResponse<Order> = await response.json();

  if (!result.success) {
    throw new Error(result.error || "Failed to create order");
  }

  return result.data;
};

/**
 * Update an order
 */
export const updateOrder = async (
  orderId: string,
  updateData: UpdateOrderDto
): Promise<Order> => {
  const response = await fetch(`${API_BASE_URL}/api/orders/${orderId}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(updateData),
  });

  if (!response.ok) {
    throw new Error(`Failed to update order: ${response.statusText}`);
  }

  const result: ApiResponse<Order> = await response.json();

  if (!result.success) {
    throw new Error(result.error || "Failed to update order");
  }

  return result.data;
};

/**
 * Update order status
 */
export const updateOrderStatus = async (
  orderId: string,
  status: OrderStatus,
  paymentStatus?: PaymentStatus,
  shippingStatus?: ShippingStatus
): Promise<Order> => {
  const response = await fetch(`${API_BASE_URL}/api/orders/${orderId}/status`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ status, paymentStatus, shippingStatus }),
  });

  if (!response.ok) {
    throw new Error(`Failed to update order status: ${response.statusText}`);
  }

  const result: ApiResponse<Order> = await response.json();

  if (!result.success) {
    throw new Error(result.error || "Failed to update order status");
  }

  return result.data;
};

/**
 * Delete (cancel) an order
 */
export const deleteOrder = async (orderId: string): Promise<void> => {
  const response = await fetch(`${API_BASE_URL}/api/orders/${orderId}`, {
    method: "DELETE",
  });

  if (!response.ok) {
    throw new Error(`Failed to delete order: ${response.statusText}`);
  }

  const result: ApiResponse<void> = await response.json();

  if (!result.success) {
    throw new Error(result.error || "Failed to delete order");
  }
};

/**
 * Get order statistics
 */
export const getOrderStats = async (
  dateFrom?: string,
  dateTo?: string
): Promise<OrderStats> => {
  const params = new URLSearchParams();
  if (dateFrom) params.append("dateFrom", dateFrom);
  if (dateTo) params.append("dateTo", dateTo);

  const response = await fetch(`${API_BASE_URL}/api/orders/stats?${params}`);

  if (!response.ok) {
    throw new Error(`Failed to fetch order statistics: ${response.statusText}`);
  }

  const result: ApiResponse<OrderStats> = await response.json();

  if (!result.success) {
    throw new Error(result.error || "Failed to fetch order statistics");
  }

  return result.data;
};
