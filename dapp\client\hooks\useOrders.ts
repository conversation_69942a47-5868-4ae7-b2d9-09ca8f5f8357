import { useState, useEffect, useCallback } from "react";
import { toast } from "sonner";
import {
  Order,
  OrdersResponse,
  OrderFilters,
  OrderStats,
  CreateOrderDto,
  UpdateOrderDto,
  OrderStatus,
  PaymentStatus,
  ShippingStatus,
  getOrders,
  getOrderById,
  getOrderByNumber,
  createOrder,
  updateOrder,
  updateOrderStatus,
  deleteOrder,
  getOrderStats,
} from "@/lib/api/orders";

/**
 * Hook for fetching orders with filtering and pagination
 */
export const useOrders = (
  initialFilters: OrderFilters = {},
  initialPage: number = 1,
  initialLimit: number = 20,
  initialSortBy: string = "placedAt",
  initialSortOrder: "asc" | "desc" = "desc"
) => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(initialPage);
  const [totalPages, setTotalPages] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<OrderFilters>(initialFilters);
  const [limit] = useState(initialLimit);
  const [sortBy] = useState(initialSortBy);
  const [sortOrder] = useState(initialSortOrder);

  const fetchOrders = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await getOrders(filters, page, limit, sortBy, sortOrder);
      setOrders(response.orders);
      setTotal(response.total);
      setTotalPages(response.totalPages);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch orders";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [filters, page, limit, sortBy, sortOrder]);

  useEffect(() => {
    fetchOrders();
  }, [fetchOrders]);

  const refetch = useCallback(() => {
    fetchOrders();
  }, [fetchOrders]);

  const updateFilters = useCallback((newFilters: OrderFilters) => {
    setFilters(newFilters);
    setPage(1); // Reset to first page when filters change
  }, []);

  const updatePage = useCallback((newPage: number) => {
    setPage(newPage);
  }, []);

  return {
    orders,
    total,
    page,
    totalPages,
    loading,
    error,
    filters,
    refetch,
    updateFilters,
    updatePage,
  };
};

/**
 * Hook for fetching a single order by ID
 */
export const useOrder = (orderId: string | null) => {
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchOrder = useCallback(async () => {
    if (!orderId) return;

    setLoading(true);
    setError(null);
    
    try {
      const orderData = await getOrderById(orderId);
      setOrder(orderData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch order";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [orderId]);

  useEffect(() => {
    fetchOrder();
  }, [fetchOrder]);

  const refetch = useCallback(() => {
    fetchOrder();
  }, [fetchOrder]);

  return {
    order,
    loading,
    error,
    refetch,
  };
};

/**
 * Hook for fetching a single order by order number
 */
export const useOrderByNumber = (orderNumber: string | null) => {
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchOrder = useCallback(async () => {
    if (!orderNumber) return;

    setLoading(true);
    setError(null);
    
    try {
      const orderData = await getOrderByNumber(orderNumber);
      setOrder(orderData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch order";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [orderNumber]);

  useEffect(() => {
    fetchOrder();
  }, [fetchOrder]);

  const refetch = useCallback(() => {
    fetchOrder();
  }, [fetchOrder]);

  return {
    order,
    loading,
    error,
    refetch,
  };
};

/**
 * Hook for order mutations (create, update, delete)
 */
export const useOrderMutations = () => {
  const [loading, setLoading] = useState(false);

  const handleCreateOrder = useCallback(async (orderData: CreateOrderDto): Promise<Order> => {
    setLoading(true);
    try {
      const newOrder = await createOrder(orderData);
      toast.success("Order created successfully");
      return newOrder;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to create order";
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const handleUpdateOrder = useCallback(async (orderId: string, updateData: UpdateOrderDto): Promise<Order> => {
    setLoading(true);
    try {
      const updatedOrder = await updateOrder(orderId, updateData);
      toast.success("Order updated successfully");
      return updatedOrder;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to update order";
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const handleUpdateOrderStatus = useCallback(async (
    orderId: string,
    status: OrderStatus,
    paymentStatus?: PaymentStatus,
    shippingStatus?: ShippingStatus
  ): Promise<Order> => {
    setLoading(true);
    try {
      const updatedOrder = await updateOrderStatus(orderId, status, paymentStatus, shippingStatus);
      toast.success("Order status updated successfully");
      return updatedOrder;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to update order status";
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const handleDeleteOrder = useCallback(async (orderId: string): Promise<void> => {
    setLoading(true);
    try {
      await deleteOrder(orderId);
      toast.success("Order cancelled successfully");
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to cancel order";
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    createOrder: handleCreateOrder,
    updateOrder: handleUpdateOrder,
    updateOrderStatus: handleUpdateOrderStatus,
    deleteOrder: handleDeleteOrder,
  };
};

/**
 * Hook for fetching order statistics
 */
export const useOrderStats = (dateFrom?: string, dateTo?: string) => {
  const [stats, setStats] = useState<OrderStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const statsData = await getOrderStats(dateFrom, dateTo);
      setStats(statsData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch order statistics";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [dateFrom, dateTo]);

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  const refetch = useCallback(() => {
    fetchStats();
  }, [fetchStats]);

  return {
    stats,
    loading,
    error,
    refetch,
  };
};
