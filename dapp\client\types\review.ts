export type ReviewStatus = "pending" | "published" | "hidden" | "flagged";

export type Review = {
  id: string;
  userId: string;
  productId: string;
  rating: number;
  title?: string;
  comment?: string;
  images?: string[];
  status?: ReviewStatus;
  isVerified?: boolean;
  helpfulCount?: number;
  createdAt: string;
  updatedAt: string;
};

export type ReviewFilters = {
  rating?: string;
  status?: string;
  search?: string;
  productId?: string;
  userId?: string;
};

export type ReviewStats = {
  totalReviews: number;
  averageRating: number;
  ratingDistribution: Record<number, number>;
  pendingReviews: number;
};
