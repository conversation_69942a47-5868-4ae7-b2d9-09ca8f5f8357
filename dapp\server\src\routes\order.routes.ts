import { Router } from "express";
import { OrderController } from "../controllers/order/order.controller";

const router = Router();
const orderController = new OrderController();

// GET /api/orders/stats - Get order statistics
router.get("/stats", orderController.getOrderStats);

// GET /api/orders/number/:orderNumber - Get order by order number
router.get("/number/:orderNumber", orderController.getOrderByNumber);

// GET /api/orders - Get all orders with optional filtering
router.get("/", orderController.getOrders);

// GET /api/orders/:id - Get order by ID
router.get("/:id", orderController.getOrderById);

// POST /api/orders - Create a new order
router.post("/", orderController.createOrder);

// PUT /api/orders/:id - Update an order
router.put("/:id", orderController.updateOrder);

// PUT /api/orders/:id/status - Update order status
router.put("/:id/status", orderController.updateOrderStatus);

// DELETE /api/orders/:id - Delete an order (soft delete)
router.delete("/:id", orderController.deleteOrder);

export default router;
