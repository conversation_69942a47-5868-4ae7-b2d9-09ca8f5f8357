import { MaterialProperties } from "./common";

export type MaterialType =
  | "fabric"
  | "metal"
  | "plastic"
  | "wood"
  | "glass"
  | "ceramic"
  | "leather"
  | "rubber"
  | "composite"
  | "natural"
  | "synthetic"
  | "other";

export type Material = {
  _id: string;
  name: string;
  description: string;
  slug: string;
  type: MaterialType;
  icon?: string;
  color?: string;
  properties: MaterialProperties;
  isActive: boolean;
  productCount: number;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
};

export type CreateMaterialDto = {
  name: string;
  description?: string;
  type: MaterialType;
  icon?: string;
  color?: string;
  properties?: Partial<MaterialProperties>;
  isActive?: boolean;
  sortOrder?: number;
};

export type UpdateMaterialDto = {
  name?: string;
  description?: string;
  slug?: string;
  type?: MaterialType;
  icon?: string;
  color?: string;
  properties?: Partial<MaterialProperties>;
  isActive?: boolean;
  sortOrder?: number;
};

export type MaterialFilters = {
  isActive?: boolean;
  search?: string;
  durability?: "low" | "medium" | "high";
  waterResistant?: boolean;
  recyclable?: boolean;
  weight?: "light" | "medium" | "heavy";
};

export type MaterialsResponse = {
  materials: Material[];
  total: number;
  page: number;
  totalPages: number;
};
