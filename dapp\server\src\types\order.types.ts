import { Document } from "mongoose";

/**
 * Order status enumeration
 */
export type OrderStatus =
  | "pending"
  | "paid"
  | "processing"
  | "shipped"
  | "delivered"
  | "cancelled"
  | "refunded"
  | "returned"
  | "failed";

/**
 * Shipping status enumeration
 */
export type ShippingStatus =
  | "pending"
  | "preparing"
  | "shipped"
  | "in-transit"
  | "delivered"
  | "returned"
  | "cancelled";

/**
 * Payment status enumeration
 */
export type PaymentStatus =
  | "pending"
  | "paid"
  | "failed"
  | "refunded"
  | "partially-refunded";

/**
 * Payment method enumeration
 */
export type PaymentMethod =
  | "card"
  | "paypal"
  | "bank-transfer"
  | "cash-on-delivery"
  | "crypto";

/**
 * Currency enumeration
 */
export type CurrencyUnit = "EUR" | "USD";

/**
 * Order item type
 */
export type IOrderItem = {
  productId: string;
  name: string;
  quantity: number;
  price: number;
  currency: CurrencyUnit;
  image?: string;
  sku?: string;
  variant?: string;
};

/**
 * Customer information type
 */
export type ICustomer = {
  customerId?: string;
  name: string;
  email: string;
  phone?: string;
  avatar?: string;
};

/**
 * Address type
 */
export type IAddress = {
  street: string;
  city: string;
  state?: string;
  postalCode: string;
  country: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
};

/**
 * Shipping information type
 */
export type IShipping = {
  method: string;
  cost: number;
  trackingNumber?: string;
  estimatedDelivery?: Date;
  shippedAt?: Date;
  deliveredAt?: Date;
};

/**
 * Payment information type
 */
export type IPayment = {
  method: PaymentMethod;
  status: PaymentStatus;
  transactionId?: string;
  providerTransactionId?: string;
  cardLast4?: string;
  cardType?: string;
  receiptUrl?: string;
  paidAt?: Date;
};

/**
 * Order type matching the frontend Order type
 */
export type IOrder = {
  orderNumber: string;
  customer: ICustomer;
  items: IOrderItem[];

  // Pricing
  subtotal: number;
  shippingCost: number;
  tax: number;
  discount: number;
  totalAmount: number;
  currency: CurrencyUnit;

  // Status
  status: OrderStatus;
  paymentStatus: PaymentStatus;
  shippingStatus: ShippingStatus;

  // Addresses
  shippingAddress: IAddress;
  billingAddress?: IAddress;

  // Payment & Shipping
  payment: IPayment;
  shipping: IShipping;

  // Additional info
  notes?: string;
  internalNotes?: string;
  tags?: string[];

  // Timestamps
  placedAt: Date;
  estimatedDelivery?: Date;
};

/**
 * Order document interface for MongoDB (keeping as interface for Document extension)
 */
export interface OrderDocument extends IOrder, Document {
  _id: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * DTO for creating a new order
 */
export type CreateOrderDto = {
  customer: ICustomer;
  items: IOrderItem[];
  shippingAddress: IAddress;
  billingAddress?: IAddress;
  shipping: {
    method: string;
    cost: number;
  };
  payment: {
    method: PaymentMethod;
    transactionId?: string;
  };
  notes?: string;
  currency?: CurrencyUnit;
};

/**
 * DTO for updating an order
 */
export type UpdateOrderDto = {
  status?: OrderStatus;
  paymentStatus?: PaymentStatus;
  shippingStatus?: ShippingStatus;
  shipping?: Partial<IShipping>;
  payment?: Partial<IPayment>;
  notes?: string;
  internalNotes?: string;
  tags?: string[];
};

/**
 * Order filters for querying
 */
export type OrderFilters = {
  status?: OrderStatus | OrderStatus[];
  paymentStatus?: PaymentStatus | PaymentStatus[];
  shippingStatus?: ShippingStatus | ShippingStatus[];
  customerId?: string;
  customerEmail?: string;
  orderNumber?: string;
  dateFrom?: Date;
  dateTo?: Date;
  minAmount?: number;
  maxAmount?: number;
  search?: string;
};

/**
 * Order statistics type
 */
export type OrderStats = {
  totalOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
  ordersByStatus: Record<OrderStatus, number>;
  ordersByPaymentStatus: Record<PaymentStatus, number>;
  ordersByShippingStatus: Record<ShippingStatus, number>;
  recentOrders: number;
  pendingOrders: number;
};
