export const products = [
  {
    id: 1,
    name: "T-shirt for Men",
    price: "$90.00",
    originalPrice: "$120.00",
    image: "/images/t-shirt.jpg",
    category: "Clothing",
    brand: "Fashion Co",
    stock: 25,
    status: "active" as const,
    rating: 4,
    isOnSale: true,
  },
  {
    id: 2,
    name: "Travel Bag Jeans",
    price: "$19.50",
    image: "/images/jeans.jpg",
    category: "Accessories",
    brand: "Travel Pro",
    stock: 0,
    status: "active" as const,
    rating: 3,
    isOnSale: false,
  },
  {
    id: 3,
    name: "Jeans shorts",
    price: "$70.00",
    image: "/images/shorts.jpg",
    category: "Clothing",
    brand: "Denim Works",
    stock: 8,
    status: "active" as const,
    rating: 5,
    isOnSale: false,
  },
  {
    id: 4,
    name: "Sofa for interior",
    price: "$375.00",
    originalPrice: "$450.00",
    image: "/images/sofa.jpg",
    category: "Furniture",
    brand: "Home Style",
    stock: 3,
    status: "active" as const,
    rating: 4,
    isOnSale: true,
  },
  {
    id: 5,
    name: "Leather Wallet",
    price: "$375.00",
    image: "/images/wallet.jpg",
    category: "Accessories",
    brand: "Leather Craft",
    stock: 15,
    status: "draft" as const,
    rating: 0,
    isOnSale: false,
  },
  {
    id: 6,
    name: "Warm Hat",
    price: "$45.00",
    image: "/images/hat.jpg",
    category: "Clothing",
    brand: "Winter Wear",
    stock: 50,
    status: "active" as const,
    rating: 4,
    isOnSale: false,
  },
  {
    id: 7,
    name: "Winter Jacket",
    price: "$120.00",
    originalPrice: "$180.00",
    image: "/images/winter-jacket.jpg",
    category: "Clothing",
    brand: "Winter Wear",
    stock: 12,
    status: "active" as const,
    rating: 5,
    isOnSale: true,
  },
  {
    id: 8,
    name: "Office Chair",
    price: "$210.00",
    image: "/images/office-chair.jpg",
    category: "Furniture",
    brand: "Office Pro",
    stock: 7,
    status: "archived" as const,
    rating: 3,
    isOnSale: false,
  },
  {
    id: 9,
    name: "Running Shoes",
    price: "$89.99",
    image: "/images/running-shoes.jpg",
    category: "Footwear",
    brand: "Sport Pro",
    stock: 20,
    status: "active" as const,
    rating: 5,
    isOnSale: false,
  },
  {
    id: 10,
    name: "Smart Watch",
    price: "$199.00",
    originalPrice: "$249.00",
    image: "/images/smart-watch.jpg",
    category: "Electronics",
    brand: "Tech Gear",
    stock: 2,
    status: "active" as const,
    rating: 4,
    isOnSale: true,
  },
  {
    id: 11,
    name: "Sunglasses",
    price: "$59.00",
    image: "/images/sunglasses.jpg",
    category: "Accessories",
    brand: "Style Co",
    stock: 18,
    status: "active" as const,
    rating: 3,
    isOnSale: false,
  },
  {
    id: 12,
    name: "Backpack",
    price: "$85.50",
    image: "/images/backpack.jpg",
    category: "Accessories",
    brand: "Travel Pro",
    stock: 35,
    status: "active" as const,
    rating: 4,
    isOnSale: false,
  },
];

export const orderProducts = [
  {
    imageSrc: "/images/backpack.jpg",
    name: "Backpack",
    quantity: 2,
    unitPrice: "$43.50",
    total: "$87.00",
  },
  {
    imageSrc: "/images/smart-watch.jpg",
    name: "Smart Watch",
    quantity: 1,
    unitPrice: "$43.50",
    total: "$87.00",
  },
  {
    imageSrc: "/images/sunglasses.jpg",
    name: "Sunglasses",
    quantity: 1,
    unitPrice: "$43.50",
    total: "$87.00",
  },
  {
    imageSrc: "/images/running-shoes.jpg",
    name: "Running Shoes",
    quantity: 1,
    unitPrice: "$81.50",
    total: "$87.00",
  },
];

// Mock orders data for the orders list
export const mockOrders = [
  {
    id: "ORD-001",
    customer: {
      name: "John Doe",
      email: "<EMAIL>",
      avatar: "/images/avatar-1.jpg",
    },
    items: [
      { name: "Backpack", quantity: 2, image: "/images/backpack.jpg" },
      { name: "Smart Watch", quantity: 1, image: "/images/smart-watch.jpg" },
    ],
    totalAmount: 299.99,
    status: "paid",
    shippingStatus: "shipped",
    placedAt: "2024-01-15T10:30:00Z",
    updatedAt: "2024-01-16T14:20:00Z",
  },
  {
    id: "ORD-002",
    customer: {
      name: "Jane Smith",
      email: "<EMAIL>",
      avatar: "/images/avatar-2.jpg",
    },
    items: [
      { name: "T-shirt for Men", quantity: 3, image: "/images/t-shirt.jpg" },
    ],
    totalAmount: 270.0,
    status: "pending",
    shippingStatus: "pending",
    placedAt: "2024-01-14T16:45:00Z",
    updatedAt: "2024-01-14T16:45:00Z",
  },
  {
    id: "ORD-003",
    customer: {
      name: "Mike Johnson",
      email: "<EMAIL>",
      avatar: "/images/avatar-3.jpg",
    },
    items: [
      { name: "Sofa for interior", quantity: 1, image: "/images/sofa.jpg" },
      { name: "Office Chair", quantity: 2, image: "/images/office-chair.jpg" },
    ],
    totalAmount: 795.0,
    status: "delivered",
    shippingStatus: "delivered",
    placedAt: "2024-01-12T09:15:00Z",
    updatedAt: "2024-01-18T11:30:00Z",
  },
  {
    id: "ORD-004",
    customer: {
      name: "Sarah Wilson",
      email: "<EMAIL>",
      avatar: "/images/avatar-4.jpg",
    },
    items: [
      { name: "Leather Wallet", quantity: 1, image: "/images/wallet.jpg" },
      { name: "Warm Hat", quantity: 2, image: "/images/hat.jpg" },
    ],
    totalAmount: 465.0,
    status: "shipped",
    shippingStatus: "shipped",
    placedAt: "2024-01-13T14:20:00Z",
    updatedAt: "2024-01-17T08:45:00Z",
  },
  {
    id: "ORD-005",
    customer: {
      name: "David Brown",
      email: "<EMAIL>",
      avatar: "/images/avatar-5.jpg",
    },
    items: [
      {
        name: "Winter Jacket",
        quantity: 1,
        image: "/images/winter-jacket.jpg",
      },
      { name: "Jeans shorts", quantity: 2, image: "/images/shorts.jpg" },
    ],
    totalAmount: 260.0,
    status: "cancelled",
    shippingStatus: "cancelled",
    placedAt: "2024-01-11T12:00:00Z",
    updatedAt: "2024-01-12T10:15:00Z",
  },
  {
    id: "ORD-006",
    customer: {
      name: "Emily Davis",
      email: "<EMAIL>",
      avatar: "/images/avatar-6.jpg",
    },
    items: [
      {
        name: "Running Shoes",
        quantity: 1,
        image: "/images/running-shoes.jpg",
      },
      { name: "Travel Bag Jeans", quantity: 1, image: "/images/jeans.jpg" },
    ],
    totalAmount: 101.5,
    status: "refunded",
    shippingStatus: "returned",
    placedAt: "2024-01-10T15:30:00Z",
    updatedAt: "2024-01-19T13:20:00Z",
  },
];

// Mock customers data for the customers list
export const mockCustomers = [
  {
    id: "CUST-001",
    name: "John Doe",
    email: "<EMAIL>",
    avatar: "/images/avatar-1.jpg",
    phone: "+****************",
    location: "New York, NY",
    joinedAt: "2023-08-15T10:30:00Z",
    lastOrderAt: "2024-01-15T14:20:00Z",
    totalOrders: 12,
    totalSpent: 2450.75,
    status: "active" as const,
    customerType: "premium",
    tags: ["vip", "frequent-buyer"],
  },
  {
    id: "CUST-002",
    name: "Jane Smith",
    email: "<EMAIL>",
    avatar: "/images/avatar-2.jpg",
    phone: "+****************",
    location: "Los Angeles, CA",
    joinedAt: "2023-11-22T16:45:00Z",
    lastOrderAt: "2024-01-14T11:30:00Z",
    totalOrders: 8,
    totalSpent: 1680.5,
    status: "active" as const,
    customerType: "regular",
    tags: ["loyal"],
  },
  {
    id: "CUST-003",
    name: "Mike Johnson",
    email: "<EMAIL>",
    avatar: "/images/avatar-3.jpg",
    phone: "+****************",
    location: "Chicago, IL",
    joinedAt: "2023-05-10T09:15:00Z",
    lastOrderAt: "2024-01-18T16:45:00Z",
    totalOrders: 25,
    totalSpent: 4890.25,
    status: "active" as const,
    customerType: "vip",
    tags: ["vip", "bulk-buyer", "corporate"],
  },
  {
    id: "CUST-004",
    name: "Sarah Wilson",
    email: "<EMAIL>",
    avatar: "/images/avatar-4.jpg",
    phone: "+****************",
    location: "Miami, FL",
    joinedAt: "2023-12-05T14:20:00Z",
    lastOrderAt: "2024-01-17T09:15:00Z",
    totalOrders: 5,
    totalSpent: 890.0,
    status: "active" as const,
    customerType: "regular",
    tags: ["new-customer"],
  },
  {
    id: "CUST-005",
    name: "David Brown",
    email: "<EMAIL>",
    avatar: "/images/avatar-5.jpg",
    phone: "+****************",
    location: "Seattle, WA",
    joinedAt: "2023-03-18T12:00:00Z",
    lastOrderAt: "2023-12-20T15:30:00Z",
    totalOrders: 3,
    totalSpent: 245.5,
    status: "inactive" as const,
    customerType: "regular",
    tags: ["at-risk"],
  },
  {
    id: "CUST-006",
    name: "Emily Davis",
    email: "<EMAIL>",
    avatar: "/images/avatar-6.jpg",
    phone: "+****************",
    location: "Austin, TX",
    joinedAt: "2024-01-08T15:30:00Z",
    lastOrderAt: "2024-01-19T10:45:00Z",
    totalOrders: 2,
    totalSpent: 156.75,
    status: "active" as const,
    customerType: "regular",
    tags: ["new-customer"],
  },
  {
    id: "CUST-007",
    name: "Robert Garcia",
    email: "<EMAIL>",
    avatar: "/images/avatar-7.jpg",
    phone: "+****************",
    location: "Phoenix, AZ",
    joinedAt: "2023-07-12T11:20:00Z",
    lastOrderAt: "2024-01-16T13:25:00Z",
    totalOrders: 15,
    totalSpent: 3250.8,
    status: "active" as const,
    customerType: "premium",
    tags: ["loyal", "frequent-buyer"],
  },
  {
    id: "CUST-008",
    name: "Lisa Anderson",
    email: "<EMAIL>",
    avatar: "/images/avatar-8.jpg",
    phone: "+****************",
    location: "Denver, CO",
    joinedAt: "2023-09-25T08:45:00Z",
    lastOrderAt: null,
    totalOrders: 0,
    totalSpent: 0,
    status: "inactive" as const,
    customerType: "regular",
    tags: ["never-purchased"],
  },
];
