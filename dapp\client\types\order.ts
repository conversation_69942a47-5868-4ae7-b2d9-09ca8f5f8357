import { CurrencyUnit, OrderStatus, ShippingStatus } from "./common";
import { PaymentMethod, PaymentStatus } from "./payment";

// Re-export types for convenience
export type {
  CurrencyUnit,
  OrderStatus,
  PaymentMethod,
  PaymentStatus,
  ShippingStatus,
};

export type OrderItem = {
  productId: string;
  name: string;
  quantity: number;
  price: number;
  currency: CurrencyUnit;
  image?: string;
  sku?: string;
  variant?: string;
};

export type Customer = {
  customerId?: string;
  name: string;
  email: string;
  phone?: string;
  avatar?: string;
};

export type Address = {
  street: string;
  city: string;
  state?: string;
  postalCode: string;
  country: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
};

export type Shipping = {
  method: string;
  cost: number;
  trackingNumber?: string;
  estimatedDelivery?: string;
  shippedAt?: string;
  deliveredAt?: string;
};

export type Payment = {
  method: PaymentMethod;
  status: PaymentStatus;
  transactionId?: string;
  providerTransactionId?: string;
  cardLast4?: string;
  cardType?: string;
  receiptUrl?: string;
  paidAt?: string;
};

export type Order = {
  _id: string;
  orderNumber: string;
  customer: Customer;
  items: OrderItem[];

  // Pricing
  subtotal: number;
  shippingCost: number;
  tax: number;
  discount: number;
  totalAmount: number;
  currency: CurrencyUnit;

  // Status
  status: OrderStatus;
  paymentStatus: PaymentStatus;
  shippingStatus: ShippingStatus;

  // Addresses
  shippingAddress: Address;
  billingAddress?: Address;

  // Payment & Shipping
  payment: Payment;
  shipping: Shipping;

  // Additional info
  notes?: string;
  internalNotes?: string;
  tags?: string[];

  // Timestamps
  placedAt: string;
  estimatedDelivery?: string;
  createdAt: string;
  updatedAt: string;
};

export type CreateOrderDto = {
  customer: Customer;
  items: OrderItem[];
  shippingAddress: Address;
  billingAddress?: Address;
  shipping: {
    method: string;
    cost: number;
  };
  payment: {
    method: PaymentMethod;
    transactionId?: string;
  };
  notes?: string;
  currency?: CurrencyUnit;
};

export type UpdateOrderDto = {
  status?: OrderStatus;
  paymentStatus?: PaymentStatus;
  shippingStatus?: ShippingStatus;
  shipping?: Partial<Shipping>;
  payment?: Partial<Payment>;
  notes?: string;
  internalNotes?: string;
  tags?: string[];
};

export type OrderFilters = {
  status?: OrderStatus | OrderStatus[];
  paymentStatus?: PaymentStatus | PaymentStatus[];
  shippingStatus?: ShippingStatus | ShippingStatus[];
  customerId?: string;
  customerEmail?: string;
  orderNumber?: string;
  dateFrom?: string;
  dateTo?: string;
  minAmount?: number;
  maxAmount?: number;
  search?: string;
};

export type OrderStats = {
  totalOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
  ordersByStatus: Record<OrderStatus, number>;
  ordersByPaymentStatus: Record<PaymentStatus, number>;
  ordersByShippingStatus: Record<ShippingStatus, number>;
  recentOrders: number;
  pendingOrders: number;
};

export type OrdersResponse = {
  orders: Order[];
  total: number;
  page: number;
  totalPages: number;
};
