[{"C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\analytics\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\appearance\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\customers\\add\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\customers\\list\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\customers\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\customers\\[id]\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\dashboard\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\layout.tsx": "8", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\orders\\list\\page.tsx": "9", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\orders\\page.tsx": "10", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\orders\\[id]\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\products\\add\\page.tsx": "12", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\products\\catalog-settings\\page.tsx": "13", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\products\\list\\page.tsx": "14", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\products\\page.tsx": "15", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\products\\[id]\\page.tsx": "16", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\profile\\page.tsx": "17", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\promotions\\page.tsx": "18", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\reviews\\page.tsx": "19", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\sellers\\page.tsx": "20", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\settings\\page.tsx": "21", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\transactions\\page.tsx": "22", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\transactions\\[id]\\page.tsx": "23", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\api\\uploadthing\\core.ts": "24", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\api\\uploadthing\\delete\\route.ts": "25", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\api\\uploadthing\\route.ts": "26", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\management\\page.tsx": "27", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\page.tsx": "28", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\shop\\layout.tsx": "29", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\shop\\page.tsx": "30", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\test-api\\page.tsx": "31", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\common\\ActionButtons.tsx": "32", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\common\\FormField.tsx": "33", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\common\\ImageDropzone.tsx": "34", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\common\\PageHeaderWrapper.tsx": "35", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\common\\PageTitle.tsx": "36", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\common\\TagInput.tsx": "37", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\debug\\BrandsDebug.tsx": "38", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\debug\\CategoriesDebug.tsx": "39", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\demo\\FormConsistencyDemo.tsx": "40", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\layout\\NavItems.tsx": "41", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\layout\\Sidebar.tsx": "42", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\layout\\Template.tsx": "43", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\layout\\TopNavbar.tsx": "44", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\brands\\BrandManagement.tsx": "45", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\brands\\BrandManagerEnhanced.tsx": "46", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\colors\\ColorManagerEnhanced.tsx": "47", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\customers\\details\\CustomerDetailsWrapper.tsx": "48", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\customers\\details\\index.ts": "49", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\customers\\index.ts": "50", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\customers\\list\\CustomerRow.tsx": "51", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\customers\\list\\CustomersFilter.tsx": "52", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\customers\\list\\CustomersListActions.tsx": "53", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\customers\\list\\CustomersListWrapper.tsx": "54", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\customers\\list\\CustomersPagination.tsx": "55", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\customers\\list\\CustomersTable.tsx": "56", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\customers\\list\\index.ts": "57", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\management\\BrandManager.tsx": "58", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\management\\BrandManagerEnhanced.tsx": "59", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\management\\BrandManagerUI.tsx": "60", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\management\\CategoryManager.tsx": "61", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\management\\CategoryManagerEnhanced.tsx": "62", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\management\\ColorManagerEnhanced.tsx": "63", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\management\\ManagementPage.tsx": "64", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\management\\MaterialManager.tsx": "65", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\management\\MaterialManagerEnhanced.tsx": "66", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\materials\\MaterialManagerEnhanced.tsx": "67", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\details\\CustomerInfoSection.tsx": "68", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\details\\Header.tsx": "69", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\details\\index.ts": "70", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\details\\InfoRow.tsx": "71", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\details\\InfoSection.tsx": "72", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\details\\OrderDetailsWrapper.tsx": "73", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\details\\OrderProductRow.tsx": "74", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\details\\OrderProductsTable.tsx": "75", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\details\\PaymentAndNotesSection.tsx": "76", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\details\\PaymentHeader.tsx": "77", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\details\\SubTotalRow.tsx": "78", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\index.ts": "79", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\list\\index.ts": "80", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\list\\OrderRow.tsx": "81", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\list\\OrdersFilter.tsx": "82", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\list\\OrdersListActions.tsx": "83", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\list\\OrdersListWrapper.tsx": "84", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\list\\OrdersPagination.tsx": "85", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\list\\OrdersTable.tsx": "86", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\AddProductForm.tsx": "87", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\AdvancedSection.tsx": "88", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\AvailabilitySection.tsx": "89", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\BasicInfoSection.tsx": "90", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\DetailsSection.tsx": "91", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\FormNavigation.tsx": "92", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\FormSectionRenderer.tsx": "93", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\FormSections.tsx": "94", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\ImageUploadSection.tsx": "95", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\InventorySection.tsx": "96", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\MediaSection.tsx": "97", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\MetadataGuide.tsx": "98", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\PricingSection.tsx": "99", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\ProductDetails.tsx": "100", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\SectionRender.tsx": "101", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\SEOSection.tsx": "102", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\ShippingSection.tsx": "103", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\TabSections.tsx": "104", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\WarrantySection.tsx": "105", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\details\\index.ts": "106", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\details\\ProductDetailsActions.tsx": "107", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\details\\ProductDetailsContent.tsx": "108", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\details\\ProductDetailsSection.tsx": "109", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\details\\ProductDetailsWrapper.tsx": "110", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\details\\ProductImageSection.tsx": "111", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\details\\ProductInfoSection.tsx": "112", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\details\\ProductInventorySection.tsx": "113", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\details\\ProductPricingSection.tsx": "114", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\details\\ProductStatusSection.tsx": "115", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\index.ts": "116", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\ProductBulkActions.tsx": "117", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\ProductCard.tsx": "118", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\ProductCardSkeleton.tsx": "119", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\ProductFilter.tsx": "120", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\ProductGrid.tsx": "121", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\ProductListActions.tsx": "122", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\ProductListHeader.tsx": "123", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\ProductListWrapper.tsx": "124", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\ProductPagination.tsx": "125", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\promotions\\index.ts": "126", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\promotions\\ProductPromotions.tsx": "127", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\promotions\\PromotionsActions.tsx": "128", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\promotions\\PromotionsStats.tsx": "129", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\promotions\\PromotionsTable.tsx": "130", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\promotions\\PromotionsWrapper.tsx": "131", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\transactions\\details\\TransactionDetailsWrapper.tsx": "132", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\transactions\\index.ts": "133", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\transactions\\list\\TransactionsFilter.tsx": "134", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\transactions\\list\\TransactionsListActions.tsx": "135", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\transactions\\list\\TransactionsListWrapper.tsx": "136", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\transactions\\list\\TransactionsStats.tsx": "137", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\transactions\\list\\TransactionsTable.tsx": "138", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\accordion.tsx": "139", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\alert.tsx": "140", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\avatar.tsx": "141", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\badge.tsx": "142", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\button.tsx": "143", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\calendar.tsx": "144", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\card.tsx": "145", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\checkbox.tsx": "146", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\collapsible-section.tsx": "147", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\confirmation-dialog.tsx": "148", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\dialog.tsx": "149", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\dropdown-menu.tsx": "150", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\emoji-selector.tsx": "151", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\form.tsx": "152", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\important-notice.tsx": "153", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\input.tsx": "154", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\label.tsx": "155", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\page-header.tsx": "156", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\popover.tsx": "157", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\progress.tsx": "158", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\scroll-area.tsx": "159", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\select.tsx": "160", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\separator.tsx": "161", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\skeleton.tsx": "162", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\slider.tsx": "163", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\switch.tsx": "164", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\tabs.tsx": "165", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\textarea.tsx": "166", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\lib\\api\\brands.ts": "167", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\lib\\api\\categoryApi.ts": "168", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\lib\\api\\colors.ts": "169", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\lib\\api\\materials.ts": "170", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\lib\\api\\orders.ts": "171", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\lib\\api\\products.ts": "172", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\lib\\test-api.ts": "173", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\lib\\test-categories-api.ts": "174", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\lib\\uploadthing\\uploadthing.ts": "175", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\lib\\uploadthing\\utils.ts": "176", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\lib\\utils.ts": "177"}, {"size": 14491, "mtime": 1749579422739, "results": "178", "hashOfConfig": "179"}, {"size": 62350, "mtime": 1749580105054, "results": "180", "hashOfConfig": "179"}, {"size": 12904, "mtime": 1749580222308, "results": "181", "hashOfConfig": "179"}, {"size": 657, "mtime": 1749057463902, "results": "182", "hashOfConfig": "179"}, {"size": 172, "mtime": 1749057463902, "results": "183", "hashOfConfig": "179"}, {"size": 617, "mtime": 1749057463901, "results": "184", "hashOfConfig": "179"}, {"size": 15834, "mtime": 1749057463903, "results": "185", "hashOfConfig": "179"}, {"size": 1006, "mtime": 1749057463904, "results": "186", "hashOfConfig": "179"}, {"size": 623, "mtime": 1749057463905, "results": "187", "hashOfConfig": "179"}, {"size": 163, "mtime": 1749057463905, "results": "188", "hashOfConfig": "179"}, {"size": 612, "mtime": 1749407044526, "results": "189", "hashOfConfig": "179"}, {"size": 460, "mtime": 1749057463907, "results": "190", "hashOfConfig": "179"}, {"size": 31057, "mtime": 1749580067342, "results": "191", "hashOfConfig": "179"}, {"size": 2538, "mtime": 1749145046936, "results": "192", "hashOfConfig": "179"}, {"size": 783, "mtime": 1749144661743, "results": "193", "hashOfConfig": "179"}, {"size": 587, "mtime": 1749057463906, "results": "194", "hashOfConfig": "179"}, {"size": 43003, "mtime": 1749580179525, "results": "195", "hashOfConfig": "179"}, {"size": 645, "mtime": 1749057463910, "results": "196", "hashOfConfig": "179"}, {"size": 16222, "mtime": 1749579340342, "results": "197", "hashOfConfig": "179"}, {"size": 8226, "mtime": 1749580245521, "results": "198", "hashOfConfig": "179"}, {"size": 63757, "mtime": 1749057463913, "results": "199", "hashOfConfig": "179"}, {"size": 695, "mtime": 1749057463915, "results": "200", "hashOfConfig": "179"}, {"size": 655, "mtime": 1749057463915, "results": "201", "hashOfConfig": "179"}, {"size": 1475, "mtime": 1749057463916, "results": "202", "hashOfConfig": "179"}, {"size": 553, "mtime": 1749057463917, "results": "203", "hashOfConfig": "179"}, {"size": 285, "mtime": 1749057463917, "results": "204", "hashOfConfig": "179"}, {"size": 166, "mtime": 1749057463918, "results": "205", "hashOfConfig": "179"}, {"size": 157, "mtime": 1749057463918, "results": "206", "hashOfConfig": "179"}, {"size": 1116, "mtime": 1749057463918, "results": "207", "hashOfConfig": "179"}, {"size": 2087, "mtime": 1749057463919, "results": "208", "hashOfConfig": "179"}, {"size": 4876, "mtime": 1749057463919, "results": "209", "hashOfConfig": "179"}, {"size": 1106, "mtime": 1749057463920, "results": "210", "hashOfConfig": "179"}, {"size": 919, "mtime": 1749057463921, "results": "211", "hashOfConfig": "179"}, {"size": 1833, "mtime": 1749057463921, "results": "212", "hashOfConfig": "179"}, {"size": 809, "mtime": 1749057463921, "results": "213", "hashOfConfig": "179"}, {"size": 212, "mtime": 1749057463922, "results": "214", "hashOfConfig": "179"}, {"size": 3744, "mtime": 1749057463922, "results": "215", "hashOfConfig": "179"}, {"size": 3046, "mtime": 1749148187324, "results": "216", "hashOfConfig": "179"}, {"size": 5204, "mtime": 1749577832760, "results": "217", "hashOfConfig": "179"}, {"size": 4830, "mtime": 1749061747715, "results": "218", "hashOfConfig": "179"}, {"size": 3796, "mtime": 1749149149969, "results": "219", "hashOfConfig": "179"}, {"size": 12282, "mtime": 1749057463923, "results": "220", "hashOfConfig": "179"}, {"size": 1807, "mtime": 1749057463923, "results": "221", "hashOfConfig": "179"}, {"size": 18865, "mtime": 1749057463924, "results": "222", "hashOfConfig": "179"}, {"size": 12530, "mtime": 1749232905113, "results": "223", "hashOfConfig": "179"}, {"size": 26414, "mtime": 1749240395097, "results": "224", "hashOfConfig": "179"}, {"size": 27508, "mtime": 1749577803852, "results": "225", "hashOfConfig": "179"}, {"size": 18913, "mtime": 1749057463925, "results": "226", "hashOfConfig": "179"}, {"size": 68, "mtime": 1749057463926, "results": "227", "hashOfConfig": "179"}, {"size": 60, "mtime": 1749057463926, "results": "228", "hashOfConfig": "179"}, {"size": 7347, "mtime": 1749057463927, "results": "229", "hashOfConfig": "179"}, {"size": 6240, "mtime": 1749057463927, "results": "230", "hashOfConfig": "179"}, {"size": 1160, "mtime": 1749057463928, "results": "231", "hashOfConfig": "179"}, {"size": 683, "mtime": 1749057463928, "results": "232", "hashOfConfig": "179"}, {"size": 4100, "mtime": 1749057463928, "results": "233", "hashOfConfig": "179"}, {"size": 1280, "mtime": 1749057463929, "results": "234", "hashOfConfig": "179"}, {"size": 342, "mtime": 1749057463929, "results": "235", "hashOfConfig": "179"}, {"size": 9133, "mtime": 1749577817038, "results": "236", "hashOfConfig": "179"}, {"size": 33636, "mtime": 1749577174691, "results": "237", "hashOfConfig": "179"}, {"size": 12379, "mtime": 1749578279462, "results": "238", "hashOfConfig": "179"}, {"size": 18787, "mtime": 1749578557518, "results": "239", "hashOfConfig": "179"}, {"size": 35997, "mtime": 1749578664578, "results": "240", "hashOfConfig": "179"}, {"size": 23547, "mtime": 1749057463932, "results": "241", "hashOfConfig": "179"}, {"size": 5356, "mtime": 1749057463932, "results": "242", "hashOfConfig": "179"}, {"size": 9303, "mtime": 1749057463933, "results": "243", "hashOfConfig": "179"}, {"size": 25354, "mtime": 1749057463933, "results": "244", "hashOfConfig": "179"}, {"size": 18339, "mtime": 1749149525449, "results": "245", "hashOfConfig": "179"}, {"size": 1791, "mtime": 1749057463934, "results": "246", "hashOfConfig": "179"}, {"size": 1573, "mtime": 1749057463934, "results": "247", "hashOfConfig": "179"}, {"size": 522, "mtime": 1749057463938, "results": "248", "hashOfConfig": "179"}, {"size": 282, "mtime": 1749057463935, "results": "249", "hashOfConfig": "179"}, {"size": 998, "mtime": 1749057463935, "results": "250", "hashOfConfig": "179"}, {"size": 20382, "mtime": 1749406287320, "results": "251", "hashOfConfig": "179"}, {"size": 1090, "mtime": 1749057463936, "results": "252", "hashOfConfig": "179"}, {"size": 2103, "mtime": 1749057463936, "results": "253", "hashOfConfig": "179"}, {"size": 1611, "mtime": 1749057463937, "results": "254", "hashOfConfig": "179"}, {"size": 359, "mtime": 1749057463937, "results": "255", "hashOfConfig": "179"}, {"size": 363, "mtime": 1749057463937, "results": "256", "hashOfConfig": "179"}, {"size": 131, "mtime": 1749057463938, "results": "257", "hashOfConfig": "179"}, {"size": 306, "mtime": 1749057463941, "results": "258", "hashOfConfig": "179"}, {"size": 7257, "mtime": 1749405967616, "results": "259", "hashOfConfig": "179"}, {"size": 3330, "mtime": 1749057463939, "results": "260", "hashOfConfig": "179"}, {"size": 643, "mtime": 1749057463939, "results": "261", "hashOfConfig": "179"}, {"size": 653, "mtime": 1749057463940, "results": "262", "hashOfConfig": "179"}, {"size": 2399, "mtime": 1749057463940, "results": "263", "hashOfConfig": "179"}, {"size": 3900, "mtime": 1749405866454, "results": "264", "hashOfConfig": "179"}, {"size": 31459, "mtime": 1749576179520, "results": "265", "hashOfConfig": "179"}, {"size": 16269, "mtime": 1749576079084, "results": "266", "hashOfConfig": "179"}, {"size": 12406, "mtime": 1749057463948, "results": "267", "hashOfConfig": "179"}, {"size": 21230, "mtime": 1749576111130, "results": "268", "hashOfConfig": "179"}, {"size": 27071, "mtime": 1749059669972, "results": "269", "hashOfConfig": "179"}, {"size": 1637, "mtime": 1749057463949, "results": "270", "hashOfConfig": "179"}, {"size": 4010, "mtime": 1749057463949, "results": "271", "hashOfConfig": "179"}, {"size": 2697, "mtime": 1749057463950, "results": "272", "hashOfConfig": "179"}, {"size": 11356, "mtime": 1749057463950, "results": "273", "hashOfConfig": "179"}, {"size": 20844, "mtime": 1749057463950, "results": "274", "hashOfConfig": "179"}, {"size": 9374, "mtime": 1749057463951, "results": "275", "hashOfConfig": "179"}, {"size": 2616, "mtime": 1749057463951, "results": "276", "hashOfConfig": "179"}, {"size": 21368, "mtime": 1749057463951, "results": "277", "hashOfConfig": "179"}, {"size": 1580, "mtime": 1749057463952, "results": "278", "hashOfConfig": "179"}, {"size": 484, "mtime": 1749057463952, "results": "279", "hashOfConfig": "179"}, {"size": 10667, "mtime": 1749057463952, "results": "280", "hashOfConfig": "179"}, {"size": 14776, "mtime": 1749057463953, "results": "281", "hashOfConfig": "179"}, {"size": 2267, "mtime": 1749057463953, "results": "282", "hashOfConfig": "179"}, {"size": 13332, "mtime": 1749057463954, "results": "283", "hashOfConfig": "179"}, {"size": 586, "mtime": 1749057463958, "results": "284", "hashOfConfig": "179"}, {"size": 2832, "mtime": 1749057463955, "results": "285", "hashOfConfig": "179"}, {"size": 2505, "mtime": 1749057463955, "results": "286", "hashOfConfig": "179"}, {"size": 6993, "mtime": 1749057463956, "results": "287", "hashOfConfig": "179"}, {"size": 5005, "mtime": 1749057463956, "results": "288", "hashOfConfig": "179"}, {"size": 11188, "mtime": 1749057463956, "results": "289", "hashOfConfig": "179"}, {"size": 4734, "mtime": 1749057463957, "results": "290", "hashOfConfig": "179"}, {"size": 5703, "mtime": 1749057463957, "results": "291", "hashOfConfig": "179"}, {"size": 5280, "mtime": 1749057463957, "results": "292", "hashOfConfig": "179"}, {"size": 5413, "mtime": 1749578227867, "results": "293", "hashOfConfig": "179"}, {"size": 406, "mtime": 1749057463958, "results": "294", "hashOfConfig": "179"}, {"size": 3680, "mtime": 1749057463941, "results": "295", "hashOfConfig": "179"}, {"size": 10300, "mtime": 1749577908209, "results": "296", "hashOfConfig": "179"}, {"size": 679, "mtime": 1749057463943, "results": "297", "hashOfConfig": "179"}, {"size": 7437, "mtime": 1749146982961, "results": "298", "hashOfConfig": "179"}, {"size": 2458, "mtime": 1749057463944, "results": "299", "hashOfConfig": "179"}, {"size": 1440, "mtime": 1749057463944, "results": "300", "hashOfConfig": "179"}, {"size": 974, "mtime": 1749057463944, "results": "301", "hashOfConfig": "179"}, {"size": 2260, "mtime": 1749145031644, "results": "302", "hashOfConfig": "179"}, {"size": 2418, "mtime": 1749057463945, "results": "303", "hashOfConfig": "179"}, {"size": 318, "mtime": 1749057463962, "results": "304", "hashOfConfig": "179"}, {"size": 10350, "mtime": 1749057463959, "results": "305", "hashOfConfig": "179"}, {"size": 864, "mtime": 1749057463960, "results": "306", "hashOfConfig": "179"}, {"size": 6649, "mtime": 1749057463960, "results": "307", "hashOfConfig": "179"}, {"size": 9874, "mtime": 1749057463960, "results": "308", "hashOfConfig": "179"}, {"size": 7209, "mtime": 1749576844160, "results": "309", "hashOfConfig": "179"}, {"size": 11530, "mtime": 1749057463963, "results": "310", "hashOfConfig": "179"}, {"size": 379, "mtime": 1749057463963, "results": "311", "hashOfConfig": "179"}, {"size": 5593, "mtime": 1749057463964, "results": "312", "hashOfConfig": "179"}, {"size": 756, "mtime": 1749057463964, "results": "313", "hashOfConfig": "179"}, {"size": 1107, "mtime": 1749057463964, "results": "314", "hashOfConfig": "179"}, {"size": 8475, "mtime": 1749057463965, "results": "315", "hashOfConfig": "179"}, {"size": 8528, "mtime": 1749057463965, "results": "316", "hashOfConfig": "179"}, {"size": 2072, "mtime": 1749057463965, "results": "317", "hashOfConfig": "179"}, {"size": 1657, "mtime": 1749057463966, "results": "318", "hashOfConfig": "179"}, {"size": 1469, "mtime": 1749057463966, "results": "319", "hashOfConfig": "179"}, {"size": 1176, "mtime": 1749057463966, "results": "320", "hashOfConfig": "179"}, {"size": 1959, "mtime": 1749057463966, "results": "321", "hashOfConfig": "179"}, {"size": 3025, "mtime": 1749057463967, "results": "322", "hashOfConfig": "179"}, {"size": 1904, "mtime": 1749057463967, "results": "323", "hashOfConfig": "179"}, {"size": 1056, "mtime": 1749057463968, "results": "324", "hashOfConfig": "179"}, {"size": 2673, "mtime": 1749057463968, "results": "325", "hashOfConfig": "179"}, {"size": 2249, "mtime": 1749057463968, "results": "326", "hashOfConfig": "179"}, {"size": 3994, "mtime": 1749057463968, "results": "327", "hashOfConfig": "179"}, {"size": 7509, "mtime": 1749057463969, "results": "328", "hashOfConfig": "179"}, {"size": 12019, "mtime": 1749063378832, "results": "329", "hashOfConfig": "179"}, {"size": 4310, "mtime": 1749057463969, "results": "330", "hashOfConfig": "179"}, {"size": 2542, "mtime": 1749057463970, "results": "331", "hashOfConfig": "179"}, {"size": 790, "mtime": 1749057463970, "results": "332", "hashOfConfig": "179"}, {"size": 750, "mtime": 1749057463971, "results": "333", "hashOfConfig": "179"}, {"size": 621, "mtime": 1749057463971, "results": "334", "hashOfConfig": "179"}, {"size": 1389, "mtime": 1749057463972, "results": "335", "hashOfConfig": "179"}, {"size": 820, "mtime": 1749057463972, "results": "336", "hashOfConfig": "179"}, {"size": 1656, "mtime": 1749062040643, "results": "337", "hashOfConfig": "179"}, {"size": 5904, "mtime": 1749057463973, "results": "338", "hashOfConfig": "179"}, {"size": 801, "mtime": 1749057463973, "results": "339", "hashOfConfig": "179"}, {"size": 281, "mtime": 1749057463973, "results": "340", "hashOfConfig": "179"}, {"size": 1079, "mtime": 1749057463974, "results": "341", "hashOfConfig": "179"}, {"size": 1191, "mtime": 1749057463974, "results": "342", "hashOfConfig": "179"}, {"size": 1946, "mtime": 1749057463974, "results": "343", "hashOfConfig": "179"}, {"size": 671, "mtime": 1749057463974, "results": "344", "hashOfConfig": "179"}, {"size": 5422, "mtime": 1749576927864, "results": "345", "hashOfConfig": "179"}, {"size": 7336, "mtime": 1749578060032, "results": "346", "hashOfConfig": "179"}, {"size": 6740, "mtime": 1749576945280, "results": "347", "hashOfConfig": "179"}, {"size": 6003, "mtime": 1749576960632, "results": "348", "hashOfConfig": "179"}, {"size": 6159, "mtime": 1749576973447, "results": "349", "hashOfConfig": "179"}, {"size": 5094, "mtime": 1749057463980, "results": "350", "hashOfConfig": "179"}, {"size": 4899, "mtime": 1749057463980, "results": "351", "hashOfConfig": "179"}, {"size": 3684, "mtime": 1749578129826, "results": "352", "hashOfConfig": "179"}, {"size": 306, "mtime": 1749057463981, "results": "353", "hashOfConfig": "179"}, {"size": 610, "mtime": 1749057463981, "results": "354", "hashOfConfig": "179"}, {"size": 172, "mtime": 1749057463982, "results": "355", "hashOfConfig": "179"}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 23, "source": null}, "ze5o7f", {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 40, "fixableErrorCount": 0, "fixableWarningCount": 36, "source": null}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 7, "source": null}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 19, "source": null}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 38, "fixableErrorCount": 0, "fixableWarningCount": 26, "source": null}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 35, "fixableErrorCount": 0, "fixableWarningCount": 31, "source": null}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 19, "source": null}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 19, "source": null}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 40, "fixableErrorCount": 0, "fixableWarningCount": 33, "source": null}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": null}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 33, "fixableErrorCount": 0, "fixableWarningCount": 28, "source": null}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 43, "fixableErrorCount": 0, "fixableWarningCount": 10, "source": null}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 21, "source": null}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 26, "fixableErrorCount": 0, "fixableWarningCount": 20, "source": null}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 31, "fixableErrorCount": 0, "fixableWarningCount": 25, "source": null}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 12, "source": null}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 8, "source": null}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 4, "source": null}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 5, "source": null}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 5, "source": null}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 33, "fixableErrorCount": 0, "fixableWarningCount": 29, "source": null}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 15, "source": null}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 36, "fixableErrorCount": 0, "fixableWarningCount": 14, "source": null}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 41, "fixableErrorCount": 0, "fixableWarningCount": 33, "source": null}, {"filePath": "542", "messages": "543", "suppressedMessages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 25, "fixableErrorCount": 0, "fixableWarningCount": 21, "source": null}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 36, "fixableErrorCount": 0, "fixableWarningCount": 18, "source": null}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 17, "source": null}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "563", "messages": "564", "suppressedMessages": "565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "566", "messages": "567", "suppressedMessages": "568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "569", "messages": "570", "suppressedMessages": "571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 30, "fixableErrorCount": 0, "fixableWarningCount": 25, "source": null}, {"filePath": "575", "messages": "576", "suppressedMessages": "577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "578", "messages": "579", "suppressedMessages": "580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "581", "messages": "582", "suppressedMessages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "584", "messages": "585", "suppressedMessages": "586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "587", "messages": "588", "suppressedMessages": "589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "590", "messages": "591", "suppressedMessages": "592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "593", "messages": "594", "suppressedMessages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 11, "source": null}, {"filePath": "599", "messages": "600", "suppressedMessages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 4, "source": null}, {"filePath": "602", "messages": "603", "suppressedMessages": "604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": null}, {"filePath": "605", "messages": "606", "suppressedMessages": "607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "608", "messages": "609", "suppressedMessages": "610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 6, "source": null}, {"filePath": "611", "messages": "612", "suppressedMessages": "613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": null}, {"filePath": "614", "messages": "615", "suppressedMessages": "616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 42, "fixableErrorCount": 0, "fixableWarningCount": 30, "source": null}, {"filePath": "617", "messages": "618", "suppressedMessages": "619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 17, "source": null}, {"filePath": "620", "messages": "621", "suppressedMessages": "622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 11, "source": null}, {"filePath": "623", "messages": "624", "suppressedMessages": "625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 29, "fixableErrorCount": 0, "fixableWarningCount": 29, "source": null}, {"filePath": "626", "messages": "627", "suppressedMessages": "628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 24, "source": null}, {"filePath": "629", "messages": "630", "suppressedMessages": "631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "632", "messages": "633", "suppressedMessages": "634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "635", "messages": "636", "suppressedMessages": "637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "638", "messages": "639", "suppressedMessages": "640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "641", "messages": "642", "suppressedMessages": "643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 36, "fixableErrorCount": 0, "fixableWarningCount": 33, "source": null}, {"filePath": "644", "messages": "645", "suppressedMessages": "646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 14, "source": null}, {"filePath": "647", "messages": "648", "suppressedMessages": "649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "650", "messages": "651", "suppressedMessages": "652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 7, "source": null}, {"filePath": "653", "messages": "654", "suppressedMessages": "655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "656", "messages": "657", "suppressedMessages": "658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "659", "messages": "660", "suppressedMessages": "661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 8, "source": null}, {"filePath": "662", "messages": "663", "suppressedMessages": "664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 6, "source": null}, {"filePath": "665", "messages": "666", "suppressedMessages": "667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "668", "messages": "669", "suppressedMessages": "670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 15, "source": null}, {"filePath": "671", "messages": "672", "suppressedMessages": "673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "674", "messages": "675", "suppressedMessages": "676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 8, "source": null}, {"filePath": "677", "messages": "678", "suppressedMessages": "679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "680", "messages": "681", "suppressedMessages": "682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 4, "source": null}, {"filePath": "683", "messages": "684", "suppressedMessages": "685", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": null}, {"filePath": "686", "messages": "687", "suppressedMessages": "688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 9, "source": null}, {"filePath": "689", "messages": "690", "suppressedMessages": "691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "692", "messages": "693", "suppressedMessages": "694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 5, "source": null}, {"filePath": "695", "messages": "696", "suppressedMessages": "697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": null}, {"filePath": "698", "messages": "699", "suppressedMessages": "700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "701", "messages": "702", "suppressedMessages": "703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "704", "messages": "705", "suppressedMessages": "706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 4, "source": null}, {"filePath": "707", "messages": "708", "suppressedMessages": "709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 8, "source": null}, {"filePath": "710", "messages": "711", "suppressedMessages": "712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "713", "messages": "714", "suppressedMessages": "715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 7, "source": null}, {"filePath": "716", "messages": "717", "suppressedMessages": "718", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": null}, {"filePath": "719", "messages": "720", "suppressedMessages": "721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 4, "source": null}, {"filePath": "722", "messages": "723", "suppressedMessages": "724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "725", "messages": "726", "suppressedMessages": "727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "728", "messages": "729", "suppressedMessages": "730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 6, "source": null}, {"filePath": "731", "messages": "732", "suppressedMessages": "733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "734", "messages": "735", "suppressedMessages": "736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 6, "source": null}, {"filePath": "737", "messages": "738", "suppressedMessages": "739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 5, "source": null}, {"filePath": "740", "messages": "741", "suppressedMessages": "742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 7, "source": null}, {"filePath": "743", "messages": "744", "suppressedMessages": "745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 14, "source": null}, {"filePath": "746", "messages": "747", "suppressedMessages": "748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 6, "source": null}, {"filePath": "749", "messages": "750", "suppressedMessages": "751", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 16, "source": null}, {"filePath": "752", "messages": "753", "suppressedMessages": "754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "755", "messages": "756", "suppressedMessages": "757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 5, "source": null}, {"filePath": "758", "messages": "759", "suppressedMessages": "760", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 4, "source": null}, {"filePath": "761", "messages": "762", "suppressedMessages": "763", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "764", "messages": "765", "suppressedMessages": "766", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 10, "source": null}, {"filePath": "767", "messages": "768", "suppressedMessages": "769", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 10, "source": null}, {"filePath": "770", "messages": "771", "suppressedMessages": "772", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "773", "messages": "774", "suppressedMessages": "775", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "776", "messages": "777", "suppressedMessages": "778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": null}, {"filePath": "779", "messages": "780", "suppressedMessages": "781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "782", "messages": "783", "suppressedMessages": "784", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "785", "messages": "786", "suppressedMessages": "787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 4, "source": null}, {"filePath": "788", "messages": "789", "suppressedMessages": "790", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "791", "messages": "792", "suppressedMessages": "793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "794", "messages": "795", "suppressedMessages": "796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "797", "messages": "798", "suppressedMessages": "799", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": null}, {"filePath": "800", "messages": "801", "suppressedMessages": "802", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "803", "messages": "804", "suppressedMessages": "805", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 7, "source": null}, {"filePath": "806", "messages": "807", "suppressedMessages": "808", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": null}, {"filePath": "809", "messages": "810", "suppressedMessages": "811", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "812", "messages": "813", "suppressedMessages": "814", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "815", "messages": "816", "suppressedMessages": "817", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "818", "messages": "819", "suppressedMessages": "820", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "821", "messages": "822", "suppressedMessages": "823", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "824", "messages": "825", "suppressedMessages": "826", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "827", "messages": "828", "suppressedMessages": "829", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "830", "messages": "831", "suppressedMessages": "832", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": null}, {"filePath": "833", "messages": "834", "suppressedMessages": "835", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 6, "source": null}, {"filePath": "836", "messages": "837", "suppressedMessages": "838", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "839", "messages": "840", "suppressedMessages": "841", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "842", "messages": "843", "suppressedMessages": "844", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "845", "messages": "846", "suppressedMessages": "847", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "848", "messages": "849", "suppressedMessages": "850", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "851", "messages": "852", "suppressedMessages": "853", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "854", "messages": "855", "suppressedMessages": "856", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "857", "messages": "858", "suppressedMessages": "859", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "860", "messages": "861", "suppressedMessages": "862", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "863", "messages": "864", "suppressedMessages": "865", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "866", "messages": "867", "suppressedMessages": "868", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "869", "messages": "870", "suppressedMessages": "871", "errorCount": 15, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "872", "messages": "873", "suppressedMessages": "874", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "875", "messages": "876", "suppressedMessages": "877", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 28, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "878", "messages": "879", "suppressedMessages": "880", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "881", "messages": "882", "suppressedMessages": "883", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "884", "messages": "885", "suppressedMessages": "886", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\analytics\\page.tsx", ["887", "888", "889", "890", "891", "892", "893", "894", "895", "896", "897", "898", "899", "900", "901", "902", "903", "904", "905", "906", "907", "908", "909", "910"], ["911", "912", "913"], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\appearance\\page.tsx", ["914", "915", "916", "917", "918", "919", "920", "921", "922", "923", "924", "925", "926", "927", "928", "929", "930", "931", "932", "933", "934", "935", "936", "937", "938", "939", "940", "941", "942", "943", "944", "945", "946", "947", "948", "949", "950", "951", "952", "953"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\customers\\add\\page.tsx", ["954", "955", "956", "957", "958", "959", "960", "961"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\customers\\list\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\customers\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\customers\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\dashboard\\page.tsx", ["962", "963", "964", "965", "966", "967", "968", "969", "970", "971", "972", "973", "974", "975", "976", "977", "978", "979", "980"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\orders\\list\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\orders\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\orders\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\products\\add\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\products\\catalog-settings\\page.tsx", ["981", "982", "983", "984", "985", "986", "987", "988", "989", "990", "991", "992", "993", "994", "995", "996", "997", "998", "999", "1000", "1001", "1002", "1003", "1004", "1005", "1006", "1007", "1008", "1009", "1010", "1011", "1012", "1013", "1014", "1015", "1016", "1017", "1018"], ["1019", "1020", "1021", "1022", "1023", "1024"], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\products\\list\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\products\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\products\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\profile\\page.tsx", ["1025", "1026", "1027", "1028", "1029", "1030", "1031", "1032", "1033", "1034", "1035", "1036", "1037", "1038", "1039", "1040", "1041", "1042", "1043", "1044", "1045", "1046", "1047", "1048", "1049", "1050", "1051", "1052", "1053", "1054", "1055", "1056", "1057", "1058", "1059"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\promotions\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\reviews\\page.tsx", ["1060", "1061", "1062", "1063", "1064", "1065", "1066", "1067", "1068", "1069", "1070", "1071", "1072", "1073", "1074", "1075", "1076", "1077", "1078"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\sellers\\page.tsx", ["1079", "1080", "1081", "1082", "1083", "1084", "1085", "1086", "1087", "1088", "1089", "1090", "1091", "1092", "1093", "1094", "1095", "1096", "1097", "1098"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\settings\\page.tsx", ["1099", "1100", "1101", "1102", "1103", "1104", "1105", "1106", "1107", "1108", "1109", "1110", "1111", "1112", "1113", "1114", "1115", "1116", "1117", "1118", "1119", "1120", "1121", "1122", "1123", "1124", "1125", "1126", "1127", "1128", "1129", "1130", "1131", "1132", "1133", "1134", "1135", "1136", "1137", "1138"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\transactions\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\admin\\transactions\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\api\\uploadthing\\core.ts", ["1139", "1140", "1141"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\api\\uploadthing\\delete\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\api\\uploadthing\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\management\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\shop\\layout.tsx", ["1142"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\shop\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\app\\test-api\\page.tsx", ["1143", "1144", "1145", "1146", "1147", "1148"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\common\\ActionButtons.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\common\\FormField.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\common\\ImageDropzone.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\common\\PageHeaderWrapper.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\common\\PageTitle.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\common\\TagInput.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\debug\\BrandsDebug.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\debug\\CategoriesDebug.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\demo\\FormConsistencyDemo.tsx", ["1149"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\layout\\NavItems.tsx", ["1150", "1151"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\layout\\Sidebar.tsx", ["1152", "1153", "1154"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\layout\\Template.tsx", ["1155"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\layout\\TopNavbar.tsx", ["1156", "1157", "1158", "1159", "1160", "1161", "1162", "1163", "1164", "1165", "1166", "1167", "1168", "1169", "1170", "1171", "1172", "1173", "1174", "1175", "1176", "1177", "1178", "1179", "1180", "1181", "1182", "1183", "1184", "1185", "1186", "1187", "1188", "1189", "1190", "1191", "1192", "1193"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\brands\\BrandManagement.tsx", ["1194", "1195", "1196", "1197", "1198", "1199", "1200", "1201", "1202", "1203", "1204", "1205", "1206", "1207", "1208", "1209", "1210", "1211", "1212", "1213", "1214", "1215", "1216", "1217", "1218", "1219", "1220", "1221", "1222", "1223", "1224", "1225", "1226", "1227", "1228", "1229", "1230", "1231", "1232", "1233", "1234", "1235", "1236"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\brands\\BrandManagerEnhanced.tsx", ["1237", "1238", "1239", "1240", "1241", "1242", "1243", "1244", "1245", "1246", "1247", "1248", "1249", "1250", "1251", "1252", "1253", "1254", "1255", "1256", "1257", "1258", "1259", "1260", "1261"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\colors\\ColorManagerEnhanced.tsx", ["1262", "1263", "1264", "1265", "1266", "1267", "1268", "1269", "1270", "1271", "1272", "1273", "1274", "1275", "1276", "1277", "1278", "1279", "1280", "1281", "1282", "1283", "1284", "1285", "1286", "1287", "1288", "1289", "1290", "1291", "1292", "1293", "1294"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\customers\\details\\CustomerDetailsWrapper.tsx", ["1295", "1296", "1297", "1298", "1299", "1300", "1301", "1302", "1303", "1304", "1305", "1306", "1307", "1308", "1309", "1310", "1311", "1312", "1313", "1314", "1315", "1316", "1317", "1318", "1319", "1320", "1321", "1322", "1323", "1324", "1325", "1326"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\customers\\details\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\customers\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\customers\\list\\CustomerRow.tsx", ["1327", "1328", "1329", "1330", "1331", "1332", "1333", "1334", "1335", "1336", "1337", "1338", "1339", "1340"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\customers\\list\\CustomersFilter.tsx", ["1341", "1342", "1343", "1344", "1345", "1346", "1347", "1348"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\customers\\list\\CustomersListActions.tsx", ["1349", "1350", "1351", "1352", "1353", "1354", "1355", "1356", "1357"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\customers\\list\\CustomersListWrapper.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\customers\\list\\CustomersPagination.tsx", ["1358", "1359", "1360", "1361", "1362"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\customers\\list\\CustomersTable.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\customers\\list\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\management\\BrandManager.tsx", ["1363", "1364", "1365", "1366", "1367", "1368"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\management\\BrandManagerEnhanced.tsx", ["1369", "1370", "1371", "1372", "1373", "1374", "1375", "1376", "1377", "1378", "1379", "1380", "1381", "1382", "1383", "1384", "1385", "1386", "1387", "1388", "1389", "1390", "1391", "1392", "1393", "1394", "1395", "1396", "1397", "1398", "1399", "1400", "1401"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\management\\BrandManagerUI.tsx", ["1402", "1403", "1404", "1405", "1406", "1407", "1408", "1409", "1410", "1411", "1412", "1413", "1414", "1415", "1416", "1417", "1418", "1419"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\management\\CategoryManager.tsx", ["1420", "1421", "1422", "1423", "1424", "1425", "1426", "1427", "1428", "1429", "1430", "1431", "1432", "1433", "1434", "1435", "1436", "1437", "1438", "1439", "1440", "1441", "1442", "1443", "1444", "1445", "1446", "1447", "1448", "1449", "1450", "1451", "1452", "1453", "1454", "1455"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\management\\CategoryManagerEnhanced.tsx", ["1456", "1457", "1458", "1459", "1460", "1461", "1462", "1463", "1464", "1465", "1466", "1467", "1468", "1469", "1470", "1471", "1472", "1473", "1474", "1475", "1476", "1477", "1478", "1479", "1480", "1481", "1482", "1483", "1484", "1485", "1486", "1487", "1488", "1489", "1490", "1491", "1492", "1493", "1494", "1495", "1496"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\management\\ColorManagerEnhanced.tsx", ["1497", "1498", "1499", "1500", "1501", "1502", "1503", "1504", "1505", "1506", "1507", "1508", "1509", "1510", "1511", "1512", "1513", "1514", "1515", "1516", "1517", "1518", "1519", "1520", "1521"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\management\\ManagementPage.tsx", ["1522", "1523"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\management\\MaterialManager.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\management\\MaterialManagerEnhanced.tsx", ["1524", "1525", "1526", "1527", "1528", "1529", "1530", "1531", "1532", "1533", "1534", "1535", "1536", "1537", "1538", "1539", "1540", "1541", "1542", "1543", "1544", "1545", "1546", "1547", "1548", "1549", "1550", "1551", "1552", "1553", "1554", "1555", "1556", "1557", "1558", "1559"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\materials\\MaterialManagerEnhanced.tsx", ["1560", "1561", "1562", "1563", "1564", "1565", "1566", "1567", "1568", "1569", "1570", "1571", "1572", "1573", "1574", "1575", "1576", "1577", "1578", "1579", "1580", "1581", "1582", "1583", "1584"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\details\\CustomerInfoSection.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\details\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\details\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\details\\InfoRow.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\details\\InfoSection.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\details\\OrderDetailsWrapper.tsx", ["1585", "1586", "1587", "1588", "1589", "1590", "1591", "1592", "1593", "1594", "1595", "1596", "1597", "1598", "1599", "1600", "1601", "1602", "1603", "1604", "1605", "1606", "1607", "1608", "1609", "1610", "1611", "1612", "1613", "1614"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\details\\OrderProductRow.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\details\\OrderProductsTable.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\details\\PaymentAndNotesSection.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\details\\PaymentHeader.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\details\\SubTotalRow.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\list\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\list\\OrderRow.tsx", ["1615", "1616", "1617", "1618", "1619", "1620", "1621", "1622", "1623", "1624", "1625", "1626", "1627"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\list\\OrdersFilter.tsx", ["1628", "1629", "1630", "1631"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\list\\OrdersListActions.tsx", ["1632", "1633", "1634"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\list\\OrdersListWrapper.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\list\\OrdersPagination.tsx", ["1635", "1636", "1637", "1638", "1639", "1640"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\orders\\list\\OrdersTable.tsx", ["1641", "1642", "1643"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\AddProductForm.tsx", ["1644", "1645", "1646", "1647", "1648", "1649", "1650", "1651", "1652", "1653", "1654", "1655", "1656", "1657", "1658", "1659", "1660", "1661", "1662", "1663", "1664", "1665", "1666", "1667", "1668", "1669", "1670", "1671", "1672", "1673", "1674", "1675", "1676", "1677", "1678", "1679", "1680", "1681", "1682", "1683", "1684", "1685"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\AdvancedSection.tsx", ["1686", "1687", "1688", "1689", "1690", "1691", "1692", "1693", "1694", "1695", "1696", "1697", "1698", "1699", "1700", "1701", "1702", "1703", "1704", "1705", "1706", "1707"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\AvailabilitySection.tsx", ["1708", "1709", "1710", "1711", "1712", "1713", "1714", "1715", "1716", "1717", "1718", "1719", "1720", "1721", "1722", "1723", "1724", "1725", "1726", "1727"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\BasicInfoSection.tsx", ["1728", "1729", "1730", "1731", "1732", "1733", "1734", "1735", "1736", "1737", "1738", "1739", "1740", "1741", "1742", "1743", "1744", "1745", "1746", "1747", "1748", "1749", "1750", "1751", "1752", "1753", "1754", "1755", "1756"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\DetailsSection.tsx", ["1757", "1758", "1759", "1760", "1761", "1762", "1763", "1764", "1765", "1766", "1767", "1768", "1769", "1770", "1771", "1772", "1773", "1774", "1775", "1776", "1777", "1778", "1779", "1780", "1781", "1782", "1783"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\FormNavigation.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\FormSectionRenderer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\FormSections.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\ImageUploadSection.tsx", [], ["1784"], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\InventorySection.tsx", ["1785", "1786", "1787", "1788", "1789", "1790", "1791", "1792", "1793", "1794", "1795", "1796", "1797", "1798", "1799", "1800", "1801", "1802", "1803", "1804", "1805", "1806", "1807", "1808", "1809", "1810", "1811", "1812", "1813", "1814", "1815", "1816", "1817", "1818", "1819", "1820"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\MediaSection.tsx", ["1821", "1822", "1823", "1824", "1825", "1826", "1827", "1828", "1829", "1830", "1831", "1832", "1833", "1834", "1835", "1836", "1837", "1838", "1839"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\MetadataGuide.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\PricingSection.tsx", ["1840", "1841", "1842", "1843", "1844", "1845", "1846"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\ProductDetails.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\SectionRender.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\SEOSection.tsx", ["1847", "1848", "1849", "1850", "1851", "1852", "1853", "1854", "1855", "1856", "1857", "1858", "1859", "1860", "1861", "1862"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\ShippingSection.tsx", ["1863", "1864", "1865", "1866", "1867", "1868", "1869", "1870", "1871", "1872", "1873", "1874", "1875"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\TabSections.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\add\\WarrantySection.tsx", ["1876", "1877", "1878", "1879", "1880", "1881", "1882", "1883", "1884", "1885", "1886", "1887", "1888", "1889", "1890", "1891", "1892", "1893", "1894", "1895", "1896", "1897", "1898", "1899"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\details\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\details\\ProductDetailsActions.tsx", ["1900", "1901", "1902", "1903", "1904", "1905", "1906", "1907", "1908", "1909", "1910", "1911"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\details\\ProductDetailsContent.tsx", ["1912", "1913", "1914", "1915"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\details\\ProductDetailsSection.tsx", ["1916", "1917", "1918", "1919", "1920", "1921", "1922"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\details\\ProductDetailsWrapper.tsx", ["1923", "1924", "1925", "1926", "1927", "1928"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\details\\ProductImageSection.tsx", ["1929", "1930", "1931", "1932", "1933", "1934", "1935", "1936", "1937", "1938", "1939"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\details\\ProductInfoSection.tsx", ["1940", "1941", "1942", "1943", "1944", "1945"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\details\\ProductInventorySection.tsx", ["1946", "1947", "1948", "1949", "1950", "1951"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\details\\ProductPricingSection.tsx", ["1952", "1953", "1954", "1955"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\details\\ProductStatusSection.tsx", ["1956", "1957", "1958", "1959"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\ProductBulkActions.tsx", ["1960", "1961", "1962", "1963"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\ProductCard.tsx", ["1964", "1965", "1966", "1967", "1968", "1969", "1970", "1971"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\ProductCardSkeleton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\ProductFilter.tsx", ["1972", "1973", "1974", "1975", "1976", "1977", "1978", "1979"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\ProductGrid.tsx", ["1980", "1981", "1982", "1983", "1984", "1985"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\ProductListActions.tsx", ["1986", "1987", "1988", "1989", "1990", "1991"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\ProductListHeader.tsx", ["1992", "1993"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\ProductListWrapper.tsx", ["1994"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\products\\ProductPagination.tsx", ["1995", "1996", "1997", "1998", "1999", "2000"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\promotions\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\promotions\\ProductPromotions.tsx", ["2001", "2002", "2003", "2004", "2005", "2006", "2007", "2008", "2009", "2010", "2011"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\promotions\\PromotionsActions.tsx", ["2012", "2013", "2014", "2015", "2016"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\promotions\\PromotionsStats.tsx", ["2017", "2018", "2019", "2020", "2021", "2022", "2023"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\promotions\\PromotionsTable.tsx", ["2024", "2025", "2026", "2027", "2028", "2029", "2030", "2031", "2032", "2033", "2034", "2035", "2036", "2037", "2038"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\promotions\\PromotionsWrapper.tsx", ["2039", "2040", "2041", "2042", "2043", "2044"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\transactions\\details\\TransactionDetailsWrapper.tsx", ["2045", "2046", "2047", "2048", "2049", "2050", "2051", "2052", "2053", "2054", "2055", "2056", "2057", "2058", "2059", "2060", "2061", "2062"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\transactions\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\transactions\\list\\TransactionsFilter.tsx", ["2063", "2064", "2065", "2066", "2067"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\transactions\\list\\TransactionsListActions.tsx", ["2068", "2069", "2070", "2071"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\transactions\\list\\TransactionsListWrapper.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\transactions\\list\\TransactionsStats.tsx", ["2072", "2073", "2074", "2075", "2076", "2077", "2078", "2079", "2080", "2081"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\pages\\transactions\\list\\TransactionsTable.tsx", ["2082", "2083", "2084", "2085", "2086", "2087", "2088", "2089", "2090", "2091", "2092"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\accordion.tsx", ["2093"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\alert.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\avatar.tsx", ["2094", "2095", "2096"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\calendar.tsx", ["2097", "2098", "2099", "2100"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\checkbox.tsx", ["2101", "2102"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\collapsible-section.tsx", ["2103", "2104"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\confirmation-dialog.tsx", ["2105", "2106", "2107"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\dialog.tsx", ["2108", "2109", "2110", "2111", "2112"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\dropdown-menu.tsx", ["2113", "2114", "2115", "2116", "2117", "2118", "2119"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\emoji-selector.tsx", ["2120", "2121", "2122", "2123", "2124"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\important-notice.tsx", ["2125"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\page-header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\popover.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\progress.tsx", ["2126"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\scroll-area.tsx", ["2127", "2128", "2129"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\select.tsx", ["2130", "2131", "2132", "2133", "2134", "2135"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\separator.tsx", ["2136", "2137"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\skeleton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\slider.tsx", ["2138"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\switch.tsx", ["2139"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\tabs.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\lib\\api\\brands.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\lib\\api\\categoryApi.ts", ["2140", "2141", "2142", "2143", "2144", "2145", "2146", "2147"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\lib\\api\\colors.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\lib\\api\\materials.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\lib\\api\\orders.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\lib\\api\\products.ts", ["2148", "2149", "2150", "2151", "2152", "2153", "2154", "2155", "2156", "2157", "2158", "2159", "2160", "2161", "2162", "2163", "2164", "2165", "2166", "2167", "2168", "2169", "2170"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\lib\\test-api.ts", ["2171", "2172", "2173", "2174", "2175", "2176", "2177", "2178", "2179", "2180", "2181", "2182", "2183", "2184", "2185", "2186", "2187", "2188", "2189", "2190", "2191", "2192"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\lib\\test-categories-api.ts", ["2193", "2194", "2195", "2196", "2197", "2198", "2199", "2200", "2201", "2202", "2203", "2204", "2205", "2206", "2207", "2208", "2209", "2210", "2211", "2212", "2213", "2214", "2215", "2216", "2217", "2218", "2219", "2220", "2221"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\lib\\uploadthing\\uploadthing.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\lib\\uploadthing\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\dapp\\client\\lib\\utils.ts", [], [], {"ruleId": "2222", "severity": 1, "message": "2223", "line": 136, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 136, "endColumn": 45, "fix": "2226"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 141, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 141, "endColumn": 47, "fix": "2227"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 155, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 155, "endColumn": 68, "fix": "2228"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 163, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 163, "endColumn": 70, "fix": "2230"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 165, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 165, "endColumn": 70, "fix": "2231"}, {"ruleId": "2222", "severity": 1, "message": "2232", "line": 171, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 171, "endColumn": 64, "fix": "2233"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 181, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 181, "endColumn": 70, "fix": "2234"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 189, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 189, "endColumn": 70, "fix": "2235"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 191, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 191, "endColumn": 70, "fix": "2236"}, {"ruleId": "2222", "severity": 1, "message": "2232", "line": 196, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 196, "endColumn": 63, "fix": "2237"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 206, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 206, "endColumn": 67, "fix": "2238"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 214, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 214, "endColumn": 70, "fix": "2239"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 216, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 216, "endColumn": 70, "fix": "2240"}, {"ruleId": "2222", "severity": 1, "message": "2232", "line": 221, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 221, "endColumn": 64, "fix": "2241"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 231, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 231, "endColumn": 68, "fix": "2242"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 239, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 239, "endColumn": 70, "fix": "2243"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 241, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 241, "endColumn": 70, "fix": "2244"}, {"ruleId": "2222", "severity": 1, "message": "2232", "line": 246, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 246, "endColumn": 64, "fix": "2245"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 258, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 258, "endColumn": 61, "fix": "2247"}, {"ruleId": "2248", "severity": 1, "message": "2249", "line": 266, "column": 48, "nodeType": null, "messageId": "2250", "endLine": 266, "endColumn": 53}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 292, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 292, "endColumn": 52, "fix": "2251"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 304, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 304, "endColumn": 61, "fix": "2252"}, {"ruleId": "2222", "severity": 1, "message": "2253", "line": 312, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 312, "endColumn": 62, "fix": "2254"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 345, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 345, "endColumn": 78, "fix": "2255"}, {"ruleId": "2248", "severity": 1, "message": "2256", "line": 46, "column": 9, "nodeType": null, "messageId": "2250", "endLine": 46, "endColumn": 20, "suppressions": "2257"}, {"ruleId": "2248", "severity": 1, "message": "2258", "line": 55, "column": 9, "nodeType": null, "messageId": "2250", "endLine": 55, "endColumn": 24, "suppressions": "2259"}, {"ruleId": "2248", "severity": 1, "message": "2260", "line": 64, "column": 9, "nodeType": null, "messageId": "2250", "endLine": 64, "endColumn": 20, "suppressions": "2261"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 175, "column": 7, "nodeType": "2264", "messageId": "2265", "endLine": 175, "endColumn": 19, "suggestions": "2266"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 188, "column": 21, "nodeType": "2264", "messageId": "2265", "endLine": 188, "endColumn": 33}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 198, "column": 21, "nodeType": "2264", "messageId": "2265", "endLine": 198, "endColumn": 33}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 234, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 234, "endColumn": 52, "fix": "2267"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 243, "column": 19, "nodeType": "2224", "messageId": "2225", "endLine": 243, "endColumn": 43, "fix": "2268"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 262, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 262, "endColumn": 54, "fix": "2269"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 270, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 270, "endColumn": 53, "fix": "2270"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 278, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 278, "endColumn": 57, "fix": "2271"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 306, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 306, "endColumn": 65, "fix": "2272"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 339, "column": 36, "nodeType": "2224", "messageId": "2225", "endLine": 339, "endColumn": 75, "fix": "2273"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 340, "column": 36, "nodeType": "2224", "messageId": "2225", "endLine": 340, "endColumn": 76, "fix": "2274"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 341, "column": 36, "nodeType": "2224", "messageId": "2225", "endLine": 341, "endColumn": 77, "fix": "2275"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 350, "column": 35, "nodeType": "2224", "messageId": "2225", "endLine": 350, "endColumn": 54, "fix": "2276"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 366, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 366, "endColumn": 66, "fix": "2277"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 445, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 445, "endColumn": 55, "fix": "2278"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 462, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 462, "endColumn": 62, "fix": "2279"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 572, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 572, "endColumn": 65, "fix": "2280"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 641, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 641, "endColumn": 53, "fix": "2281"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 657, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 657, "endColumn": 65, "fix": "2282"}, {"ruleId": "2222", "severity": 1, "message": "2283", "line": 677, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 677, "endColumn": 76, "fix": "2284"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 681, "column": 27, "nodeType": "2264", "messageId": "2265", "endLine": 681, "endColumn": 39, "suggestions": "2285"}, {"ruleId": "2222", "severity": 1, "message": "2283", "line": 704, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 704, "endColumn": 76, "fix": "2286"}, {"ruleId": "2222", "severity": 1, "message": "2283", "line": 727, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 727, "endColumn": 76, "fix": "2287"}, {"ruleId": "2222", "severity": 1, "message": "2283", "line": 748, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 748, "endColumn": 76, "fix": "2288"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 762, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 762, "endColumn": 60, "fix": "2289"}, {"ruleId": "2222", "severity": 1, "message": "2283", "line": 779, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 779, "endColumn": 76, "fix": "2290"}, {"ruleId": "2222", "severity": 1, "message": "2283", "line": 797, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 797, "endColumn": 76, "fix": "2291"}, {"ruleId": "2222", "severity": 1, "message": "2283", "line": 818, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 818, "endColumn": 76, "fix": "2292"}, {"ruleId": "2222", "severity": 1, "message": "2283", "line": 839, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 839, "endColumn": 76, "fix": "2293"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 886, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 886, "endColumn": 53, "fix": "2294"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 902, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 902, "endColumn": 60, "fix": "2295"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 1005, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 1005, "endColumn": 65, "fix": "2296"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 1105, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 1105, "endColumn": 53, "fix": "2297"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 1121, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 1121, "endColumn": 64, "fix": "2298"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 1212, "column": 35, "nodeType": "2224", "messageId": "2225", "endLine": 1212, "endColumn": 59, "fix": "2299"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 1225, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 1225, "endColumn": 64, "fix": "2300"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 1343, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 1343, "endColumn": 53, "fix": "2301"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 1359, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 1359, "endColumn": 66, "fix": "2302"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 1444, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 1444, "endColumn": 65, "fix": "2303"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 1522, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 1522, "endColumn": 53, "fix": "2304"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 75, "column": 7, "nodeType": "2264", "messageId": "2265", "endLine": 75, "endColumn": 19, "suggestions": "2305"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 102, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 102, "endColumn": 43, "fix": "2306"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 112, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 112, "endColumn": 45, "fix": "2307"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 127, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 127, "endColumn": 60, "fix": "2308"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 193, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 193, "endColumn": 63, "fix": "2309"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 255, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 255, "endColumn": 62, "fix": "2310"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 283, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 283, "endColumn": 61, "fix": "2311"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 352, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 352, "endColumn": 51, "fix": "2312"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 92, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 92, "endColumn": 69, "fix": "2313"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 98, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 98, "endColumn": 72, "fix": "2314"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 100, "column": 35, "nodeType": "2224", "messageId": "2225", "endLine": 100, "endColumn": 72, "fix": "2315"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 114, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 114, "endColumn": 46, "fix": "2316"}, {"ruleId": "2222", "severity": 1, "message": "2317", "line": 150, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 150, "endColumn": 102, "fix": "2318"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 151, "column": 35, "nodeType": "2224", "messageId": "2225", "endLine": 151, "endColumn": 69, "fix": "2319"}, {"ruleId": "2222", "severity": 1, "message": "2317", "line": 162, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 162, "endColumn": 101, "fix": "2320"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 163, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 163, "endColumn": 61, "fix": "2321"}, {"ruleId": "2222", "severity": 1, "message": "2317", "line": 176, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 176, "endColumn": 103, "fix": "2322"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 177, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 177, "endColumn": 65, "fix": "2323"}, {"ruleId": "2222", "severity": 1, "message": "2324", "line": 201, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 201, "endColumn": 70, "fix": "2325"}, {"ruleId": "2222", "severity": 1, "message": "2324", "line": 209, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 209, "endColumn": 67, "fix": "2326"}, {"ruleId": "2222", "severity": 1, "message": "2324", "line": 217, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 217, "endColumn": 68, "fix": "2327"}, {"ruleId": "2222", "severity": 1, "message": "2283", "line": 318, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 318, "endColumn": 100, "fix": "2328"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 319, "column": 32, "nodeType": "2224", "messageId": "2225", "endLine": 319, "endColumn": 65, "fix": "2329"}, {"ruleId": "2222", "severity": 1, "message": "2283", "line": 334, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 334, "endColumn": 100, "fix": "2330"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 335, "column": 32, "nodeType": "2224", "messageId": "2225", "endLine": 335, "endColumn": 65, "fix": "2331"}, {"ruleId": "2222", "severity": 1, "message": "2283", "line": 350, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 350, "endColumn": 100, "fix": "2332"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 351, "column": 32, "nodeType": "2224", "messageId": "2225", "endLine": 351, "endColumn": 65, "fix": "2333"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 325, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 325, "endColumn": 52, "fix": "2334"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 330, "column": 19, "nodeType": "2224", "messageId": "2225", "endLine": 330, "endColumn": 43, "fix": "2335"}, {"ruleId": "2222", "severity": 1, "message": "2317", "line": 357, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 357, "endColumn": 64, "fix": "2336"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 371, "column": 32, "nodeType": "2224", "messageId": "2225", "endLine": 371, "endColumn": 65, "fix": "2337"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 385, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 385, "endColumn": 67, "fix": "2338"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 400, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 400, "endColumn": 66, "fix": "2339"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 423, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 423, "endColumn": 63, "fix": "2340"}, {"ruleId": "2222", "severity": 1, "message": "2341", "line": 440, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 440, "endColumn": 138, "fix": "2342"}, {"ruleId": "2343", "severity": 1, "message": "2344", "line": 453, "column": 25, "nodeType": "2224", "messageId": "2345", "endLine": 453, "endColumn": 125}, {"ruleId": "2343", "severity": 1, "message": "2346", "line": 453, "column": 25, "nodeType": "2224", "messageId": "2345", "endLine": 453, "endColumn": 125}, {"ruleId": "2343", "severity": 1, "message": "2347", "line": 453, "column": 25, "nodeType": "2224", "messageId": "2345", "endLine": 453, "endColumn": 125}, {"ruleId": "2343", "severity": 1, "message": "2348", "line": 453, "column": 25, "nodeType": "2224", "messageId": "2345", "endLine": 453, "endColumn": 125}, {"ruleId": "2222", "severity": 1, "message": "2349", "line": 456, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 456, "endColumn": 71, "fix": "2350"}, {"ruleId": "2343", "severity": 1, "message": "2351", "line": 456, "column": 27, "nodeType": "2224", "messageId": "2345", "endLine": 456, "endColumn": 71}, {"ruleId": "2343", "severity": 1, "message": "2352", "line": 456, "column": 27, "nodeType": "2224", "messageId": "2345", "endLine": 456, "endColumn": 71}, {"ruleId": "2222", "severity": 1, "message": "2353", "line": 486, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 486, "endColumn": 132, "fix": "2354"}, {"ruleId": "2343", "severity": 1, "message": "2355", "line": 486, "column": 21, "nodeType": "2224", "messageId": "2345", "endLine": 486, "endColumn": 132}, {"ruleId": "2343", "severity": 1, "message": "2356", "line": 486, "column": 21, "nodeType": "2224", "messageId": "2345", "endLine": 486, "endColumn": 132}, {"ruleId": "2343", "severity": 1, "message": "2357", "line": 486, "column": 21, "nodeType": "2224", "messageId": "2345", "endLine": 486, "endColumn": 132}, {"ruleId": "2343", "severity": 1, "message": "2352", "line": 486, "column": 21, "nodeType": "2224", "messageId": "2345", "endLine": 486, "endColumn": 132}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 497, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 497, "endColumn": 65, "fix": "2358"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 567, "column": 32, "nodeType": "2224", "messageId": "2225", "endLine": 567, "endColumn": 67, "fix": "2359"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 579, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 579, "endColumn": 74, "fix": "2360"}, {"ruleId": "2343", "severity": 1, "message": "2351", "line": 579, "column": 27, "nodeType": "2224", "messageId": "2345", "endLine": 579, "endColumn": 74}, {"ruleId": "2343", "severity": 1, "message": "2352", "line": 579, "column": 27, "nodeType": "2224", "messageId": "2345", "endLine": 579, "endColumn": 74}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 596, "column": 37, "nodeType": "2224", "messageId": "2225", "endLine": 596, "endColumn": 92, "fix": "2361"}, {"ruleId": "2222", "severity": 1, "message": "2317", "line": 623, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 623, "endColumn": 77, "fix": "2362"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 632, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 632, "endColumn": 57, "fix": "2363"}, {"ruleId": "2222", "severity": 1, "message": "2317", "line": 642, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 642, "endColumn": 71, "fix": "2364"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 651, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 651, "endColumn": 57, "fix": "2365"}, {"ruleId": "2222", "severity": 1, "message": "2317", "line": 661, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 661, "endColumn": 75, "fix": "2366"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 670, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 670, "endColumn": 57, "fix": "2367"}, {"ruleId": "2222", "severity": 1, "message": "2317", "line": 680, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 680, "endColumn": 74, "fix": "2368"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 689, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 689, "endColumn": 57, "fix": "2369"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 701, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 701, "endColumn": 64, "fix": "2370"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 722, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 722, "endColumn": 58, "fix": "2371"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 742, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 742, "endColumn": 62, "fix": "2372"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 763, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 763, "endColumn": 61, "fix": "2373"}, {"ruleId": "2248", "severity": 1, "message": "2374", "line": 46, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 46, "endColumn": 21, "suppressions": "2375"}, {"ruleId": "2248", "severity": 1, "message": "2376", "line": 46, "column": 23, "nodeType": null, "messageId": "2250", "endLine": 46, "endColumn": 37, "suppressions": "2377"}, {"ruleId": "2248", "severity": 1, "message": "2378", "line": 58, "column": 20, "nodeType": null, "messageId": "2250", "endLine": 58, "endColumn": 37, "suppressions": "2379"}, {"ruleId": "2248", "severity": 1, "message": "2380", "line": 60, "column": 20, "nodeType": null, "messageId": "2250", "endLine": 60, "endColumn": 33, "suppressions": "2381"}, {"ruleId": "2248", "severity": 1, "message": "2382", "line": 62, "column": 20, "nodeType": null, "messageId": "2250", "endLine": 62, "endColumn": 36, "suppressions": "2383"}, {"ruleId": "2248", "severity": 1, "message": "2384", "line": 64, "column": 20, "nodeType": null, "messageId": "2250", "endLine": 64, "endColumn": 33, "suppressions": "2385"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 122, "column": 7, "nodeType": "2264", "messageId": "2265", "endLine": 122, "endColumn": 19, "suggestions": "2386"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 140, "column": 7, "nodeType": "2264", "messageId": "2265", "endLine": 140, "endColumn": 19, "suggestions": "2387"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 158, "column": 7, "nodeType": "2264", "messageId": "2265", "endLine": 158, "endColumn": 19, "suggestions": "2388"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 171, "column": 7, "nodeType": "2264", "messageId": "2265", "endLine": 171, "endColumn": 19, "suggestions": "2389"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 188, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 188, "endColumn": 45, "fix": "2390"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 192, "column": 19, "nodeType": "2224", "messageId": "2225", "endLine": 192, "endColumn": 43, "fix": "2391"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 215, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 215, "endColumn": 64, "fix": "2392"}, {"ruleId": "2222", "severity": 1, "message": "2253", "line": 220, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 220, "endColumn": 69, "fix": "2393"}, {"ruleId": "2222", "severity": 1, "message": "2317", "line": 230, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 230, "endColumn": 87, "fix": "2394"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 232, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 232, "endColumn": 52, "fix": "2395"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 236, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 236, "endColumn": 55, "fix": "2396"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 280, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 280, "endColumn": 62, "fix": "2397"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 367, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 367, "endColumn": 55, "fix": "2398"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 384, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 384, "endColumn": 60, "fix": "2399"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 414, "column": 35, "nodeType": "2224", "messageId": "2225", "endLine": 414, "endColumn": 54, "fix": "2400"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 416, "column": 32, "nodeType": "2224", "messageId": "2225", "endLine": 416, "endColumn": 51, "fix": "2401"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 445, "column": 35, "nodeType": "2224", "messageId": "2225", "endLine": 445, "endColumn": 54, "fix": "2402"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 447, "column": 32, "nodeType": "2224", "messageId": "2225", "endLine": 447, "endColumn": 51, "fix": "2403"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 480, "column": 35, "nodeType": "2224", "messageId": "2225", "endLine": 480, "endColumn": 54, "fix": "2404"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 482, "column": 32, "nodeType": "2224", "messageId": "2225", "endLine": 482, "endColumn": 51, "fix": "2405"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 489, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 489, "endColumn": 48, "fix": "2406"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 506, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 506, "endColumn": 52, "fix": "2407"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 517, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 517, "endColumn": 61, "fix": "2408"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 593, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 593, "endColumn": 64, "fix": "2409"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 638, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 638, "endColumn": 60, "fix": "2410"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 709, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 709, "endColumn": 62, "fix": "2411"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 781, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 781, "endColumn": 64, "fix": "2412"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 823, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 823, "endColumn": 51, "fix": "2413"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 838, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 838, "endColumn": 65, "fix": "2414"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 857, "column": 34, "nodeType": "2224", "messageId": "2225", "endLine": 857, "endColumn": 53, "fix": "2415"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 863, "column": 35, "nodeType": "2224", "messageId": "2225", "endLine": 863, "endColumn": 54, "fix": "2416"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 869, "column": 39, "nodeType": "2224", "messageId": "2225", "endLine": 869, "endColumn": 58, "fix": "2417"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 939, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 939, "endColumn": 65, "fix": "2418"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 1010, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 1010, "endColumn": 57, "fix": "2419"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 1047, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 1047, "endColumn": 51, "fix": "2420"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 108, "column": 9, "nodeType": "2224", "messageId": "2225", "endLine": 110, "endColumn": 12, "fix": "2421"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 123, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 123, "endColumn": 45, "fix": "2422"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 128, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 128, "endColumn": 47, "fix": "2423"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 141, "column": 34, "nodeType": "2224", "messageId": "2225", "endLine": 141, "endColumn": 67, "fix": "2424"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 159, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 159, "endColumn": 60, "fix": "2425"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 182, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 182, "endColumn": 63, "fix": "2426"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 200, "column": 34, "nodeType": "2224", "messageId": "2225", "endLine": 200, "endColumn": 69, "fix": "2427"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 221, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 221, "endColumn": 101, "fix": "2428"}, {"ruleId": "2222", "severity": 1, "message": "2429", "line": 278, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 278, "endColumn": 54, "fix": "2430"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 323, "column": 45, "nodeType": "2224", "messageId": "2225", "endLine": 323, "endColumn": 64, "fix": "2431"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 328, "column": 34, "nodeType": "2224", "messageId": "2225", "endLine": 328, "endColumn": 58, "fix": "2432"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 332, "column": 35, "nodeType": "2224", "messageId": "2225", "endLine": 332, "endColumn": 59, "fix": "2433"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 336, "column": 38, "nodeType": "2224", "messageId": "2225", "endLine": 336, "endColumn": 62, "fix": "2434"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 341, "column": 42, "nodeType": "2224", "messageId": "2225", "endLine": 341, "endColumn": 66, "fix": "2435"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 345, "column": 38, "nodeType": "2224", "messageId": "2225", "endLine": 345, "endColumn": 62, "fix": "2436"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 368, "column": 38, "nodeType": "2224", "messageId": "2225", "endLine": 368, "endColumn": 76, "fix": "2437"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 386, "column": 42, "nodeType": "2224", "messageId": "2225", "endLine": 386, "endColumn": 66, "fix": "2438"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 390, "column": 37, "nodeType": "2224", "messageId": "2225", "endLine": 390, "endColumn": 61, "fix": "2439"}, {"ruleId": "2222", "severity": 1, "message": "2429", "line": 406, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 406, "endColumn": 78, "fix": "2440"}, {"ruleId": "2222", "severity": 1, "message": "2441", "line": 29, "column": 18, "nodeType": "2224", "messageId": "2225", "endLine": 29, "endColumn": 110, "fix": "2442"}, {"ruleId": "2222", "severity": 1, "message": "2283", "line": 30, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 30, "endColumn": 57, "fix": "2443"}, {"ruleId": "2222", "severity": 1, "message": "2283", "line": 54, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 54, "endColumn": 105, "fix": "2444"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 55, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 55, "endColumn": 60, "fix": "2445"}, {"ruleId": "2222", "severity": 1, "message": "2283", "line": 65, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 65, "endColumn": 104, "fix": "2446"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 66, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 66, "endColumn": 61, "fix": "2447"}, {"ruleId": "2222", "severity": 1, "message": "2283", "line": 76, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 76, "endColumn": 106, "fix": "2448"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 77, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 77, "endColumn": 66, "fix": "2449"}, {"ruleId": "2222", "severity": 1, "message": "2283", "line": 87, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 87, "endColumn": 106, "fix": "2450"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 88, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 88, "endColumn": 65, "fix": "2451"}, {"ruleId": "2222", "severity": 1, "message": "2283", "line": 98, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 98, "endColumn": 103, "fix": "2452"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 99, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 99, "endColumn": 59, "fix": "2453"}, {"ruleId": "2222", "severity": 1, "message": "2283", "line": 109, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 109, "endColumn": 106, "fix": "2454"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 110, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 110, "endColumn": 60, "fix": "2455"}, {"ruleId": "2456", "severity": 2, "message": "2457", "line": 126, "column": 19, "nodeType": "2458", "messageId": "2459", "suggestions": "2460"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 140, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 140, "endColumn": 41, "fix": "2461"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 147, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 147, "endColumn": 64, "fix": "2462"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 160, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 160, "endColumn": 64, "fix": "2463"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 173, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 173, "endColumn": 57, "fix": "2464"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 186, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 186, "endColumn": 57, "fix": "2465"}, {"ruleId": "2248", "severity": 1, "message": "2466", "line": 6, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 6, "endColumn": 12}, {"ruleId": "2248", "severity": 1, "message": "2467", "line": 19, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 19, "endColumn": 8}, {"ruleId": "2248", "severity": 1, "message": "2468", "line": 25, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 25, "endColumn": 15}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 187, "column": 7, "nodeType": "2264", "messageId": "2265", "endLine": 187, "endColumn": 18, "suggestions": "2469"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 200, "column": 21, "nodeType": "2264", "messageId": "2265", "endLine": 200, "endColumn": 32}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 205, "column": 21, "nodeType": "2264", "messageId": "2265", "endLine": 205, "endColumn": 32}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 210, "column": 21, "nodeType": "2264", "messageId": "2265", "endLine": 210, "endColumn": 32}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 229, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 229, "endColumn": 52, "fix": "2470"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 238, "column": 19, "nodeType": "2224", "messageId": "2225", "endLine": 238, "endColumn": 43, "fix": "2471"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 262, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 262, "endColumn": 61, "fix": "2472"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 376, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 376, "endColumn": 62, "fix": "2473"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 498, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 498, "endColumn": 53, "fix": "2474"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 514, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 514, "endColumn": 60, "fix": "2475"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 520, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 520, "endColumn": 46, "fix": "2476"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 625, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 625, "endColumn": 53, "fix": "2477"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 636, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 636, "endColumn": 65, "fix": "2478"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 756, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 756, "endColumn": 53, "fix": "2479"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 772, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 772, "endColumn": 66, "fix": "2480"}, {"ruleId": "2222", "severity": 1, "message": "2317", "line": 781, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 781, "endColumn": 102, "fix": "2481"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 782, "column": 39, "nodeType": "2224", "messageId": "2225", "endLine": 782, "endColumn": 72, "fix": "2482"}, {"ruleId": "2222", "severity": 1, "message": "2317", "line": 840, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 840, "endColumn": 104, "fix": "2483"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 841, "column": 39, "nodeType": "2224", "messageId": "2225", "endLine": 841, "endColumn": 74, "fix": "2484"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 937, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 937, "endColumn": 65, "fix": "2485"}, {"ruleId": "2222", "severity": 1, "message": "2324", "line": 992, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 992, "endColumn": 75, "fix": "2486"}, {"ruleId": "2222", "severity": 1, "message": "2324", "line": 996, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 996, "endColumn": 75, "fix": "2487"}, {"ruleId": "2222", "severity": 1, "message": "2324", "line": 1000, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 1000, "endColumn": 75, "fix": "2488"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 1011, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 1011, "endColumn": 53, "fix": "2489"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 1027, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 1027, "endColumn": 61, "fix": "2490"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 1145, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 1145, "endColumn": 61, "fix": "2491"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 1193, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 1193, "endColumn": 53, "fix": "2492"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 1209, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 1209, "endColumn": 65, "fix": "2493"}, {"ruleId": "2222", "severity": 1, "message": "2283", "line": 1229, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 1229, "endColumn": 61, "fix": "2494"}, {"ruleId": "2222", "severity": 1, "message": "2283", "line": 1252, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 1252, "endColumn": 61, "fix": "2495"}, {"ruleId": "2222", "severity": 1, "message": "2283", "line": 1275, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 1275, "endColumn": 61, "fix": "2496"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 1310, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 1310, "endColumn": 63, "fix": "2497"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 1326, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 1326, "endColumn": 55, "fix": "2498"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 1345, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 1345, "endColumn": 55, "fix": "2499"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 1393, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 1393, "endColumn": 53, "fix": "2500"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 1407, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 1407, "endColumn": 64, "fix": "2501"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 1583, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 1583, "endColumn": 51, "fix": "2502"}, {"ruleId": "2248", "severity": 1, "message": "2503", "line": 6, "column": 15, "nodeType": null, "messageId": "2250", "endLine": 6, "endColumn": 18}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 33, "column": 7, "nodeType": "2264", "messageId": "2265", "endLine": 33, "endColumn": 18, "suggestions": "2504"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 34, "column": 7, "nodeType": "2264", "messageId": "2265", "endLine": 34, "endColumn": 18, "suggestions": "2505"}, {"ruleId": "2222", "severity": 1, "message": "2506", "line": 11, "column": 14, "nodeType": "2224", "messageId": "2225", "endLine": 11, "endColumn": 53, "fix": "2507"}, {"ruleId": "2456", "severity": 2, "message": "2508", "line": 128, "column": 27, "nodeType": "2458", "messageId": "2459", "suggestions": "2509"}, {"ruleId": "2456", "severity": 2, "message": "2508", "line": 128, "column": 47, "nodeType": "2458", "messageId": "2459", "suggestions": "2510"}, {"ruleId": "2456", "severity": 2, "message": "2508", "line": 129, "column": 27, "nodeType": "2458", "messageId": "2459", "suggestions": "2511"}, {"ruleId": "2456", "severity": 2, "message": "2508", "line": 129, "column": 44, "nodeType": "2458", "messageId": "2459", "suggestions": "2512"}, {"ruleId": "2456", "severity": 2, "message": "2508", "line": 130, "column": 27, "nodeType": "2458", "messageId": "2459", "suggestions": "2513"}, {"ruleId": "2456", "severity": 2, "message": "2508", "line": 130, "column": 46, "nodeType": "2458", "messageId": "2459", "suggestions": "2514"}, {"ruleId": "2222", "severity": 1, "message": "2515", "line": 90, "column": 17, "nodeType": "2224", "messageId": "2225", "endLine": 90, "endColumn": 64, "fix": "2516"}, {"ruleId": "2248", "severity": 1, "message": "2517", "line": 8, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 8, "endColumn": 9}, {"ruleId": "2248", "severity": 1, "message": "2518", "line": 17, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 17, "endColumn": 6}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 168, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 168, "endColumn": 95, "fix": "2519"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 228, "column": 41, "nodeType": "2224", "messageId": "2225", "endLine": 228, "endColumn": 60, "fix": "2520"}, {"ruleId": "2521", "severity": 1, "message": "2522", "line": 293, "column": 35, "nodeType": "2224", "messageId": "2523", "endLine": 293, "endColumn": 79, "fix": "2524"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 27, "column": 49, "nodeType": "2527", "messageId": "2528", "endLine": 27, "endColumn": 52, "suggestions": "2529"}, {"ruleId": "2248", "severity": 1, "message": "2530", "line": 70, "column": 9, "nodeType": null, "messageId": "2250", "endLine": 70, "endColumn": 15}, {"ruleId": "2248", "severity": 1, "message": "2249", "line": 103, "column": 31, "nodeType": null, "messageId": "2250", "endLine": 103, "endColumn": 36}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 144, "column": 25, "nodeType": "2264", "messageId": "2265", "endLine": 144, "endColumn": 36}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 154, "column": 25, "nodeType": "2264", "messageId": "2265", "endLine": 154, "endColumn": 36}, {"ruleId": "2531", "severity": 1, "message": "2532", "line": 168, "column": 16, "nodeType": "2533", "messageId": "2534", "endLine": 168, "endColumn": 18}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 192, "column": 45, "nodeType": "2224", "messageId": "2225", "endLine": 192, "endColumn": 69, "fix": "2535"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 201, "column": 46, "nodeType": "2224", "messageId": "2225", "endLine": 201, "endColumn": 65, "fix": "2536"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 212, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 212, "endColumn": 95, "fix": "2537"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 226, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 226, "endColumn": 39, "fix": "2538"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 238, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 238, "endColumn": 46, "fix": "2539"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 240, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 240, "endColumn": 54, "fix": "2540"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 248, "column": 36, "nodeType": "2224", "messageId": "2225", "endLine": 248, "endColumn": 60, "fix": "2541"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 256, "column": 36, "nodeType": "2224", "messageId": "2225", "endLine": 256, "endColumn": 60, "fix": "2542"}, {"ruleId": "2222", "severity": 1, "message": "2543", "line": 274, "column": 17, "nodeType": "2224", "messageId": "2225", "endLine": 274, "endColumn": 40, "fix": "2544"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 277, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 277, "endColumn": 48, "fix": "2545"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 289, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 289, "endColumn": 53, "fix": "2546"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 293, "column": 32, "nodeType": "2224", "messageId": "2225", "endLine": 293, "endColumn": 56, "fix": "2547"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 297, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 297, "endColumn": 50, "fix": "2548"}, {"ruleId": "2456", "severity": 2, "message": "2457", "line": 303, "column": 49, "nodeType": "2458", "messageId": "2459", "suggestions": "2549"}, {"ruleId": "2222", "severity": 1, "message": "2543", "line": 320, "column": 13, "nodeType": "2224", "messageId": "2225", "endLine": 320, "endColumn": 36, "fix": "2550"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 324, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 324, "endColumn": 39, "fix": "2551"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 326, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 326, "endColumn": 40, "fix": "2552"}, {"ruleId": "2222", "severity": 1, "message": "2543", "line": 336, "column": 17, "nodeType": "2224", "messageId": "2225", "endLine": 336, "endColumn": 54, "fix": "2553"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 338, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 338, "endColumn": 42, "fix": "2554"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 340, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 340, "endColumn": 162, "fix": "2555"}, {"ruleId": "2222", "severity": 1, "message": "2324", "line": 371, "column": 34, "nodeType": "2224", "messageId": "2225", "endLine": 371, "endColumn": 78, "fix": "2556"}, {"ruleId": "2222", "severity": 1, "message": "2543", "line": 403, "column": 17, "nodeType": "2224", "messageId": "2225", "endLine": 403, "endColumn": 62, "fix": "2557"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 436, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 436, "endColumn": 51, "fix": "2558"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 446, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 446, "endColumn": 53, "fix": "2559"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 480, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 480, "endColumn": 51, "fix": "2560"}, {"ruleId": "2222", "severity": 1, "message": "2353", "line": 494, "column": 14, "nodeType": "2224", "messageId": "2225", "endLine": 494, "endColumn": 91, "fix": "2561"}, {"ruleId": "2456", "severity": 2, "message": "2508", "line": 497, "column": 34, "nodeType": "2458", "messageId": "2459", "suggestions": "2562"}, {"ruleId": "2456", "severity": 2, "message": "2508", "line": 497, "column": 48, "nodeType": "2458", "messageId": "2459", "suggestions": "2563"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 501, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 501, "endColumn": 59, "fix": "2564"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 508, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 508, "endColumn": 64, "fix": "2565"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 521, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 521, "endColumn": 49, "fix": "2566"}, {"ruleId": "2456", "severity": 2, "message": "2508", "line": 522, "column": 40, "nodeType": "2458", "messageId": "2459", "suggestions": "2567"}, {"ruleId": "2456", "severity": 2, "message": "2508", "line": 522, "column": 54, "nodeType": "2458", "messageId": "2459", "suggestions": "2568"}, {"ruleId": "2248", "severity": 1, "message": "2569", "line": 6, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 6, "endColumn": 10}, {"ruleId": "2248", "severity": 1, "message": "2570", "line": 7, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 7, "endColumn": 8}, {"ruleId": "2248", "severity": 1, "message": "2571", "line": 8, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 8, "endColumn": 7}, {"ruleId": "2248", "severity": 1, "message": "2572", "line": 10, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 10, "endColumn": 15}, {"ruleId": "2248", "severity": 1, "message": "2573", "line": 13, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 13, "endColumn": 10}, {"ruleId": "2248", "severity": 1, "message": "2574", "line": 14, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 14, "endColumn": 7}, {"ruleId": "2248", "severity": 1, "message": "2575", "line": 20, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 20, "endColumn": 4}, {"ruleId": "2248", "severity": 1, "message": "2576", "line": 23, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 23, "endColumn": 15}, {"ruleId": "2248", "severity": 1, "message": "2577", "line": 42, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 42, "endColumn": 15}, {"ruleId": "2248", "severity": 1, "message": "2578", "line": 44, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 44, "endColumn": 9}, {"ruleId": "2248", "severity": 1, "message": "2579", "line": 45, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 45, "endColumn": 16}, {"ruleId": "2248", "severity": 1, "message": "2580", "line": 46, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 46, "endColumn": 13}, {"ruleId": "2248", "severity": 1, "message": "2581", "line": 47, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 47, "endColumn": 16}, {"ruleId": "2248", "severity": 1, "message": "2582", "line": 48, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 48, "endColumn": 14}, {"ruleId": "2248", "severity": 1, "message": "2583", "line": 51, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 51, "endColumn": 16}, {"ruleId": "2248", "severity": 1, "message": "2584", "line": 60, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 60, "endColumn": 18}, {"ruleId": "2248", "severity": 1, "message": "2530", "line": 73, "column": 9, "nodeType": null, "messageId": "2250", "endLine": 73, "endColumn": 15}, {"ruleId": "2248", "severity": 1, "message": "2585", "line": 76, "column": 46, "nodeType": null, "messageId": "2250", "endLine": 76, "endColumn": 53}, {"ruleId": "2248", "severity": 1, "message": "2586", "line": 78, "column": 5, "nodeType": null, "messageId": "2250", "endLine": 78, "endColumn": 16}, {"ruleId": "2248", "severity": 1, "message": "2587", "line": 79, "column": 5, "nodeType": null, "messageId": "2250", "endLine": 79, "endColumn": 16}, {"ruleId": "2248", "severity": 1, "message": "2588", "line": 81, "column": 14, "nodeType": null, "messageId": "2250", "endLine": 81, "endColumn": 29}, {"ruleId": "2248", "severity": 1, "message": "2589", "line": 86, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 86, "endColumn": 18}, {"ruleId": "2248", "severity": 1, "message": "2590", "line": 86, "column": 20, "nodeType": null, "messageId": "2250", "endLine": 86, "endColumn": 31}, {"ruleId": "2248", "severity": 1, "message": "2591", "line": 94, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 94, "endColumn": 24}, {"ruleId": "2248", "severity": 1, "message": "2592", "line": 94, "column": 26, "nodeType": null, "messageId": "2250", "endLine": 94, "endColumn": 43}, {"ruleId": "2248", "severity": 1, "message": "2593", "line": 95, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 95, "endColumn": 18}, {"ruleId": "2248", "severity": 1, "message": "2594", "line": 95, "column": 20, "nodeType": null, "messageId": "2250", "endLine": 95, "endColumn": 31}, {"ruleId": "2248", "severity": 1, "message": "2595", "line": 96, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 96, "endColumn": 21}, {"ruleId": "2248", "severity": 1, "message": "2596", "line": 96, "column": 23, "nodeType": null, "messageId": "2250", "endLine": 96, "endColumn": 37}, {"ruleId": "2248", "severity": 1, "message": "2597", "line": 99, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 99, "endColumn": 18}, {"ruleId": "2248", "severity": 1, "message": "2598", "line": 99, "column": 20, "nodeType": null, "messageId": "2250", "endLine": 99, "endColumn": 31}, {"ruleId": "2248", "severity": 1, "message": "2599", "line": 100, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 100, "endColumn": 16}, {"ruleId": "2248", "severity": 1, "message": "2600", "line": 100, "column": 18, "nodeType": null, "messageId": "2250", "endLine": 100, "endColumn": 27}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 189, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 189, "endColumn": 48, "fix": "2601"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 193, "column": 19, "nodeType": "2224", "messageId": "2225", "endLine": 193, "endColumn": 43, "fix": "2602"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 254, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 254, "endColumn": 109, "fix": "2603"}, {"ruleId": "2521", "severity": 1, "message": "2604", "line": 254, "column": 25, "nodeType": "2224", "messageId": "2605", "endLine": 254, "endColumn": 109, "fix": "2606"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 267, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 267, "endColumn": 47, "fix": "2607"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 320, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 320, "endColumn": 66, "fix": "2608"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 355, "column": 45, "nodeType": "2224", "messageId": "2225", "endLine": 355, "endColumn": 64, "fix": "2609"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 362, "column": 35, "nodeType": "2224", "messageId": "2225", "endLine": 362, "endColumn": 59, "fix": "2610"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 368, "column": 34, "nodeType": "2224", "messageId": "2225", "endLine": 368, "endColumn": 58, "fix": "2611"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 377, "column": 37, "nodeType": "2224", "messageId": "2225", "endLine": 377, "endColumn": 61, "fix": "2612"}, {"ruleId": "2248", "severity": 1, "message": "2613", "line": 12, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 12, "endColumn": 9}, {"ruleId": "2248", "severity": 1, "message": "2614", "line": 13, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 13, "endColumn": 8}, {"ruleId": "2248", "severity": 1, "message": "2615", "line": 19, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 19, "endColumn": 12}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 293, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 293, "endColumn": 56, "fix": "2616"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 307, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 307, "endColumn": 62, "fix": "2617"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 323, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 323, "endColumn": 58, "fix": "2618"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 339, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 339, "endColumn": 61, "fix": "2619"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 359, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 359, "endColumn": 99, "fix": "2620"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 370, "column": 40, "nodeType": "2527", "messageId": "2528", "endLine": 370, "endColumn": 43, "suggestions": "2621"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 399, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 399, "endColumn": 47, "fix": "2622"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 406, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 406, "endColumn": 44, "fix": "2623"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 411, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 411, "endColumn": 47, "fix": "2624"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 511, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 511, "endColumn": 48, "fix": "2625"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 515, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 515, "endColumn": 44, "fix": "2626"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 609, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 609, "endColumn": 55, "fix": "2627"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 617, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 617, "endColumn": 52, "fix": "2628"}, {"ruleId": "2222", "severity": 1, "message": "2317", "line": 633, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 633, "endColumn": 94, "fix": "2629"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 661, "column": 45, "nodeType": "2224", "messageId": "2225", "endLine": 661, "endColumn": 64, "fix": "2630"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 668, "column": 35, "nodeType": "2224", "messageId": "2225", "endLine": 668, "endColumn": 59, "fix": "2631"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 674, "column": 34, "nodeType": "2224", "messageId": "2225", "endLine": 674, "endColumn": 58, "fix": "2632"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 680, "column": 35, "nodeType": "2224", "messageId": "2225", "endLine": 680, "endColumn": 59, "fix": "2633"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 689, "column": 45, "nodeType": "2224", "messageId": "2225", "endLine": 689, "endColumn": 69, "fix": "2634"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 702, "column": 38, "nodeType": "2224", "messageId": "2225", "endLine": 702, "endColumn": 62, "fix": "2635"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 710, "column": 37, "nodeType": "2224", "messageId": "2225", "endLine": 710, "endColumn": 61, "fix": "2636"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 744, "column": 41, "nodeType": "2224", "messageId": "2225", "endLine": 744, "endColumn": 60, "fix": "2637"}, {"ruleId": "2248", "severity": 1, "message": "2569", "line": 6, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 6, "endColumn": 10}, {"ruleId": "2248", "severity": 1, "message": "2613", "line": 11, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 11, "endColumn": 9}, {"ruleId": "2248", "severity": 1, "message": "2638", "line": 17, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 17, "endColumn": 9}, {"ruleId": "2248", "severity": 1, "message": "2639", "line": 21, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 21, "endColumn": 10}, {"ruleId": "2248", "severity": 1, "message": "2640", "line": 22, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 22, "endColumn": 11}, {"ruleId": "2248", "severity": 1, "message": "2530", "line": 74, "column": 9, "nodeType": null, "messageId": "2250", "endLine": 74, "endColumn": 15}, {"ruleId": "2641", "severity": 2, "message": "2642", "line": 127, "column": 7, "nodeType": "2643", "messageId": "2644", "endLine": 127, "endColumn": 8}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 241, "column": 37, "nodeType": "2527", "messageId": "2528", "endLine": 241, "endColumn": 40, "suggestions": "2645"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 328, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 328, "endColumn": 59, "fix": "2646"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 342, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 342, "endColumn": 63, "fix": "2647"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 356, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 356, "endColumn": 56, "fix": "2648"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 370, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 370, "endColumn": 59, "fix": "2649"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 388, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 388, "endColumn": 99, "fix": "2650"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 413, "column": 40, "nodeType": "2527", "messageId": "2528", "endLine": 413, "endColumn": 43, "suggestions": "2651"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 442, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 442, "endColumn": 47, "fix": "2652"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 449, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 449, "endColumn": 44, "fix": "2653"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 454, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 454, "endColumn": 47, "fix": "2654"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 497, "column": 74, "nodeType": "2527", "messageId": "2528", "endLine": 497, "endColumn": 77, "suggestions": "2655"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 509, "column": 74, "nodeType": "2527", "messageId": "2528", "endLine": 509, "endColumn": 77, "suggestions": "2656"}, {"ruleId": "2222", "severity": 1, "message": "2317", "line": 540, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 540, "endColumn": 119, "fix": "2657"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 546, "column": 69, "nodeType": "2527", "messageId": "2528", "endLine": 546, "endColumn": 72, "suggestions": "2658"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 563, "column": 42, "nodeType": "2527", "messageId": "2528", "endLine": 563, "endColumn": 45, "suggestions": "2659"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 612, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 612, "endColumn": 48, "fix": "2660"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 616, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 616, "endColumn": 44, "fix": "2661"}, {"ruleId": "2222", "severity": 1, "message": "2349", "line": 709, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 709, "endColumn": 119, "fix": "2662"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 732, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 732, "endColumn": 55, "fix": "2663"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 743, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 743, "endColumn": 52, "fix": "2664"}, {"ruleId": "2222", "severity": 1, "message": "2429", "line": 754, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 754, "endColumn": 94, "fix": "2665"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 783, "column": 45, "nodeType": "2224", "messageId": "2225", "endLine": 783, "endColumn": 64, "fix": "2666"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 793, "column": 35, "nodeType": "2224", "messageId": "2225", "endLine": 793, "endColumn": 59, "fix": "2667"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 797, "column": 34, "nodeType": "2224", "messageId": "2225", "endLine": 797, "endColumn": 58, "fix": "2668"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 801, "column": 35, "nodeType": "2224", "messageId": "2225", "endLine": 801, "endColumn": 59, "fix": "2669"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 809, "column": 37, "nodeType": "2224", "messageId": "2225", "endLine": 809, "endColumn": 61, "fix": "2670"}, {"ruleId": "2248", "severity": 1, "message": "2671", "line": 8, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 8, "endColumn": 11}, {"ruleId": "2248", "severity": 1, "message": "2672", "line": 14, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 14, "endColumn": 9}, {"ruleId": "2248", "severity": 1, "message": "2673", "line": 17, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 17, "endColumn": 8}, {"ruleId": "2248", "severity": 1, "message": "2674", "line": 38, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 38, "endColumn": 19}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 55, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 55, "endColumn": 16, "suggestions": "2675"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 59, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 59, "endColumn": 16, "suggestions": "2676"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 125, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 125, "endColumn": 43, "fix": "2677"}, {"ruleId": "2222", "severity": 1, "message": "2429", "line": 130, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 130, "endColumn": 42, "fix": "2678"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 163, "column": 19, "nodeType": "2224", "messageId": "2225", "endLine": 163, "endColumn": 38, "fix": "2679"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 173, "column": 19, "nodeType": "2224", "messageId": "2225", "endLine": 173, "endColumn": 38, "fix": "2680"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 180, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 180, "endColumn": 52, "fix": "2681"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 185, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 185, "endColumn": 54, "fix": "2682"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 189, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 189, "endColumn": 51, "fix": "2683"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 194, "column": 32, "nodeType": "2224", "messageId": "2225", "endLine": 194, "endColumn": 56, "fix": "2684"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 198, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 198, "endColumn": 46, "fix": "2685"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 212, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 212, "endColumn": 63, "fix": "2686"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 230, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 230, "endColumn": 63, "fix": "2687"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 246, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 246, "endColumn": 64, "fix": "2688"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 267, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 267, "endColumn": 59, "fix": "2689"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 293, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 293, "endColumn": 56, "fix": "2690"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 349, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 349, "endColumn": 64, "fix": "2691"}, {"ruleId": "2222", "severity": 1, "message": "2283", "line": 362, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 362, "endColumn": 109, "fix": "2692"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 363, "column": 40, "nodeType": "2224", "messageId": "2225", "endLine": 363, "endColumn": 73, "fix": "2693"}, {"ruleId": "2222", "severity": 1, "message": "2429", "line": 417, "column": 32, "nodeType": "2224", "messageId": "2225", "endLine": 417, "endColumn": 80, "fix": "2694"}, {"ruleId": "2456", "severity": 2, "message": "2457", "line": 422, "column": 39, "nodeType": "2458", "messageId": "2459", "suggestions": "2695"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 436, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 436, "endColumn": 61, "fix": "2696"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 485, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 485, "endColumn": 57, "fix": "2697"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 508, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 508, "endColumn": 58, "fix": "2698"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 518, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 518, "endColumn": 47, "fix": "2699"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 527, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 527, "endColumn": 54, "fix": "2700"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 536, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 536, "endColumn": 47, "fix": "2701"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 541, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 541, "endColumn": 51, "fix": "2702"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 56, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 56, "endColumn": 16, "suggestions": "2703"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 64, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 64, "endColumn": 16, "suggestions": "2704"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 121, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 121, "endColumn": 115, "fix": "2705"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 158, "column": 19, "nodeType": "2224", "messageId": "2225", "endLine": 158, "endColumn": 52, "fix": "2706"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 162, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 162, "endColumn": 53, "fix": "2707"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 166, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 166, "endColumn": 54, "fix": "2708"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 181, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 181, "endColumn": 57, "fix": "2709"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 196, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 196, "endColumn": 54, "fix": "2710"}, {"ruleId": "2222", "severity": 1, "message": "2317", "line": 209, "column": 47, "nodeType": "2224", "messageId": "2225", "endLine": 209, "endColumn": 70, "fix": "2711"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 210, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 210, "endColumn": 50, "fix": "2712"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 215, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 215, "endColumn": 44, "fix": "2713"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 219, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 219, "endColumn": 45, "fix": "2714"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 223, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 223, "endColumn": 52, "fix": "2715"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 228, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 228, "endColumn": 45, "fix": "2716"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 39, "column": 19, "nodeType": "2224", "messageId": "2225", "endLine": 39, "endColumn": 93, "fix": "2717"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 50, "column": 19, "nodeType": "2224", "messageId": "2225", "endLine": 50, "endColumn": 43, "fix": "2718"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 53, "column": 40, "nodeType": "2224", "messageId": "2225", "endLine": 53, "endColumn": 89, "fix": "2719"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 118, "column": 18, "nodeType": "2224", "messageId": "2225", "endLine": 118, "endColumn": 42, "fix": "2720"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 135, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 135, "endColumn": 39, "fix": "2721"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 146, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 146, "endColumn": 39, "fix": "2722"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 157, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 157, "endColumn": 39, "fix": "2723"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 168, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 168, "endColumn": 39, "fix": "2724"}, {"ruleId": "2248", "severity": 1, "message": "2725", "line": 5, "column": 20, "nodeType": null, "messageId": "2250", "endLine": 5, "endColumn": 24}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 10, "column": 30, "nodeType": "2264", "messageId": "2265", "endLine": 10, "endColumn": 41}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 11, "column": 30, "nodeType": "2264", "messageId": "2265", "endLine": 11, "endColumn": 41}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 12, "column": 31, "nodeType": "2264", "messageId": "2265", "endLine": 12, "endColumn": 42}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 13, "column": 35, "nodeType": "2264", "messageId": "2265", "endLine": 13, "endColumn": 46}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 18, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 18, "endColumn": 44, "fix": "2726"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 23, "column": 17, "nodeType": "2224", "messageId": "2225", "endLine": 23, "endColumn": 41, "fix": "2727"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 28, "column": 19, "nodeType": "2224", "messageId": "2225", "endLine": 28, "endColumn": 43, "fix": "2728"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 33, "column": 19, "nodeType": "2224", "messageId": "2225", "endLine": 33, "endColumn": 43, "fix": "2729"}, {"ruleId": "2222", "severity": 1, "message": "2317", "line": 105, "column": 13, "nodeType": "2224", "messageId": "2225", "endLine": 105, "endColumn": 36, "fix": "2730"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 107, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 107, "endColumn": 45, "fix": "2731"}, {"ruleId": "2222", "severity": 1, "message": "2317", "line": 119, "column": 19, "nodeType": "2224", "messageId": "2225", "endLine": 119, "endColumn": 42, "fix": "2732"}, {"ruleId": "2222", "severity": 1, "message": "2317", "line": 132, "column": 13, "nodeType": "2224", "messageId": "2225", "endLine": 132, "endColumn": 36, "fix": "2733"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 134, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 134, "endColumn": 46, "fix": "2734"}, {"ruleId": "2248", "severity": 1, "message": "2735", "line": 15, "column": 6, "nodeType": null, "messageId": "2250", "endLine": 15, "endColumn": 19}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 180, "column": 19, "nodeType": "2224", "messageId": "2225", "endLine": 180, "endColumn": 43, "fix": "2736"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 223, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 223, "endColumn": 57, "fix": "2737"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 231, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 231, "endColumn": 54, "fix": "2738"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 260, "column": 35, "nodeType": "2224", "messageId": "2225", "endLine": 260, "endColumn": 54, "fix": "2739"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 268, "column": 35, "nodeType": "2224", "messageId": "2225", "endLine": 268, "endColumn": 54, "fix": "2740"}, {"ruleId": "2248", "severity": 1, "message": "2613", "line": 13, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 13, "endColumn": 9}, {"ruleId": "2248", "severity": 1, "message": "2741", "line": 16, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 16, "endColumn": 8}, {"ruleId": "2248", "severity": 1, "message": "2742", "line": 18, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 18, "endColumn": 7}, {"ruleId": "2248", "severity": 1, "message": "2743", "line": 20, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 20, "endColumn": 10}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 412, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 412, "endColumn": 56, "fix": "2744"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 426, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 426, "endColumn": 62, "fix": "2745"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 442, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 442, "endColumn": 59, "fix": "2746"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 458, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 458, "endColumn": 58, "fix": "2747"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 483, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 483, "endColumn": 99, "fix": "2748"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 538, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 538, "endColumn": 47, "fix": "2749"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 540, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 540, "endColumn": 48, "fix": "2750"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 551, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 551, "endColumn": 47, "fix": "2751"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 559, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 559, "endColumn": 44, "fix": "2752"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 564, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 564, "endColumn": 47, "fix": "2753"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 577, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 577, "endColumn": 55, "fix": "2754"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 708, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 708, "endColumn": 47, "fix": "2755"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 712, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 712, "endColumn": 44, "fix": "2756"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 749, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 749, "endColumn": 55, "fix": "2757"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 757, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 757, "endColumn": 47, "fix": "2758"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 791, "column": 45, "nodeType": "2224", "messageId": "2225", "endLine": 791, "endColumn": 64, "fix": "2759"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 798, "column": 35, "nodeType": "2224", "messageId": "2225", "endLine": 798, "endColumn": 59, "fix": "2760"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 802, "column": 34, "nodeType": "2224", "messageId": "2225", "endLine": 802, "endColumn": 58, "fix": "2761"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 812, "column": 47, "nodeType": "2224", "messageId": "2225", "endLine": 812, "endColumn": 71, "fix": "2762"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 818, "column": 35, "nodeType": "2224", "messageId": "2225", "endLine": 818, "endColumn": 59, "fix": "2763"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 823, "column": 38, "nodeType": "2224", "messageId": "2225", "endLine": 823, "endColumn": 62, "fix": "2764"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 830, "column": 37, "nodeType": "2224", "messageId": "2225", "endLine": 830, "endColumn": 61, "fix": "2765"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 845, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 845, "endColumn": 84, "fix": "2766"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 852, "column": 34, "nodeType": "2224", "messageId": "2225", "endLine": 852, "endColumn": 53, "fix": "2767"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 862, "column": 41, "nodeType": "2224", "messageId": "2225", "endLine": 862, "endColumn": 60, "fix": "2768"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 928, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 928, "endColumn": 52, "fix": "2769"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 935, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 935, "endColumn": 52, "fix": "2770"}, {"ruleId": "2222", "severity": 1, "message": "2429", "line": 950, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 950, "endColumn": 67, "fix": "2771"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 961, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 961, "endColumn": 47, "fix": "2772"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 40, "column": 4, "nodeType": "2527", "messageId": "2528", "endLine": 40, "endColumn": 7, "suggestions": "2773"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 47, "column": 17, "nodeType": "2224", "messageId": "2225", "endLine": 47, "endColumn": 51, "fix": "2774"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 178, "column": 19, "nodeType": "2224", "messageId": "2225", "endLine": 178, "endColumn": 43, "fix": "2775"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 182, "column": 16, "nodeType": "2224", "messageId": "2225", "endLine": 182, "endColumn": 40, "fix": "2776"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 200, "column": 4, "nodeType": "2527", "messageId": "2528", "endLine": 200, "endColumn": 7, "suggestions": "2777"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 203, "column": 44, "nodeType": "2527", "messageId": "2528", "endLine": 203, "endColumn": 47, "suggestions": "2778"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 226, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 226, "endColumn": 51, "fix": "2779"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 234, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 234, "endColumn": 43, "fix": "2780"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 268, "column": 41, "nodeType": "2224", "messageId": "2225", "endLine": 268, "endColumn": 60, "fix": "2781"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 273, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 273, "endColumn": 55, "fix": "2782"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 277, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 277, "endColumn": 54, "fix": "2783"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 287, "column": 43, "nodeType": "2224", "messageId": "2225", "endLine": 287, "endColumn": 67, "fix": "2784"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 293, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 293, "endColumn": 55, "fix": "2785"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 298, "column": 34, "nodeType": "2224", "messageId": "2225", "endLine": 298, "endColumn": 58, "fix": "2786"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 305, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 305, "endColumn": 57, "fix": "2787"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 320, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 320, "endColumn": 80, "fix": "2788"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 327, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 327, "endColumn": 49, "fix": "2789"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 337, "column": 37, "nodeType": "2224", "messageId": "2225", "endLine": 337, "endColumn": 56, "fix": "2790"}, {"ruleId": "2248", "severity": 1, "message": "2569", "line": 6, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 6, "endColumn": 10}, {"ruleId": "2248", "severity": 1, "message": "2571", "line": 7, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 7, "endColumn": 7}, {"ruleId": "2248", "severity": 1, "message": "2791", "line": 8, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 8, "endColumn": 6}, {"ruleId": "2248", "severity": 1, "message": "2613", "line": 9, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 9, "endColumn": 9}, {"ruleId": "2248", "severity": 1, "message": "2792", "line": 10, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 10, "endColumn": 13}, {"ruleId": "2248", "severity": 1, "message": "2793", "line": 14, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 14, "endColumn": 17}, {"ruleId": "2248", "severity": 1, "message": "2518", "line": 23, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 23, "endColumn": 6}, {"ruleId": "2248", "severity": 1, "message": "2468", "line": 30, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 30, "endColumn": 15}, {"ruleId": "2248", "severity": 1, "message": "2794", "line": 32, "column": 29, "nodeType": null, "messageId": "2250", "endLine": 32, "endColumn": 39}, {"ruleId": "2248", "severity": 1, "message": "2795", "line": 32, "column": 41, "nodeType": null, "messageId": "2250", "endLine": 32, "endColumn": 50}, {"ruleId": "2248", "severity": 1, "message": "2796", "line": 34, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 34, "endColumn": 15}, {"ruleId": "2248", "severity": 1, "message": "2797", "line": 35, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 35, "endColumn": 22}, {"ruleId": "2248", "severity": 1, "message": "2798", "line": 36, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 36, "endColumn": 19}, {"ruleId": "2248", "severity": 1, "message": "2799", "line": 37, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 37, "endColumn": 24}, {"ruleId": "2248", "severity": 1, "message": "2800", "line": 38, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 38, "endColumn": 22}, {"ruleId": "2248", "severity": 1, "message": "2577", "line": 41, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 41, "endColumn": 15}, {"ruleId": "2248", "severity": 1, "message": "2583", "line": 49, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 49, "endColumn": 16}, {"ruleId": "2248", "severity": 1, "message": "2584", "line": 50, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 50, "endColumn": 18}, {"ruleId": "2248", "severity": 1, "message": "2801", "line": 58, "column": 7, "nodeType": null, "messageId": "2250", "endLine": 58, "endColumn": 24}, {"ruleId": "2248", "severity": 1, "message": "2595", "line": 177, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 177, "endColumn": 21}, {"ruleId": "2248", "severity": 1, "message": "2802", "line": 188, "column": 9, "nodeType": null, "messageId": "2250", "endLine": 188, "endColumn": 36}, {"ruleId": "2248", "severity": 1, "message": "2803", "line": 219, "column": 9, "nodeType": null, "messageId": "2250", "endLine": 219, "endColumn": 26}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 335, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 335, "endColumn": 62, "fix": "2804"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 349, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 349, "endColumn": 63, "fix": "2805"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 365, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 365, "endColumn": 61, "fix": "2806"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 381, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 381, "endColumn": 62, "fix": "2807"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 406, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 406, "endColumn": 99, "fix": "2808"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 448, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 448, "endColumn": 47, "fix": "2809"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 450, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 450, "endColumn": 48, "fix": "2810"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 461, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 461, "endColumn": 47, "fix": "2811"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 469, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 469, "endColumn": 44, "fix": "2812"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 474, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 474, "endColumn": 47, "fix": "2813"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 522, "column": 35, "nodeType": "2224", "messageId": "2225", "endLine": 522, "endColumn": 59, "fix": "2814"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 530, "column": 32, "nodeType": "2224", "messageId": "2225", "endLine": 530, "endColumn": 56, "fix": "2815"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 552, "column": 37, "nodeType": "2224", "messageId": "2225", "endLine": 552, "endColumn": 56, "fix": "2816"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 560, "column": 37, "nodeType": "2224", "messageId": "2225", "endLine": 560, "endColumn": 56, "fix": "2817"}, {"ruleId": "2248", "severity": 1, "message": "2613", "line": 10, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 10, "endColumn": 9}, {"ruleId": "2248", "severity": 1, "message": "2518", "line": 25, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 25, "endColumn": 6}, {"ruleId": "2248", "severity": 1, "message": "2818", "line": 60, "column": 6, "nodeType": null, "messageId": "2250", "endLine": 60, "endColumn": 22}, {"ruleId": "2248", "severity": 1, "message": "2819", "line": 123, "column": 9, "nodeType": null, "messageId": "2250", "endLine": 123, "endColumn": 21}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 238, "column": 7, "nodeType": "2264", "messageId": "2265", "endLine": 238, "endColumn": 18, "suggestions": "2820"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 239, "column": 7, "nodeType": "2264", "messageId": "2265", "endLine": 239, "endColumn": 18, "suggestions": "2821"}, {"ruleId": "2222", "severity": 1, "message": "2429", "line": 328, "column": 18, "nodeType": "2224", "messageId": "2225", "endLine": 328, "endColumn": 119, "fix": "2822"}, {"ruleId": "2222", "severity": 1, "message": "2429", "line": 347, "column": 18, "nodeType": "2224", "messageId": "2225", "endLine": 347, "endColumn": 75, "fix": "2823"}, {"ruleId": "2222", "severity": 1, "message": "2349", "line": 348, "column": 18, "nodeType": "2224", "messageId": "2225", "endLine": 348, "endColumn": 50, "fix": "2824"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 371, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 371, "endColumn": 62, "fix": "2825"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 385, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 385, "endColumn": 63, "fix": "2826"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 401, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 401, "endColumn": 61, "fix": "2827"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 417, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 417, "endColumn": 62, "fix": "2828"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 442, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 442, "endColumn": 99, "fix": "2829"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 484, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 484, "endColumn": 47, "fix": "2830"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 486, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 486, "endColumn": 48, "fix": "2831"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 497, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 497, "endColumn": 47, "fix": "2832"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 505, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 505, "endColumn": 44, "fix": "2833"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 514, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 514, "endColumn": 52, "fix": "2834"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 519, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 519, "endColumn": 47, "fix": "2835"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 532, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 532, "endColumn": 54, "fix": "2836"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 608, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 608, "endColumn": 47, "fix": "2837"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 616, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 616, "endColumn": 44, "fix": "2838"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 628, "column": 13, "nodeType": "2264", "messageId": "2265", "endLine": 628, "endColumn": 24, "suggestions": "2839"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 700, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 700, "endColumn": 57, "fix": "2840"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 709, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 709, "endColumn": 49, "fix": "2841"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 735, "column": 47, "nodeType": "2224", "messageId": "2225", "endLine": 735, "endColumn": 66, "fix": "2842"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 742, "column": 37, "nodeType": "2224", "messageId": "2225", "endLine": 742, "endColumn": 61, "fix": "2843"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 748, "column": 36, "nodeType": "2224", "messageId": "2225", "endLine": 748, "endColumn": 60, "fix": "2844"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 754, "column": 37, "nodeType": "2224", "messageId": "2225", "endLine": 754, "endColumn": 61, "fix": "2845"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 766, "column": 40, "nodeType": "2224", "messageId": "2225", "endLine": 766, "endColumn": 64, "fix": "2846"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 773, "column": 39, "nodeType": "2224", "messageId": "2225", "endLine": 773, "endColumn": 63, "fix": "2847"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 787, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 787, "endColumn": 59, "fix": "2848"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 804, "column": 17, "nodeType": "2264", "messageId": "2265", "endLine": 804, "endColumn": 28, "suggestions": "2849"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 874, "column": 35, "nodeType": "2224", "messageId": "2225", "endLine": 874, "endColumn": 54, "fix": "2850"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 881, "column": 32, "nodeType": "2224", "messageId": "2225", "endLine": 881, "endColumn": 51, "fix": "2851"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 911, "column": 35, "nodeType": "2224", "messageId": "2225", "endLine": 911, "endColumn": 67, "fix": "2852"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 925, "column": 37, "nodeType": "2224", "messageId": "2225", "endLine": 925, "endColumn": 56, "fix": "2853"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 932, "column": 37, "nodeType": "2224", "messageId": "2225", "endLine": 932, "endColumn": 56, "fix": "2854"}, {"ruleId": "2222", "severity": 1, "message": "2429", "line": 949, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 949, "endColumn": 68, "fix": "2855"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 960, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 960, "endColumn": 47, "fix": "2856"}, {"ruleId": "2248", "severity": 1, "message": "2857", "line": 8, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 8, "endColumn": 14}, {"ruleId": "2248", "severity": 1, "message": "2858", "line": 9, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 9, "endColumn": 12}, {"ruleId": "2248", "severity": 1, "message": "2859", "line": 190, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 190, "endColumn": 24}, {"ruleId": "2248", "severity": 1, "message": "2860", "line": 190, "column": 26, "nodeType": null, "messageId": "2250", "endLine": 190, "endColumn": 43}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 307, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 307, "endColumn": 55, "fix": "2861"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 314, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 314, "endColumn": 66, "fix": "2862"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 325, "column": 18, "nodeType": "2224", "messageId": "2225", "endLine": 325, "endColumn": 52, "fix": "2863"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 342, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 342, "endColumn": 58, "fix": "2864"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 357, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 357, "endColumn": 59, "fix": "2865"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 374, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 374, "endColumn": 95, "fix": "2866"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 384, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 384, "endColumn": 47, "fix": "2867"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 424, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 424, "endColumn": 41, "fix": "2868"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 431, "column": 19, "nodeType": "2224", "messageId": "2225", "endLine": 431, "endColumn": 43, "fix": "2869"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 442, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 442, "endColumn": 40, "fix": "2870"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 540, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 540, "endColumn": 48, "fix": "2871"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 544, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 544, "endColumn": 44, "fix": "2872"}, {"ruleId": "2222", "severity": 1, "message": "2429", "line": 571, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 571, "endColumn": 88, "fix": "2873"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 598, "column": 37, "nodeType": "2224", "messageId": "2225", "endLine": 598, "endColumn": 61, "fix": "2874"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 608, "column": 39, "nodeType": "2224", "messageId": "2225", "endLine": 608, "endColumn": 58, "fix": "2875"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 613, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 613, "endColumn": 53, "fix": "2876"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 619, "column": 35, "nodeType": "2224", "messageId": "2225", "endLine": 619, "endColumn": 59, "fix": "2877"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 624, "column": 32, "nodeType": "2224", "messageId": "2225", "endLine": 624, "endColumn": 56, "fix": "2878"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 631, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 631, "endColumn": 55, "fix": "2879"}, {"ruleId": "2222", "severity": 1, "message": "2429", "line": 658, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 658, "endColumn": 65, "fix": "2880"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 674, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 674, "endColumn": 49, "fix": "2881"}, {"ruleId": "2882", "severity": 1, "message": "2883", "line": 115, "column": 6, "nodeType": "2884", "endLine": 115, "endColumn": 50, "suggestions": "2885"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 151, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 151, "endColumn": 45, "fix": "2886"}, {"ruleId": "2248", "severity": 1, "message": "2613", "line": 11, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 11, "endColumn": 9}, {"ruleId": "2248", "severity": 1, "message": "2743", "line": 16, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 16, "endColumn": 10}, {"ruleId": "2248", "severity": 1, "message": "2638", "line": 17, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 17, "endColumn": 9}, {"ruleId": "2248", "severity": 1, "message": "2887", "line": 20, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 20, "endColumn": 7}, {"ruleId": "2248", "severity": 1, "message": "2888", "line": 25, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 25, "endColumn": 11}, {"ruleId": "2248", "severity": 1, "message": "2518", "line": 26, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 26, "endColumn": 6}, {"ruleId": "2248", "severity": 1, "message": "2889", "line": 28, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 28, "endColumn": 13}, {"ruleId": "2248", "severity": 1, "message": "2575", "line": 29, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 29, "endColumn": 4}, {"ruleId": "2248", "severity": 1, "message": "2794", "line": 35, "column": 29, "nodeType": null, "messageId": "2250", "endLine": 35, "endColumn": 39}, {"ruleId": "2248", "severity": 1, "message": "2795", "line": 35, "column": 41, "nodeType": null, "messageId": "2250", "endLine": 35, "endColumn": 50}, {"ruleId": "2248", "severity": 1, "message": "2577", "line": 44, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 44, "endColumn": 15}, {"ruleId": "2248", "severity": 1, "message": "2583", "line": 52, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 52, "endColumn": 16}, {"ruleId": "2248", "severity": 1, "message": "2584", "line": 53, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 53, "endColumn": 18}, {"ruleId": "2248", "severity": 1, "message": "2600", "line": 240, "column": 18, "nodeType": null, "messageId": "2250", "endLine": 240, "endColumn": 27}, {"ruleId": "2248", "severity": 1, "message": "2595", "line": 243, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 243, "endColumn": 21}, {"ruleId": "2248", "severity": 1, "message": "2890", "line": 302, "column": 9, "nodeType": null, "messageId": "2250", "endLine": 302, "endColumn": 26}, {"ruleId": "2248", "severity": 1, "message": "2891", "line": 368, "column": 9, "nodeType": null, "messageId": "2250", "endLine": 368, "endColumn": 25}, {"ruleId": "2248", "severity": 1, "message": "2892", "line": 374, "column": 9, "nodeType": null, "messageId": "2250", "endLine": 374, "endColumn": 23}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 441, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 441, "endColumn": 60, "fix": "2893"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 455, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 455, "endColumn": 60, "fix": "2894"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 471, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 471, "endColumn": 58, "fix": "2895"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 487, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 487, "endColumn": 58, "fix": "2896"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 507, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 507, "endColumn": 99, "fix": "2897"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 562, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 562, "endColumn": 47, "fix": "2898"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 564, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 564, "endColumn": 48, "fix": "2899"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 575, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 575, "endColumn": 47, "fix": "2900"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 583, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 583, "endColumn": 44, "fix": "2901"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 588, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 588, "endColumn": 47, "fix": "2902"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 647, "column": 41, "nodeType": "2224", "messageId": "2225", "endLine": 647, "endColumn": 60, "fix": "2903"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 654, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 654, "endColumn": 55, "fix": "2904"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 658, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 658, "endColumn": 54, "fix": "2905"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 662, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 662, "endColumn": 55, "fix": "2906"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 667, "column": 34, "nodeType": "2224", "messageId": "2225", "endLine": 667, "endColumn": 58, "fix": "2907"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 674, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 674, "endColumn": 57, "fix": "2908"}, {"ruleId": "2222", "severity": 1, "message": "2429", "line": 727, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 727, "endColumn": 64, "fix": "2909"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 744, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 744, "endColumn": 49, "fix": "2910"}, {"ruleId": "2248", "severity": 1, "message": "2569", "line": 6, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 6, "endColumn": 10}, {"ruleId": "2248", "severity": 1, "message": "2613", "line": 11, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 11, "endColumn": 9}, {"ruleId": "2248", "severity": 1, "message": "2638", "line": 17, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 17, "endColumn": 9}, {"ruleId": "2248", "severity": 1, "message": "2639", "line": 21, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 21, "endColumn": 10}, {"ruleId": "2248", "severity": 1, "message": "2640", "line": 22, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 22, "endColumn": 11}, {"ruleId": "2248", "severity": 1, "message": "2530", "line": 73, "column": 9, "nodeType": null, "messageId": "2250", "endLine": 73, "endColumn": 15}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 214, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 214, "endColumn": 60, "fix": "2911"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 228, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 228, "endColumn": 63, "fix": "2912"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 242, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 242, "endColumn": 56, "fix": "2913"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 256, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 256, "endColumn": 61, "fix": "2914"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 274, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 274, "endColumn": 99, "fix": "2915"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 283, "column": 61, "nodeType": "2527", "messageId": "2528", "endLine": 283, "endColumn": 64, "suggestions": "2916"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 311, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 311, "endColumn": 47, "fix": "2917"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 318, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 318, "endColumn": 44, "fix": "2918"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 323, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 323, "endColumn": 47, "fix": "2919"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 357, "column": 42, "nodeType": "2527", "messageId": "2528", "endLine": 357, "endColumn": 45, "suggestions": "2920"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 398, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 398, "endColumn": 48, "fix": "2921"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 402, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 402, "endColumn": 44, "fix": "2922"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 468, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 468, "endColumn": 55, "fix": "2923"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 479, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 479, "endColumn": 52, "fix": "2924"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 515, "column": 45, "nodeType": "2224", "messageId": "2225", "endLine": 515, "endColumn": 64, "fix": "2925"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 525, "column": 35, "nodeType": "2224", "messageId": "2225", "endLine": 525, "endColumn": 59, "fix": "2926"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 529, "column": 34, "nodeType": "2224", "messageId": "2225", "endLine": 529, "endColumn": 58, "fix": "2927"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 533, "column": 35, "nodeType": "2224", "messageId": "2225", "endLine": 533, "endColumn": 59, "fix": "2928"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 541, "column": 37, "nodeType": "2224", "messageId": "2225", "endLine": 541, "endColumn": 61, "fix": "2929"}, {"ruleId": "2248", "severity": 1, "message": "2930", "line": 9, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 9, "endColumn": 14}, {"ruleId": "2248", "severity": 1, "message": "2931", "line": 10, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 10, "endColumn": 8}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 54, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 54, "endColumn": 16, "suggestions": "2932"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 62, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 62, "endColumn": 16, "suggestions": "2933"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 66, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 66, "endColumn": 16, "suggestions": "2934"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 133, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 133, "endColumn": 43, "fix": "2935"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 190, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 190, "endColumn": 43, "fix": "2936"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 218, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 218, "endColumn": 41, "fix": "2937"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 228, "column": 19, "nodeType": "2224", "messageId": "2225", "endLine": 228, "endColumn": 38, "fix": "2938"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 235, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 235, "endColumn": 52, "fix": "2939"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 240, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 240, "endColumn": 52, "fix": "2940"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 244, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 244, "endColumn": 51, "fix": "2941"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 252, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 252, "endColumn": 54, "fix": "2942"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 266, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 266, "endColumn": 59, "fix": "2943"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 284, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 284, "endColumn": 63, "fix": "2944"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 305, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 305, "endColumn": 59, "fix": "2945"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 335, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 335, "endColumn": 59, "fix": "2946"}, {"ruleId": "2222", "severity": 1, "message": "2232", "line": 346, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 346, "endColumn": 86, "fix": "2947"}, {"ruleId": "2222", "severity": 1, "message": "2948", "line": 356, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 356, "endColumn": 88, "fix": "2949"}, {"ruleId": "2222", "severity": 1, "message": "2349", "line": 357, "column": 36, "nodeType": "2224", "messageId": "2225", "endLine": 357, "endColumn": 69, "fix": "2950"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 439, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 439, "endColumn": 56, "fix": "2951"}, {"ruleId": "2222", "severity": 1, "message": "2283", "line": 445, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 445, "endColumn": 101, "fix": "2952"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 446, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 446, "endColumn": 58, "fix": "2953"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 458, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 458, "endColumn": 58, "fix": "2954"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 465, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 465, "endColumn": 61, "fix": "2955"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 479, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 479, "endColumn": 59, "fix": "2956"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 505, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 505, "endColumn": 64, "fix": "2957"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 512, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 512, "endColumn": 64, "fix": "2958"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 539, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 539, "endColumn": 59, "fix": "2959"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 566, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 566, "endColumn": 60, "fix": "2960"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 76, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 76, "endColumn": 16, "suggestions": "2961"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 81, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 81, "endColumn": 16, "suggestions": "2962"}, {"ruleId": "2222", "severity": 1, "message": "2317", "line": 102, "column": 16, "nodeType": "2224", "messageId": "2225", "endLine": 102, "endColumn": 102, "fix": "2963"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 103, "column": 19, "nodeType": "2224", "messageId": "2225", "endLine": 103, "endColumn": 52, "fix": "2964"}, {"ruleId": "2222", "severity": 1, "message": "2317", "line": 121, "column": 17, "nodeType": "2224", "messageId": "2225", "endLine": 121, "endColumn": 113, "fix": "2965"}, {"ruleId": "2222", "severity": 1, "message": "2948", "line": 132, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 132, "endColumn": 82, "fix": "2966"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 133, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 133, "endColumn": 63, "fix": "2967"}, {"ruleId": "2222", "severity": 1, "message": "2317", "line": 139, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 139, "endColumn": 162, "fix": "2968"}, {"ruleId": "2222", "severity": 1, "message": "2317", "line": 191, "column": 47, "nodeType": "2224", "messageId": "2225", "endLine": 191, "endColumn": 70, "fix": "2969"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 192, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 192, "endColumn": 50, "fix": "2970"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 197, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 197, "endColumn": 44, "fix": "2971"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 201, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 201, "endColumn": 45, "fix": "2972"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 209, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 209, "endColumn": 47, "fix": "2973"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 16, "column": 19, "nodeType": "2224", "messageId": "2225", "endLine": 16, "endColumn": 93, "fix": "2974"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 25, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 25, "endColumn": 47, "fix": "2975"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 29, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 29, "endColumn": 47, "fix": "2976"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 39, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 39, "endColumn": 45, "fix": "2977"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 12, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 12, "endColumn": 44, "fix": "2978"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 17, "column": 19, "nodeType": "2224", "messageId": "2225", "endLine": 17, "endColumn": 43, "fix": "2979"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 22, "column": 15, "nodeType": "2224", "messageId": "2225", "endLine": 22, "endColumn": 39, "fix": "2980"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 39, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 39, "endColumn": 45, "fix": "2981"}, {"ruleId": "2222", "severity": 1, "message": "2317", "line": 45, "column": 49, "nodeType": "2224", "messageId": "2225", "endLine": 45, "endColumn": 72, "fix": "2982"}, {"ruleId": "2222", "severity": 1, "message": "2317", "line": 48, "column": 49, "nodeType": "2224", "messageId": "2225", "endLine": 48, "endColumn": 72, "fix": "2983"}, {"ruleId": "2222", "severity": 1, "message": "2317", "line": 51, "column": 49, "nodeType": "2224", "messageId": "2225", "endLine": 51, "endColumn": 72, "fix": "2984"}, {"ruleId": "2222", "severity": 1, "message": "2317", "line": 55, "column": 49, "nodeType": "2224", "messageId": "2225", "endLine": 55, "endColumn": 72, "fix": "2985"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 62, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 62, "endColumn": 46, "fix": "2986"}, {"ruleId": "2222", "severity": 1, "message": "2317", "line": 52, "column": 35, "nodeType": "2224", "messageId": "2225", "endLine": 52, "endColumn": 67, "fix": "2987"}, {"ruleId": "2222", "severity": 1, "message": "2317", "line": 75, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 75, "endColumn": 60, "fix": "2988"}, {"ruleId": "2222", "severity": 1, "message": "2349", "line": 90, "column": 18, "nodeType": "2224", "messageId": "2225", "endLine": 90, "endColumn": 51, "fix": "2989"}, {"ruleId": "2248", "severity": 1, "message": "2990", "line": 24, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 24, "endColumn": 24}, {"ruleId": "2248", "severity": 1, "message": "2991", "line": 27, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 27, "endColumn": 23}, {"ruleId": "2248", "severity": 1, "message": "2992", "line": 28, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 28, "endColumn": 26}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 108, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 108, "endColumn": 61, "fix": "2993"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 111, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 111, "endColumn": 54, "fix": "2994"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 113, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 113, "endColumn": 58, "fix": "2995"}, {"ruleId": "2222", "severity": 1, "message": "2349", "line": 124, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 124, "endColumn": 59, "fix": "2996"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 143, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 143, "endColumn": 117, "fix": "2997"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 149, "column": 34, "nodeType": "2224", "messageId": "2225", "endLine": 149, "endColumn": 53, "fix": "2998"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 160, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 160, "endColumn": 46, "fix": "2999"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 165, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 165, "endColumn": 47, "fix": "3000"}, {"ruleId": "2248", "severity": 1, "message": "2249", "line": 193, "column": 39, "nodeType": null, "messageId": "2250", "endLine": 193, "endColumn": 44}, {"ruleId": "2222", "severity": 1, "message": "3001", "line": 209, "column": 14, "nodeType": "2224", "messageId": "2225", "endLine": 209, "endColumn": 106, "fix": "3002"}, {"ruleId": "2222", "severity": 1, "message": "2232", "line": 210, "column": 14, "nodeType": "2224", "messageId": "2225", "endLine": 210, "endColumn": 110, "fix": "3003"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 219, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 219, "endColumn": 60, "fix": "3004"}, {"ruleId": "2521", "severity": 1, "message": "2522", "line": 237, "column": 26, "nodeType": "2224", "messageId": "2523", "endLine": 237, "endColumn": 51, "fix": "3005"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 258, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 258, "endColumn": 75, "fix": "3006"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 288, "column": 36, "nodeType": "2224", "messageId": "2225", "endLine": 288, "endColumn": 60, "fix": "3007"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 337, "column": 38, "nodeType": "2224", "messageId": "2225", "endLine": 337, "endColumn": 77, "fix": "3008"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 369, "column": 38, "nodeType": "2224", "messageId": "2225", "endLine": 369, "endColumn": 77, "fix": "3009"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 402, "column": 38, "nodeType": "2224", "messageId": "2225", "endLine": 402, "endColumn": 77, "fix": "3010"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 435, "column": 38, "nodeType": "2224", "messageId": "2225", "endLine": 435, "endColumn": 77, "fix": "3011"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 475, "column": 38, "nodeType": "2224", "messageId": "2225", "endLine": 475, "endColumn": 77, "fix": "3012"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 515, "column": 38, "nodeType": "2224", "messageId": "2225", "endLine": 515, "endColumn": 77, "fix": "3013"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 545, "column": 36, "nodeType": "2224", "messageId": "2225", "endLine": 545, "endColumn": 75, "fix": "3014"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 576, "column": 36, "nodeType": "2224", "messageId": "2225", "endLine": 576, "endColumn": 55, "fix": "3015"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 589, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 589, "endColumn": 53, "fix": "3016"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 598, "column": 27, "nodeType": "2264", "messageId": "2265", "endLine": 598, "endColumn": 38, "suggestions": "3017"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 602, "column": 27, "nodeType": "2264", "messageId": "2265", "endLine": 602, "endColumn": 38, "suggestions": "3018"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 603, "column": 27, "nodeType": "2264", "messageId": "2265", "endLine": 603, "endColumn": 38, "suggestions": "3019"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 607, "column": 27, "nodeType": "2264", "messageId": "2265", "endLine": 607, "endColumn": 38, "suggestions": "3020"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 625, "column": 34, "nodeType": "2224", "messageId": "2225", "endLine": 625, "endColumn": 127, "fix": "3021"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 630, "column": 38, "nodeType": "2224", "messageId": "2225", "endLine": 630, "endColumn": 62, "fix": "3022"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 639, "column": 27, "nodeType": "2264", "messageId": "2265", "endLine": 639, "endColumn": 38, "suggestions": "3023"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 643, "column": 27, "nodeType": "2264", "messageId": "2265", "endLine": 643, "endColumn": 38, "suggestions": "3024"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 644, "column": 27, "nodeType": "2264", "messageId": "2265", "endLine": 644, "endColumn": 38, "suggestions": "3025"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 648, "column": 27, "nodeType": "2264", "messageId": "2265", "endLine": 648, "endColumn": 38, "suggestions": "3026"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 660, "column": 36, "nodeType": "2224", "messageId": "2225", "endLine": 660, "endColumn": 71, "fix": "3027"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 676, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 676, "endColumn": 44, "fix": "3028"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 684, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 684, "endColumn": 62, "fix": "3029"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 698, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 698, "endColumn": 58, "fix": "3030"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 710, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 710, "endColumn": 58, "fix": "3031"}, {"ruleId": "2248", "severity": 1, "message": "2614", "line": 9, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 9, "endColumn": 8}, {"ruleId": "2248", "severity": 1, "message": "3032", "line": 14, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 14, "endColumn": 7}, {"ruleId": "2248", "severity": 1, "message": "2467", "line": 16, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 16, "endColumn": 8}, {"ruleId": "2248", "severity": 1, "message": "3033", "line": 28, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 28, "endColumn": 15}, {"ruleId": "2248", "severity": 1, "message": "3034", "line": 68, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 68, "endColumn": 9}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 82, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 82, "endColumn": 54, "fix": "3035"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 98, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 98, "endColumn": 54, "fix": "3036"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 126, "column": 44, "nodeType": "2224", "messageId": "2225", "endLine": 126, "endColumn": 63, "fix": "3037"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 141, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 141, "endColumn": 67, "fix": "3038"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 171, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 171, "endColumn": 56, "fix": "3039"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 203, "column": 43, "nodeType": "2224", "messageId": "2225", "endLine": 203, "endColumn": 62, "fix": "3040"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 216, "column": 34, "nodeType": "2224", "messageId": "2225", "endLine": 216, "endColumn": 76, "fix": "3041"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 235, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 235, "endColumn": 56, "fix": "3042"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 275, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 275, "endColumn": 66, "fix": "3043"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 296, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 296, "endColumn": 55, "fix": "3044"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 340, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 340, "endColumn": 56, "fix": "3045"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 355, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 355, "endColumn": 62, "fix": "3046"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 363, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 363, "endColumn": 59, "fix": "3047"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 379, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 379, "endColumn": 42, "fix": "3048"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 387, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 387, "endColumn": 58, "fix": "3049"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 397, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 397, "endColumn": 60, "fix": "3050"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 410, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 410, "endColumn": 60, "fix": "3051"}, {"ruleId": "2248", "severity": 1, "message": "2791", "line": 10, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 10, "endColumn": 6}, {"ruleId": "2248", "severity": 1, "message": "3052", "line": 13, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 13, "endColumn": 7}, {"ruleId": "2248", "severity": 1, "message": "2467", "line": 15, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 15, "endColumn": 8}, {"ruleId": "2248", "severity": 1, "message": "2578", "line": 36, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 36, "endColumn": 9}, {"ruleId": "2248", "severity": 1, "message": "2579", "line": 37, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 37, "endColumn": 16}, {"ruleId": "2248", "severity": 1, "message": "2580", "line": 38, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 38, "endColumn": 13}, {"ruleId": "2248", "severity": 1, "message": "2581", "line": 39, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 39, "endColumn": 16}, {"ruleId": "2248", "severity": 1, "message": "2582", "line": 40, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 40, "endColumn": 14}, {"ruleId": "2248", "severity": 1, "message": "3053", "line": 60, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 60, "endColumn": 11}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 96, "column": 18, "nodeType": "2224", "messageId": "2225", "endLine": 96, "endColumn": 52, "fix": "3054"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 112, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 112, "endColumn": 60, "fix": "3055"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 150, "column": 19, "nodeType": "2224", "messageId": "2225", "endLine": 158, "endColumn": 22, "fix": "3056"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 189, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 189, "endColumn": 64, "fix": "3057"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 211, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 211, "endColumn": 57, "fix": "3058"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 250, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 250, "endColumn": 57, "fix": "3059"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 278, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 278, "endColumn": 62, "fix": "3060"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 292, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 292, "endColumn": 39, "fix": "3061"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 300, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 300, "endColumn": 64, "fix": "3062"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 312, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 312, "endColumn": 66, "fix": "3063"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 326, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 326, "endColumn": 57, "fix": "3064"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 75, "column": 17, "nodeType": "2224", "messageId": "2225", "endLine": 75, "endColumn": 50, "fix": "3065"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 104, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 104, "endColumn": 55, "fix": "3066"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 131, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 131, "endColumn": 65, "fix": "3067"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 145, "column": 18, "nodeType": "2224", "messageId": "2225", "endLine": 145, "endColumn": 52, "fix": "3068"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 178, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 178, "endColumn": 49, "fix": "3069"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 186, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 186, "endColumn": 50, "fix": "3070"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 220, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 220, "endColumn": 55, "fix": "3071"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 235, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 235, "endColumn": 56, "fix": "3072"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 287, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 287, "endColumn": 49, "fix": "3073"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 293, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 293, "endColumn": 46, "fix": "3074"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 299, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 299, "endColumn": 50, "fix": "3075"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 305, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 305, "endColumn": 48, "fix": "3076"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 311, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 311, "endColumn": 49, "fix": "3077"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 323, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 323, "endColumn": 67, "fix": "3078"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 351, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 351, "endColumn": 65, "fix": "3079"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 365, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 365, "endColumn": 58, "fix": "3080"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 394, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 394, "endColumn": 67, "fix": "3081"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 409, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 409, "endColumn": 53, "fix": "3082"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 425, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 425, "endColumn": 45, "fix": "3083"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 431, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 431, "endColumn": 46, "fix": "3084"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 437, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 437, "endColumn": 46, "fix": "3085"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 443, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 443, "endColumn": 44, "fix": "3086"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 456, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 456, "endColumn": 64, "fix": "3087"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 459, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 459, "endColumn": 65, "fix": "3088"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 462, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 462, "endColumn": 65, "fix": "3089"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 465, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 465, "endColumn": 63, "fix": "3090"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 495, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 495, "endColumn": 59, "fix": "3091"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 514, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 514, "endColumn": 59, "fix": "3092"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 531, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 531, "endColumn": 64, "fix": "3093"}, {"ruleId": "2248", "severity": 1, "message": "3094", "line": 4, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 4, "endColumn": 8}, {"ruleId": "2248", "severity": 1, "message": "2573", "line": 7, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 7, "endColumn": 10}, {"ruleId": "2248", "severity": 1, "message": "3095", "line": 24, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 24, "endColumn": 18}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 133, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 133, "endColumn": 55, "fix": "3096"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 155, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 155, "endColumn": 54, "fix": "3097"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 208, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 208, "endColumn": 50, "fix": "3098"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 226, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 226, "endColumn": 69, "fix": "3099"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 246, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 246, "endColumn": 56, "fix": "3100"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 279, "column": 34, "nodeType": "2224", "messageId": "2225", "endLine": 279, "endColumn": 53, "fix": "3101"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 287, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 287, "endColumn": 50, "fix": "3102"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 305, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 305, "endColumn": 70, "fix": "3103"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 325, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 325, "endColumn": 55, "fix": "3104"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 353, "column": 32, "nodeType": "2224", "messageId": "2225", "endLine": 353, "endColumn": 51, "fix": "3105"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 364, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 364, "endColumn": 79, "fix": "3106"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 377, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 377, "endColumn": 50, "fix": "3107"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 387, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 387, "endColumn": 69, "fix": "3108"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 407, "column": 18, "nodeType": "2224", "messageId": "2225", "endLine": 407, "endColumn": 53, "fix": "3109"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 433, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 433, "endColumn": 62, "fix": "3110"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 445, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 445, "endColumn": 67, "fix": "3111"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 460, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 460, "endColumn": 55, "fix": "3112"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 563, "column": 38, "nodeType": "2224", "messageId": "2225", "endLine": 563, "endColumn": 57, "fix": "3113"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 575, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 575, "endColumn": 69, "fix": "3114"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 592, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 592, "endColumn": 54, "fix": "3115"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 656, "column": 39, "nodeType": "2224", "messageId": "2225", "endLine": 656, "endColumn": 58, "fix": "3116"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 668, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 668, "endColumn": 67, "fix": "3117"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 685, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 685, "endColumn": 59, "fix": "3118"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 709, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 709, "endColumn": 64, "fix": "3119"}, {"ruleId": "2248", "severity": 1, "message": "3120", "line": 47, "column": 11, "nodeType": null, "messageId": "3121", "endLine": 47, "endColumn": 22, "suppressions": "3122"}, {"ruleId": "2248", "severity": 1, "message": "3123", "line": 11, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 11, "endColumn": 15}, {"ruleId": "2248", "severity": 1, "message": "3124", "line": 14, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 14, "endColumn": 8}, {"ruleId": "2248", "severity": 1, "message": "3125", "line": 25, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 25, "endColumn": 18}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 81, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 81, "endColumn": 55, "fix": "3126"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 105, "column": 19, "nodeType": "2224", "messageId": "2225", "endLine": 105, "endColumn": 54, "fix": "3127"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 136, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 136, "endColumn": 65, "fix": "3128"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 139, "column": 36, "nodeType": "2224", "messageId": "2225", "endLine": 139, "endColumn": 69, "fix": "3129"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 142, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 142, "endColumn": 68, "fix": "3130"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 145, "column": 40, "nodeType": "2224", "messageId": "2225", "endLine": 145, "endColumn": 72, "fix": "3131"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 148, "column": 32, "nodeType": "2224", "messageId": "2225", "endLine": 148, "endColumn": 66, "fix": "3132"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 151, "column": 32, "nodeType": "2224", "messageId": "2225", "endLine": 151, "endColumn": 66, "fix": "3133"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 154, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 154, "endColumn": 65, "fix": "3134"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 157, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 157, "endColumn": 64, "fix": "3135"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 160, "column": 38, "nodeType": "2224", "messageId": "2225", "endLine": 160, "endColumn": 72, "fix": "3136"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 163, "column": 40, "nodeType": "2224", "messageId": "2225", "endLine": 163, "endColumn": 75, "fix": "3137"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 180, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 180, "endColumn": 66, "fix": "3138"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 183, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 183, "endColumn": 70, "fix": "3139"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 186, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 186, "endColumn": 69, "fix": "3140"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 189, "column": 34, "nodeType": "2224", "messageId": "2225", "endLine": 189, "endColumn": 73, "fix": "3141"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 192, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 192, "endColumn": 67, "fix": "3142"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 195, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 195, "endColumn": 67, "fix": "3143"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 198, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 198, "endColumn": 66, "fix": "3144"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 201, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 201, "endColumn": 65, "fix": "3145"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 204, "column": 32, "nodeType": "2224", "messageId": "2225", "endLine": 204, "endColumn": 73, "fix": "3146"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 207, "column": 34, "nodeType": "2224", "messageId": "2225", "endLine": 207, "endColumn": 76, "fix": "3147"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 255, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 255, "endColumn": 57, "fix": "3148"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 304, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 310, "endColumn": 24, "fix": "3149"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 338, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 338, "endColumn": 60, "fix": "3150"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 366, "column": 55, "nodeType": "2224", "messageId": "2225", "endLine": 366, "endColumn": 74, "fix": "3151"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 367, "column": 52, "nodeType": "2224", "messageId": "2225", "endLine": 367, "endColumn": 71, "fix": "3152"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 368, "column": 55, "nodeType": "2224", "messageId": "2225", "endLine": 368, "endColumn": 74, "fix": "3153"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 423, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 423, "endColumn": 59, "fix": "3154"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 481, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 481, "endColumn": 44, "fix": "3155"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 489, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 489, "endColumn": 59, "fix": "3156"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 501, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 501, "endColumn": 63, "fix": "3157"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 513, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 513, "endColumn": 64, "fix": "3158"}, {"ruleId": "2248", "severity": 1, "message": "3052", "line": 5, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 5, "endColumn": 7}, {"ruleId": "2248", "severity": 1, "message": "3159", "line": 46, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 46, "endColumn": 11}, {"ruleId": "2248", "severity": 1, "message": "3160", "line": 47, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 47, "endColumn": 8}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 58, "column": 19, "nodeType": "2224", "messageId": "2225", "endLine": 58, "endColumn": 52, "fix": "3161"}, {"ruleId": "3162", "severity": 1, "message": "3163", "line": 80, "column": 13, "nodeType": "3164", "endLine": 80, "endColumn": 56}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 80, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 80, "endColumn": 53, "fix": "3165"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 109, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 109, "endColumn": 67, "fix": "3166"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 116, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 116, "endColumn": 63, "fix": "3167"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 122, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 122, "endColumn": 63, "fix": "3168"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 128, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 128, "endColumn": 60, "fix": "3169"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 134, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 134, "endColumn": 59, "fix": "3170"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 150, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 150, "endColumn": 57, "fix": "3171"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 169, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 169, "endColumn": 64, "fix": "3172"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 183, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 183, "endColumn": 58, "fix": "3173"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 202, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 202, "endColumn": 68, "fix": "3174"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 216, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 216, "endColumn": 40, "fix": "3175"}, {"ruleId": "3162", "severity": 1, "message": "3163", "line": 224, "column": 17, "nodeType": "3164", "endLine": 224, "endColumn": 60}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 224, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 224, "endColumn": 57, "fix": "3176"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 239, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 239, "endColumn": 59, "fix": "3177"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 146, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 146, "endColumn": 57, "fix": "3178"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 170, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 170, "endColumn": 58, "fix": "3179"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 268, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 268, "endColumn": 58, "fix": "3180"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 308, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 308, "endColumn": 64, "fix": "3181"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 339, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 339, "endColumn": 59, "fix": "3182"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 433, "column": 41, "nodeType": "2224", "messageId": "2225", "endLine": 433, "endColumn": 65, "fix": "3183"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 497, "column": 32, "nodeType": "2224", "messageId": "2225", "endLine": 497, "endColumn": 64, "fix": "3184"}, {"ruleId": "2248", "severity": 1, "message": "3185", "line": 2, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 2, "endColumn": 12}, {"ruleId": "2248", "severity": 1, "message": "2572", "line": 3, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 3, "endColumn": 15}, {"ruleId": "2248", "severity": 1, "message": "2614", "line": 5, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 5, "endColumn": 8}, {"ruleId": "2248", "severity": 1, "message": "3186", "line": 7, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 7, "endColumn": 15}, {"ruleId": "2248", "severity": 1, "message": "2467", "line": 12, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 12, "endColumn": 8}, {"ruleId": "2248", "severity": 1, "message": "3187", "line": 13, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 13, "endColumn": 6}, {"ruleId": "2248", "severity": 1, "message": "3034", "line": 42, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 42, "endColumn": 9}, {"ruleId": "2248", "severity": 1, "message": "3188", "line": 48, "column": 9, "nodeType": null, "messageId": "2250", "endLine": 48, "endColumn": 21}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 55, "column": 19, "nodeType": "2224", "messageId": "2225", "endLine": 55, "endColumn": 53, "fix": "3189"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 69, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 69, "endColumn": 57, "fix": "3190"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 147, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 147, "endColumn": 54, "fix": "3191"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 186, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 186, "endColumn": 56, "fix": "3192"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 226, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 226, "endColumn": 67, "fix": "3193"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 254, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 254, "endColumn": 44, "fix": "3194"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 262, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 262, "endColumn": 59, "fix": "3195"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 275, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 275, "endColumn": 55, "fix": "3196"}, {"ruleId": "2248", "severity": 1, "message": "3197", "line": 4, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 4, "endColumn": 11}, {"ruleId": "2248", "severity": 1, "message": "2614", "line": 5, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 5, "endColumn": 8}, {"ruleId": "2248", "severity": 1, "message": "3198", "line": 6, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 6, "endColumn": 7}, {"ruleId": "2248", "severity": 1, "message": "3199", "line": 10, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 10, "endColumn": 8}, {"ruleId": "2248", "severity": 1, "message": "3200", "line": 12, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 12, "endColumn": 9}, {"ruleId": "2248", "severity": 1, "message": "3201", "line": 36, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 36, "endColumn": 22}, {"ruleId": "2248", "severity": 1, "message": "3202", "line": 39, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 39, "endColumn": 19}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 112, "column": 18, "nodeType": "2224", "messageId": "2225", "endLine": 112, "endColumn": 51, "fix": "3203"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 146, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 146, "endColumn": 55, "fix": "3204"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 194, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 194, "endColumn": 59, "fix": "3205"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 229, "column": 36, "nodeType": "2224", "messageId": "2225", "endLine": 229, "endColumn": 55, "fix": "3206"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 298, "column": 43, "nodeType": "2224", "messageId": "2225", "endLine": 298, "endColumn": 62, "fix": "3207"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 317, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 317, "endColumn": 61, "fix": "3208"}, {"ruleId": "2248", "severity": 1, "message": "2671", "line": 3, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 3, "endColumn": 11}, {"ruleId": "2248", "severity": 1, "message": "2931", "line": 5, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 5, "endColumn": 8}, {"ruleId": "2248", "severity": 1, "message": "3209", "line": 6, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 6, "endColumn": 13}, {"ruleId": "2248", "severity": 1, "message": "2742", "line": 8, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 8, "endColumn": 7}, {"ruleId": "2248", "severity": 1, "message": "2743", "line": 9, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 9, "endColumn": 10}, {"ruleId": "2248", "severity": 1, "message": "3124", "line": 13, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 13, "endColumn": 8}, {"ruleId": "2248", "severity": 1, "message": "2577", "line": 25, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 25, "endColumn": 15}, {"ruleId": "2248", "severity": 1, "message": "2583", "line": 33, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 33, "endColumn": 16}, {"ruleId": "2248", "severity": 1, "message": "3034", "line": 70, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 70, "endColumn": 9}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 84, "column": 19, "nodeType": "2224", "messageId": "2225", "endLine": 84, "endColumn": 52, "fix": "3210"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 100, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 100, "endColumn": 54, "fix": "3211"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 130, "column": 40, "nodeType": "2224", "messageId": "2225", "endLine": 130, "endColumn": 72, "fix": "3212"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 132, "column": 35, "nodeType": "2224", "messageId": "2225", "endLine": 132, "endColumn": 68, "fix": "3213"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 170, "column": 34, "nodeType": "2224", "messageId": "2225", "endLine": 170, "endColumn": 67, "fix": "3214"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 172, "column": 34, "nodeType": "2224", "messageId": "2225", "endLine": 172, "endColumn": 67, "fix": "3215"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 194, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 194, "endColumn": 58, "fix": "3216"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 220, "column": 40, "nodeType": "2224", "messageId": "2225", "endLine": 220, "endColumn": 72, "fix": "3217"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 222, "column": 38, "nodeType": "2224", "messageId": "2225", "endLine": 222, "endColumn": 72, "fix": "3218"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 260, "column": 34, "nodeType": "2224", "messageId": "2225", "endLine": 260, "endColumn": 68, "fix": "3219"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 262, "column": 34, "nodeType": "2224", "messageId": "2225", "endLine": 262, "endColumn": 67, "fix": "3220"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 284, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 284, "endColumn": 57, "fix": "3221"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 327, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 327, "endColumn": 42, "fix": "3222"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 335, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 335, "endColumn": 58, "fix": "3223"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 349, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 349, "endColumn": 62, "fix": "3224"}, {"ruleId": "2248", "severity": 1, "message": "3225", "line": 3, "column": 17, "nodeType": null, "messageId": "2250", "endLine": 3, "endColumn": 25}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 42, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 42, "endColumn": 16, "suggestions": "3226"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 47, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 47, "endColumn": 16, "suggestions": "3227"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 52, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 52, "endColumn": 16, "suggestions": "3228"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 60, "column": 17, "nodeType": "2224", "messageId": "2225", "endLine": 60, "endColumn": 41, "fix": "3229"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 64, "column": 14, "nodeType": "2224", "messageId": "2225", "endLine": 64, "endColumn": 38, "fix": "3230"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 75, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 75, "endColumn": 46, "fix": "3231"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 81, "column": 15, "nodeType": "2224", "messageId": "2225", "endLine": 81, "endColumn": 39, "fix": "3232"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 88, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 88, "endColumn": 48, "fix": "3233"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 93, "column": 18, "nodeType": "2224", "messageId": "2225", "endLine": 93, "endColumn": 42, "fix": "3234"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 97, "column": 19, "nodeType": "2224", "messageId": "2225", "endLine": 97, "endColumn": 43, "fix": "3235"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 102, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 102, "endColumn": 45, "fix": "3236"}, {"ruleId": "2248", "severity": 1, "message": "3237", "line": 23, "column": 6, "nodeType": null, "messageId": "2250", "endLine": 23, "endColumn": 25}, {"ruleId": "2248", "severity": 1, "message": "3238", "line": 33, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 33, "endColumn": 15}, {"ruleId": "2248", "severity": 1, "message": "3239", "line": 34, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 34, "endColumn": 9}, {"ruleId": "2248", "severity": 1, "message": "3240", "line": 35, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 35, "endColumn": 11}, {"ruleId": "2248", "severity": 1, "message": "3241", "line": 22, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 22, "endColumn": 18}, {"ruleId": "2248", "severity": 1, "message": "3242", "line": 24, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 24, "endColumn": 23}, {"ruleId": "2248", "severity": 1, "message": "3243", "line": 27, "column": 9, "nodeType": null, "messageId": "2250", "endLine": 27, "endColumn": 26}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 48, "column": 17, "nodeType": "2224", "messageId": "2225", "endLine": 48, "endColumn": 36, "fix": "3244"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 84, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 84, "endColumn": 48, "fix": "3245"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 103, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 103, "endColumn": 41, "fix": "3246"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 120, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 120, "endColumn": 42, "fix": "3247"}, {"ruleId": "2248", "severity": 1, "message": "2576", "line": 7, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 7, "endColumn": 15}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 94, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 94, "endColumn": 41, "fix": "3248"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 103, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 103, "endColumn": 48, "fix": "3249"}, {"ruleId": "2456", "severity": 2, "message": "2457", "line": 119, "column": 28, "nodeType": "2458", "messageId": "2459", "suggestions": "3250"}, {"ruleId": "2456", "severity": 2, "message": "2457", "line": 119, "column": 49, "nodeType": "2458", "messageId": "2459", "suggestions": "3251"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 122, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 122, "endColumn": 48, "fix": "3252"}, {"ruleId": "2248", "severity": 1, "message": "3241", "line": 22, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 22, "endColumn": 18}, {"ruleId": "2248", "severity": 1, "message": "3253", "line": 24, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 24, "endColumn": 19}, {"ruleId": "2222", "severity": 1, "message": "2429", "line": 93, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 93, "endColumn": 59, "fix": "3254"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 102, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 102, "endColumn": 49, "fix": "3255"}, {"ruleId": "2222", "severity": 1, "message": "2349", "line": 140, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 140, "endColumn": 60, "fix": "3256"}, {"ruleId": "2222", "severity": 1, "message": "2349", "line": 149, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 149, "endColumn": 59, "fix": "3257"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 151, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 151, "endColumn": 47, "fix": "3258"}, {"ruleId": "2222", "severity": 1, "message": "2349", "line": 172, "column": 53, "nodeType": "2224", "messageId": "2225", "endLine": 172, "endColumn": 76, "fix": "3259"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 173, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 173, "endColumn": 46, "fix": "3260"}, {"ruleId": "2222", "severity": 1, "message": "2349", "line": 207, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 207, "endColumn": 39, "fix": "3261"}, {"ruleId": "2222", "severity": 1, "message": "2232", "line": 267, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 271, "endColumn": 26, "fix": "3262"}, {"ruleId": "2248", "severity": 1, "message": "3263", "line": 5, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 5, "endColumn": 14}, {"ruleId": "2248", "severity": 1, "message": "2887", "line": 5, "column": 16, "nodeType": null, "messageId": "2250", "endLine": 5, "endColumn": 20}, {"ruleId": "2248", "severity": 1, "message": "2575", "line": 5, "column": 28, "nodeType": null, "messageId": "2250", "endLine": 5, "endColumn": 29}, {"ruleId": "2248", "severity": 1, "message": "3264", "line": 7, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 7, "endColumn": 16}, {"ruleId": "2248", "severity": 1, "message": "3241", "line": 23, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 23, "endColumn": 18}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 101, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 105, "endColumn": 24, "fix": "3265"}, {"ruleId": "2248", "severity": 1, "message": "3241", "line": 22, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 22, "endColumn": 18}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 65, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 65, "endColumn": 39, "fix": "3266"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 98, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 98, "endColumn": 52, "fix": "3267"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 125, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 125, "endColumn": 47, "fix": "3268"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 130, "column": 34, "nodeType": "2224", "messageId": "2225", "endLine": 130, "endColumn": 53, "fix": "3269"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 135, "column": 32, "nodeType": "2224", "messageId": "2225", "endLine": 135, "endColumn": 51, "fix": "3270"}, {"ruleId": "2248", "severity": 1, "message": "3241", "line": 23, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 23, "endColumn": 18}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 54, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 54, "endColumn": 42, "fix": "3271"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 129, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 129, "endColumn": 52, "fix": "3272"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 137, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 137, "endColumn": 50, "fix": "3273"}, {"ruleId": "2248", "severity": 1, "message": "3241", "line": 29, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 29, "endColumn": 18}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 42, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 42, "endColumn": 16, "suggestions": "3274"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 78, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 78, "endColumn": 40, "fix": "3275"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 103, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 103, "endColumn": 53, "fix": "3276"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 75, "column": 18, "nodeType": "2224", "messageId": "2225", "endLine": 75, "endColumn": 42, "fix": "3277"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 84, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 84, "endColumn": 45, "fix": "3278"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 93, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 93, "endColumn": 46, "fix": "3279"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 102, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 102, "endColumn": 45, "fix": "3280"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 200, "column": 18, "nodeType": "2224", "messageId": "2225", "endLine": 200, "endColumn": 42, "fix": "3281"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 257, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 257, "endColumn": 53, "fix": "3282"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 272, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 272, "endColumn": 45, "fix": "3283"}, {"ruleId": "2222", "severity": 1, "message": "2317", "line": 279, "column": 49, "nodeType": "2224", "messageId": "2225", "endLine": 279, "endColumn": 72, "fix": "3284"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 280, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 280, "endColumn": 52, "fix": "3285"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 285, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 285, "endColumn": 46, "fix": "3286"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 289, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 289, "endColumn": 47, "fix": "3287"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 298, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 298, "endColumn": 49, "fix": "3288"}, {"ruleId": "2248", "severity": 1, "message": "3289", "line": 3, "column": 30, "nodeType": null, "messageId": "2250", "endLine": 3, "endColumn": 39}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 107, "column": 19, "nodeType": "2224", "messageId": "2225", "endLine": 107, "endColumn": 93, "fix": "3290"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 124, "column": 15, "nodeType": "2224", "messageId": "2225", "endLine": 124, "endColumn": 74, "fix": "3291"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 129, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 129, "endColumn": 47, "fix": "3292"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 138, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 138, "endColumn": 43, "fix": "3293"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 141, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 141, "endColumn": 40, "fix": "3294"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 154, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 154, "endColumn": 45, "fix": "3295"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 191, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 191, "endColumn": 62, "fix": "3296"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 44, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 44, "endColumn": 41, "fix": "3297"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 53, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 53, "endColumn": 48, "fix": "3298"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 66, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 66, "endColumn": 46, "fix": "3299"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 77, "column": 28, "nodeType": "2527", "messageId": "2528", "endLine": 77, "endColumn": 31, "suggestions": "3300"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 77, "column": 53, "nodeType": "2527", "messageId": "2528", "endLine": 77, "endColumn": 56, "suggestions": "3301"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 78, "column": 31, "nodeType": "2527", "messageId": "2528", "endLine": 78, "endColumn": 34, "suggestions": "3302"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 21, "column": 30, "nodeType": "2264", "messageId": "2265", "endLine": 21, "endColumn": 41}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 22, "column": 30, "nodeType": "2264", "messageId": "2265", "endLine": 22, "endColumn": 41}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 35, "column": 11, "nodeType": "2224", "messageId": "2225", "endLine": 35, "endColumn": 70, "fix": "3303"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 41, "column": 17, "nodeType": "2224", "messageId": "2225", "endLine": 41, "endColumn": 41, "fix": "3304"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 46, "column": 19, "nodeType": "2224", "messageId": "2225", "endLine": 46, "endColumn": 43, "fix": "3305"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 51, "column": 15, "nodeType": "2224", "messageId": "2225", "endLine": 51, "endColumn": 39, "fix": "3306"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 11, "column": 30, "nodeType": "2264", "messageId": "2265", "endLine": 11, "endColumn": 41}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 12, "column": 30, "nodeType": "2264", "messageId": "2265", "endLine": 12, "endColumn": 41}, {"ruleId": "2248", "severity": 1, "message": "3289", "line": 5, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 5, "endColumn": 12}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 46, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 46, "endColumn": 45, "fix": "3307"}, {"ruleId": "2222", "severity": 1, "message": "2317", "line": 52, "column": 49, "nodeType": "2224", "messageId": "2225", "endLine": 52, "endColumn": 72, "fix": "3308"}, {"ruleId": "2222", "severity": 1, "message": "2317", "line": 55, "column": 49, "nodeType": "2224", "messageId": "2225", "endLine": 55, "endColumn": 72, "fix": "3309"}, {"ruleId": "2222", "severity": 1, "message": "2317", "line": 58, "column": 49, "nodeType": "2224", "messageId": "2225", "endLine": 58, "endColumn": 72, "fix": "3310"}, {"ruleId": "2222", "severity": 1, "message": "2317", "line": 62, "column": 49, "nodeType": "2224", "messageId": "2225", "endLine": 62, "endColumn": 72, "fix": "3311"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 69, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 69, "endColumn": 46, "fix": "3312"}, {"ruleId": "2248", "severity": 1, "message": "3313", "line": 6, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 6, "endColumn": 10}, {"ruleId": "2248", "severity": 1, "message": "2518", "line": 10, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 10, "endColumn": 6}, {"ruleId": "2248", "severity": 1, "message": "2889", "line": 11, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 11, "endColumn": 13}, {"ruleId": "2248", "severity": 1, "message": "2613", "line": 13, "column": 3, "nodeType": null, "messageId": "2250", "endLine": 13, "endColumn": 9}, {"ruleId": "3314", "severity": 1, "message": "3315", "line": 102, "column": 13, "nodeType": "3164", "endLine": 106, "endColumn": 15}, {"ruleId": "2222", "severity": 1, "message": "3316", "line": 105, "column": 15, "nodeType": "2224", "messageId": "2225", "endLine": 105, "endColumn": 60, "fix": "3317"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 165, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 165, "endColumn": 51, "fix": "3318"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 169, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 169, "endColumn": 43, "fix": "3319"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 192, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 192, "endColumn": 49, "fix": "3320"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 230, "column": 19, "nodeType": "2224", "messageId": "2225", "endLine": 230, "endColumn": 93, "fix": "3321"}, {"ruleId": "2222", "severity": 1, "message": "2429", "line": 262, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 262, "endColumn": 63, "fix": "3322"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 11, "column": 17, "nodeType": "2224", "messageId": "2225", "endLine": 11, "endColumn": 41, "fix": "3323"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 16, "column": 19, "nodeType": "2224", "messageId": "2225", "endLine": 16, "endColumn": 43, "fix": "3324"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 21, "column": 19, "nodeType": "2224", "messageId": "2225", "endLine": 21, "endColumn": 43, "fix": "3325"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 26, "column": 14, "nodeType": "2224", "messageId": "2225", "endLine": 26, "endColumn": 38, "fix": "3326"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 31, "column": 15, "nodeType": "2224", "messageId": "2225", "endLine": 31, "endColumn": 39, "fix": "3327"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 64, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 64, "endColumn": 62, "fix": "3328"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 66, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 66, "endColumn": 62, "fix": "3329"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 68, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 68, "endColumn": 56, "fix": "3330"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 94, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 94, "endColumn": 63, "fix": "3331"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 117, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 117, "endColumn": 58, "fix": "3332"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 151, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 151, "endColumn": 57, "fix": "3333"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 169, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 169, "endColumn": 76, "fix": "3334"}, {"ruleId": "2248", "severity": 1, "message": "3335", "line": 31, "column": 26, "nodeType": null, "messageId": "2250", "endLine": 31, "endColumn": 35}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 54, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 54, "endColumn": 56, "fix": "3336"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 56, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 56, "endColumn": 60, "fix": "3337"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 58, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 58, "endColumn": 55, "fix": "3338"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 60, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 60, "endColumn": 56, "fix": "3339"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 62, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 62, "endColumn": 56, "fix": "3340"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 158, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 158, "endColumn": 63, "fix": "3341"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 179, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 179, "endColumn": 66, "fix": "3342"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 208, "column": 41, "nodeType": "2224", "messageId": "2225", "endLine": 208, "endColumn": 60, "fix": "3343"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 213, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 213, "endColumn": 54, "fix": "3344"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 217, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 217, "endColumn": 55, "fix": "3345"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 221, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 221, "endColumn": 55, "fix": "3346"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 227, "column": 34, "nodeType": "2224", "messageId": "2225", "endLine": 227, "endColumn": 58, "fix": "3347"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 232, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 232, "endColumn": 57, "fix": "3348"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 238, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 238, "endColumn": 57, "fix": "3349"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 30, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 30, "endColumn": 39, "fix": "3350"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 34, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 34, "endColumn": 41, "fix": "3351"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 38, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 38, "endColumn": 43, "fix": "3352"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 42, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 42, "endColumn": 42, "fix": "3353"}, {"ruleId": "2222", "severity": 1, "message": "2429", "line": 78, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 78, "endColumn": 73, "fix": "3354"}, {"ruleId": "2222", "severity": 1, "message": "2429", "line": 124, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 124, "endColumn": 72, "fix": "3355"}, {"ruleId": "2456", "severity": 2, "message": "2457", "line": 37, "column": 64, "nodeType": "2458", "messageId": "2459", "suggestions": "3356"}, {"ruleId": "2456", "severity": 2, "message": "2457", "line": 37, "column": 85, "nodeType": "2458", "messageId": "2459", "suggestions": "3357"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 39, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 39, "endColumn": 48, "fix": "3358"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 50, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 50, "endColumn": 63, "fix": "3359"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 52, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 52, "endColumn": 58, "fix": "3360"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 54, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 54, "endColumn": 57, "fix": "3361"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 56, "column": 25, "nodeType": "2224", "messageId": "2225", "endLine": 56, "endColumn": 58, "fix": "3362"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 58, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 58, "endColumn": 60, "fix": "3363"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 60, "column": 29, "nodeType": "2224", "messageId": "2225", "endLine": 60, "endColumn": 62, "fix": "3364"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 88, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 88, "endColumn": 48, "fix": "3365"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 135, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 135, "endColumn": 52, "fix": "3366"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 179, "column": 38, "nodeType": "2224", "messageId": "2225", "endLine": 179, "endColumn": 62, "fix": "3367"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 181, "column": 43, "nodeType": "2224", "messageId": "2225", "endLine": 181, "endColumn": 67, "fix": "3368"}, {"ruleId": "2222", "severity": 1, "message": "2324", "line": 233, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 233, "endColumn": 73, "fix": "3369"}, {"ruleId": "2222", "severity": 1, "message": "2324", "line": 244, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 244, "endColumn": 76, "fix": "3370"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 265, "column": 33, "nodeType": "2224", "messageId": "2225", "endLine": 265, "endColumn": 57, "fix": "3371"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 271, "column": 32, "nodeType": "2224", "messageId": "2225", "endLine": 271, "endColumn": 56, "fix": "3372"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 278, "column": 32, "nodeType": "2224", "messageId": "2225", "endLine": 278, "endColumn": 56, "fix": "3373"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 32, "column": 19, "nodeType": "2224", "messageId": "2225", "endLine": 32, "endColumn": 93, "fix": "3374"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 44, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 44, "endColumn": 47, "fix": "3375"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 48, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 48, "endColumn": 47, "fix": "3376"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 56, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 56, "endColumn": 45, "fix": "3377"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 59, "column": 42, "nodeType": "2224", "messageId": "2225", "endLine": 59, "endColumn": 91, "fix": "3378"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 11, "column": 17, "nodeType": "2224", "messageId": "2225", "endLine": 11, "endColumn": 41, "fix": "3379"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 16, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 16, "endColumn": 44, "fix": "3380"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 21, "column": 19, "nodeType": "2224", "messageId": "2225", "endLine": 21, "endColumn": 43, "fix": "3381"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 26, "column": 15, "nodeType": "2224", "messageId": "2225", "endLine": 26, "endColumn": 39, "fix": "3382"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 56, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 56, "endColumn": 62, "fix": "3383"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 58, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 58, "endColumn": 62, "fix": "3384"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 60, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 60, "endColumn": 60, "fix": "3385"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 85, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 85, "endColumn": 59, "fix": "3386"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 108, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 108, "endColumn": 46, "fix": "3387"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 116, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 116, "endColumn": 68, "fix": "3388"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 129, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 129, "endColumn": 70, "fix": "3389"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 142, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 142, "endColumn": 69, "fix": "3390"}, {"ruleId": "2222", "severity": 1, "message": "2229", "line": 155, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 155, "endColumn": 70, "fix": "3391"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 173, "column": 27, "nodeType": "2224", "messageId": "2225", "endLine": 173, "endColumn": 46, "fix": "3392"}, {"ruleId": "2248", "severity": 1, "message": "3393", "line": 26, "column": 10, "nodeType": null, "messageId": "2250", "endLine": 26, "endColumn": 21}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 65, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 65, "endColumn": 45, "fix": "3394"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 67, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 67, "endColumn": 45, "fix": "3395"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 69, "column": 24, "nodeType": "2224", "messageId": "2225", "endLine": 69, "endColumn": 43, "fix": "3396"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 71, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 71, "endColumn": 42, "fix": "3397"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 73, "column": 26, "nodeType": "2224", "messageId": "2225", "endLine": 73, "endColumn": 45, "fix": "3398"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 197, "column": 41, "nodeType": "2224", "messageId": "2225", "endLine": 197, "endColumn": 60, "fix": "3399"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 203, "column": 32, "nodeType": "2224", "messageId": "2225", "endLine": 203, "endColumn": 56, "fix": "3400"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 209, "column": 36, "nodeType": "2224", "messageId": "2225", "endLine": 209, "endColumn": 60, "fix": "3401"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 214, "column": 39, "nodeType": "2224", "messageId": "2225", "endLine": 214, "endColumn": 63, "fix": "3402"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 220, "column": 38, "nodeType": "2224", "messageId": "2225", "endLine": 220, "endColumn": 62, "fix": "3403"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 37, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 37, "endColumn": 104, "fix": "3404"}, {"ruleId": "2222", "severity": 1, "message": "2283", "line": 14, "column": 16, "nodeType": "3405", "messageId": "2225", "endLine": 17, "endColumn": 6, "fix": "3406"}, {"ruleId": "2222", "severity": 1, "message": "2948", "line": 29, "column": 16, "nodeType": "3405", "messageId": "2225", "endLine": 29, "endColumn": 60, "fix": "3407"}, {"ruleId": "2222", "severity": 1, "message": "2948", "line": 41, "column": 16, "nodeType": "3405", "messageId": "2225", "endLine": 44, "endColumn": 6, "fix": "3408"}, {"ruleId": "2222", "severity": 1, "message": "3409", "line": 28, "column": 21, "nodeType": "3405", "messageId": "2225", "endLine": 31, "endColumn": 10, "fix": "3410"}, {"ruleId": "2222", "severity": 1, "message": "2317", "line": 45, "column": 14, "nodeType": "3405", "messageId": "2225", "endLine": 48, "endColumn": 10, "fix": "3411"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 64, "column": 35, "nodeType": "3405", "messageId": "2225", "endLine": 64, "endColumn": 59, "fix": "3412"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 67, "column": 36, "nodeType": "3405", "messageId": "2225", "endLine": 67, "endColumn": 60, "fix": "3413"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 15, "column": 16, "nodeType": "3405", "messageId": "2225", "endLine": 18, "endColumn": 6, "fix": "3414"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 24, "column": 14, "nodeType": "2224", "messageId": "2225", "endLine": 24, "endColumn": 33, "fix": "3415"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 69, "column": 30, "nodeType": "2224", "messageId": "2225", "endLine": 69, "endColumn": 49, "fix": "3416"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 74, "column": 31, "nodeType": "2224", "messageId": "2225", "endLine": 74, "endColumn": 50, "fix": "3417"}, {"ruleId": "2222", "severity": 1, "message": "2283", "line": 50, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 50, "endColumn": 98, "fix": "3418"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 51, "column": 32, "nodeType": "2224", "messageId": "2225", "endLine": 51, "endColumn": 64, "fix": "3419"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 75, "column": 34, "nodeType": "2224", "messageId": "2225", "endLine": 75, "endColumn": 71, "fix": "3420"}, {"ruleId": "3421", "severity": 1, "message": "3422", "line": 41, "column": 18, "nodeType": "3405", "messageId": "3423", "endLine": 44, "endColumn": 8}, {"ruleId": "3421", "severity": 1, "message": "3424", "line": 41, "column": 18, "nodeType": "3405", "messageId": "3423", "endLine": 44, "endColumn": 8}, {"ruleId": "3421", "severity": 1, "message": "3425", "line": 41, "column": 18, "nodeType": "3405", "messageId": "3423", "endLine": 44, "endColumn": 8}, {"ruleId": "3421", "severity": 1, "message": "3426", "line": 41, "column": 18, "nodeType": "3405", "messageId": "3423", "endLine": 44, "endColumn": 8}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 49, "column": 12, "nodeType": "2224", "messageId": "2225", "endLine": 49, "endColumn": 31, "fix": "3427"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 37, "column": 19, "nodeType": "2224", "messageId": "2225", "endLine": 37, "endColumn": 46, "fix": "3428"}, {"ruleId": "3421", "severity": 1, "message": "3429", "line": 49, "column": 16, "nodeType": "3405", "messageId": "3423", "endLine": 52, "endColumn": 6, "fix": "3430"}, {"ruleId": "3421", "severity": 1, "message": "3429", "line": 67, "column": 18, "nodeType": "3405", "messageId": "3423", "endLine": 70, "endColumn": 8, "fix": "3431"}, {"ruleId": "2222", "severity": 1, "message": "3432", "line": 108, "column": 11, "nodeType": "2224", "messageId": "2225", "endLine": 108, "endColumn": 83, "fix": "3433"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 110, "column": 16, "nodeType": "2224", "messageId": "2225", "endLine": 110, "endColumn": 35, "fix": "3434"}, {"ruleId": "2222", "severity": 1, "message": "3432", "line": 131, "column": 11, "nodeType": "2224", "messageId": "2225", "endLine": 131, "endColumn": 83, "fix": "3435"}, {"ruleId": "2222", "severity": 1, "message": "2324", "line": 133, "column": 17, "nodeType": "2224", "messageId": "2225", "endLine": 133, "endColumn": 49, "fix": "3436"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 400, "column": 26, "nodeType": "3405", "messageId": "2225", "endLine": 403, "endColumn": 16, "fix": "3437"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 412, "column": 23, "nodeType": "2224", "messageId": "2225", "endLine": 412, "endColumn": 105, "fix": "3438"}, {"ruleId": "2222", "severity": 1, "message": "2283", "line": 449, "column": 21, "nodeType": "2224", "messageId": "2225", "endLine": 449, "endColumn": 61, "fix": "3439"}, {"ruleId": "2456", "severity": 2, "message": "2508", "line": 461, "column": 39, "nodeType": "2458", "messageId": "2459", "suggestions": "3440"}, {"ruleId": "2456", "severity": 2, "message": "2508", "line": 461, "column": 53, "nodeType": "2458", "messageId": "2459", "suggestions": "3441"}, {"ruleId": "2222", "severity": 1, "message": "2246", "line": 59, "column": 22, "nodeType": "2224", "messageId": "2225", "endLine": 59, "endColumn": 65, "fix": "3442"}, {"ruleId": "2222", "severity": 1, "message": "2948", "line": 21, "column": 7, "nodeType": "2224", "messageId": "2225", "endLine": 21, "endColumn": 65, "fix": "3443"}, {"ruleId": "2222", "severity": 1, "message": "2948", "line": 17, "column": 35, "nodeType": "2224", "messageId": "2225", "endLine": 17, "endColumn": 78, "fix": "3444"}, {"ruleId": "3421", "severity": 1, "message": "3445", "line": 33, "column": 16, "nodeType": "3405", "messageId": "3423", "endLine": 40, "endColumn": 6, "fix": "3446"}, {"ruleId": "3421", "severity": 1, "message": "3445", "line": 33, "column": 16, "nodeType": "3405", "messageId": "3423", "endLine": 40, "endColumn": 6, "fix": "3447"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 29, "column": 20, "nodeType": "2224", "messageId": "2225", "endLine": 29, "endColumn": 50, "fix": "3448"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 47, "column": 16, "nodeType": "2224", "messageId": "2225", "endLine": 47, "endColumn": 35, "fix": "3449"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 64, "column": 18, "nodeType": "2224", "messageId": "2225", "endLine": 64, "endColumn": 37, "fix": "3450"}, {"ruleId": "3421", "severity": 1, "message": "3429", "line": 77, "column": 18, "nodeType": "3405", "messageId": "3423", "endLine": 82, "endColumn": 8, "fix": "3451"}, {"ruleId": "2222", "severity": 1, "message": "3432", "line": 126, "column": 11, "nodeType": "2224", "messageId": "2225", "endLine": 126, "endColumn": 84, "fix": "3452"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 128, "column": 16, "nodeType": "2224", "messageId": "2225", "endLine": 128, "endColumn": 35, "fix": "3453"}, {"ruleId": "3421", "severity": 1, "message": "3454", "line": 20, "column": 18, "nodeType": "3405", "messageId": "3423", "endLine": 24, "endColumn": 8, "fix": "3455"}, {"ruleId": "3421", "severity": 1, "message": "3456", "line": 20, "column": 18, "nodeType": "3405", "messageId": "3423", "endLine": 24, "endColumn": 8, "fix": "3457"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 23, "column": 28, "nodeType": "2224", "messageId": "2225", "endLine": 23, "endColumn": 251, "fix": "3458"}, {"ruleId": "2222", "severity": 1, "message": "2223", "line": 21, "column": 18, "nodeType": "3405", "messageId": "2225", "endLine": 23, "endColumn": 8, "fix": "3459"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 26, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 26, "endColumn": 16, "suggestions": "3460"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 41, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 41, "endColumn": 16, "suggestions": "3461"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 95, "column": 33, "nodeType": "2527", "messageId": "2528", "endLine": 95, "endColumn": 36, "suggestions": "3462"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 123, "column": 33, "nodeType": "2527", "messageId": "2528", "endLine": 123, "endColumn": 36, "suggestions": "3463"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 159, "column": 33, "nodeType": "2527", "messageId": "2528", "endLine": 159, "endColumn": 36, "suggestions": "3464"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 196, "column": 33, "nodeType": "2527", "messageId": "2528", "endLine": 196, "endColumn": 36, "suggestions": "3465"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 218, "column": 7, "nodeType": "2264", "messageId": "2265", "endLine": 218, "endColumn": 18, "suggestions": "3466"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 258, "column": 33, "nodeType": "2527", "messageId": "2528", "endLine": 258, "endColumn": 36, "suggestions": "3467"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 67, "column": 3, "nodeType": "2264", "messageId": "2265", "endLine": 67, "endColumn": 14, "suggestions": "3468"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 68, "column": 3, "nodeType": "2264", "messageId": "2265", "endLine": 68, "endColumn": 14, "suggestions": "3469"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 73, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 73, "endColumn": 16, "suggestions": "3470"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 74, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 74, "endColumn": 16, "suggestions": "3471"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 85, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 85, "endColumn": 16, "suggestions": "3472"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 98, "column": 24, "nodeType": "2527", "messageId": "2528", "endLine": 98, "endColumn": 27, "suggestions": "3473"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 115, "column": 21, "nodeType": "2527", "messageId": "2528", "endLine": 115, "endColumn": 24, "suggestions": "3474"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 121, "column": 71, "nodeType": "2527", "messageId": "2528", "endLine": 121, "endColumn": 74, "suggestions": "3475"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 122, "column": 21, "nodeType": "2527", "messageId": "2528", "endLine": 122, "endColumn": 24, "suggestions": "3476"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 128, "column": 74, "nodeType": "2527", "messageId": "2528", "endLine": 128, "endColumn": 77, "suggestions": "3477"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 129, "column": 21, "nodeType": "2527", "messageId": "2528", "endLine": 129, "endColumn": 24, "suggestions": "3478"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 136, "column": 16, "nodeType": "2527", "messageId": "2528", "endLine": 136, "endColumn": 19, "suggestions": "3479"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 137, "column": 24, "nodeType": "2527", "messageId": "2528", "endLine": 137, "endColumn": 27, "suggestions": "3480"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 138, "column": 3, "nodeType": "2264", "messageId": "2265", "endLine": 138, "endColumn": 14, "suggestions": "3481"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 139, "column": 3, "nodeType": "2264", "messageId": "2265", "endLine": 139, "endColumn": 14, "suggestions": "3482"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 142, "column": 37, "nodeType": "2527", "messageId": "2528", "endLine": 142, "endColumn": 40, "suggestions": "3483"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 146, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 146, "endColumn": 16, "suggestions": "3484"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 159, "column": 16, "nodeType": "2527", "messageId": "2528", "endLine": 159, "endColumn": 19, "suggestions": "3485"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 160, "column": 24, "nodeType": "2527", "messageId": "2528", "endLine": 160, "endColumn": 27, "suggestions": "3486"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 161, "column": 21, "nodeType": "2527", "messageId": "2528", "endLine": 161, "endColumn": 24, "suggestions": "3487"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 172, "column": 16, "nodeType": "2527", "messageId": "2528", "endLine": 172, "endColumn": 19, "suggestions": "3488"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 173, "column": 24, "nodeType": "2527", "messageId": "2528", "endLine": 173, "endColumn": 27, "suggestions": "3489"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 174, "column": 21, "nodeType": "2527", "messageId": "2528", "endLine": 174, "endColumn": 24, "suggestions": "3490"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 13, "column": 3, "nodeType": "2264", "messageId": "2265", "endLine": 13, "endColumn": 14, "suggestions": "3491"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 18, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 18, "endColumn": 16, "suggestions": "3492"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 21, "column": 7, "nodeType": "2264", "messageId": "2265", "endLine": 21, "endColumn": 18, "suggestions": "3493"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 28, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 28, "endColumn": 16, "suggestions": "3494"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 31, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 31, "endColumn": 16, "suggestions": "3495"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 38, "column": 7, "nodeType": "2264", "messageId": "2265", "endLine": 38, "endColumn": 18, "suggestions": "3496"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 41, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 41, "endColumn": 16, "suggestions": "3497"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 50, "column": 3, "nodeType": "2264", "messageId": "2265", "endLine": 50, "endColumn": 14, "suggestions": "3498"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 54, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 54, "endColumn": 16, "suggestions": "3499"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 67, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 67, "endColumn": 16, "suggestions": "3500"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 80, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 80, "endColumn": 16, "suggestions": "3501"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 82, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 82, "endColumn": 16, "suggestions": "3502"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 84, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 84, "endColumn": 16, "suggestions": "3503"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 93, "column": 3, "nodeType": "2264", "messageId": "2265", "endLine": 93, "endColumn": 14, "suggestions": "3504"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 131, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 131, "endColumn": 16, "suggestions": "3505"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 134, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 134, "endColumn": 16, "suggestions": "3506"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 139, "column": 7, "nodeType": "2264", "messageId": "2265", "endLine": 139, "endColumn": 18, "suggestions": "3507"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 140, "column": 7, "nodeType": "2264", "messageId": "2265", "endLine": 140, "endColumn": 18, "suggestions": "3508"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 143, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 143, "endColumn": 16, "suggestions": "3509"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 153, "column": 14, "nodeType": "2527", "messageId": "2528", "endLine": 153, "endColumn": 17, "suggestions": "3510"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 154, "column": 14, "nodeType": "2527", "messageId": "2528", "endLine": 154, "endColumn": 17, "suggestions": "3511"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 155, "column": 14, "nodeType": "2527", "messageId": "2528", "endLine": 155, "endColumn": 17, "suggestions": "3512"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 8, "column": 3, "nodeType": "2264", "messageId": "2265", "endLine": 8, "endColumn": 14, "suggestions": "3513"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 12, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 12, "endColumn": 16, "suggestions": "3514"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 16, "column": 7, "nodeType": "2264", "messageId": "2265", "endLine": 16, "endColumn": 18, "suggestions": "3515"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 18, "column": 7, "nodeType": "2264", "messageId": "2265", "endLine": 18, "endColumn": 18, "suggestions": "3516"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 22, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 22, "endColumn": 16, "suggestions": "3517"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 24, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 24, "endColumn": 16, "suggestions": "3518"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 25, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 25, "endColumn": 16, "suggestions": "3519"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 28, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 28, "endColumn": 16, "suggestions": "3520"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 32, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 32, "endColumn": 16, "suggestions": "3521"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 38, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 38, "endColumn": 16, "suggestions": "3522"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 49, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 49, "endColumn": 16, "suggestions": "3523"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 52, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 52, "endColumn": 16, "suggestions": "3524"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 60, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 60, "endColumn": 16, "suggestions": "3525"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 63, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 63, "endColumn": 16, "suggestions": "3526"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 67, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 67, "endColumn": 16, "suggestions": "3527"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 70, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 70, "endColumn": 16, "suggestions": "3528"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 72, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 72, "endColumn": 16, "suggestions": "3529"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 75, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 75, "endColumn": 16, "suggestions": "3530"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 78, "column": 7, "nodeType": "2264", "messageId": "2265", "endLine": 78, "endColumn": 18, "suggestions": "3531"}, {"ruleId": "2248", "severity": 1, "message": "3532", "line": 79, "column": 14, "nodeType": null, "messageId": "2250", "endLine": 79, "endColumn": 19}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 80, "column": 7, "nodeType": "2264", "messageId": "2265", "endLine": 80, "endColumn": 18, "suggestions": "3533"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 83, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 83, "endColumn": 16, "suggestions": "3534"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 87, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 87, "endColumn": 16, "suggestions": "3535"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 88, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 88, "endColumn": 16, "suggestions": "3536"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 89, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 89, "endColumn": 16, "suggestions": "3537"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 90, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 90, "endColumn": 16, "suggestions": "3538"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 91, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 91, "endColumn": 16, "suggestions": "3539"}, {"ruleId": "2262", "severity": 1, "message": "2263", "line": 92, "column": 5, "nodeType": "2264", "messageId": "2265", "endLine": 92, "endColumn": 16, "suggestions": "3540"}, {"ruleId": "2525", "severity": 2, "message": "2526", "line": 99, "column": 14, "nodeType": "2527", "messageId": "2528", "endLine": 99, "endColumn": 17, "suggestions": "3541"}, "tailwindcss/enforces-shorthand", "Classnames 'h-4, w-4' could be replaced by the 'size-4' shorthand!", "JSXAttribute", "shorthandCandidateDetected", {"range": "3542", "text": "3543"}, {"range": "3544", "text": "3543"}, {"range": "3545", "text": "3546"}, "Classnames 'h-3, w-3' could be replaced by the 'size-3' shorthand!", {"range": "3547", "text": "3548"}, {"range": "3549", "text": "3550"}, "Classnames 'h-16, w-16' could be replaced by the 'size-16' shorthand!", {"range": "3551", "text": "3552"}, {"range": "3553", "text": "3546"}, {"range": "3554", "text": "3548"}, {"range": "3555", "text": "3550"}, {"range": "3556", "text": "3557"}, {"range": "3558", "text": "3546"}, {"range": "3559", "text": "3548"}, {"range": "3560", "text": "3550"}, {"range": "3561", "text": "3562"}, {"range": "3563", "text": "3546"}, {"range": "3564", "text": "3548"}, {"range": "3565", "text": "3550"}, {"range": "3566", "text": "3567"}, "Classnames 'h-5, w-5' could be replaced by the 'size-5' shorthand!", {"range": "3568", "text": "3569"}, "@typescript-eslint/no-unused-vars", "'index' is defined but never used.", "unusedVar", {"range": "3570", "text": "3571"}, {"range": "3572", "text": "3573"}, "Classnames 'h-32, w-32' could be replaced by the 'size-32' shorthand!", {"range": "3574", "text": "3575"}, {"range": "3576", "text": "3577"}, "'topProducts' is assigned a value but never used.", ["3578"], "'customerMetrics' is assigned a value but never used.", ["3579"], "'trafficData' is assigned a value but never used.", ["3580"], "no-console", "Unexpected console statement. Only these console methods are allowed: warn, error.", "MemberExpression", "limited", ["3581"], {"range": "3582", "text": "3543"}, {"range": "3583", "text": "3543"}, {"range": "3584", "text": "3585"}, {"range": "3586", "text": "3585"}, {"range": "3587", "text": "3585"}, {"range": "3588", "text": "3589"}, {"range": "3590", "text": "3591"}, {"range": "3592", "text": "3593"}, {"range": "3594", "text": "3595"}, {"range": "3596", "text": "3597"}, {"range": "3598", "text": "3569"}, {"range": "3599", "text": "3543"}, {"range": "3600", "text": "3569"}, {"range": "3601", "text": "3573"}, {"range": "3602", "text": "3543"}, {"range": "3603", "text": "3589"}, "Classnames 'h-10, w-10' could be replaced by the 'size-10' shorthand!", {"range": "3604", "text": "3605"}, ["3606"], {"range": "3607", "text": "3605"}, {"range": "3608", "text": "3605"}, {"range": "3609", "text": "3605"}, {"range": "3610", "text": "3569"}, {"range": "3611", "text": "3605"}, {"range": "3612", "text": "3605"}, {"range": "3613", "text": "3605"}, {"range": "3614", "text": "3605"}, {"range": "3615", "text": "3543"}, {"range": "3616", "text": "3569"}, {"range": "3617", "text": "3573"}, {"range": "3618", "text": "3543"}, {"range": "3619", "text": "3589"}, {"range": "3620", "text": "3543"}, {"range": "3621", "text": "3569"}, {"range": "3622", "text": "3543"}, {"range": "3623", "text": "3569"}, {"range": "3624", "text": "3573"}, {"range": "3625", "text": "3543"}, ["3626"], {"range": "3627", "text": "3597"}, {"range": "3628", "text": "3597"}, {"range": "3629", "text": "3569"}, {"range": "3630", "text": "3573"}, {"range": "3631", "text": "3589"}, {"range": "3632", "text": "3633"}, {"range": "3634", "text": "3543"}, {"range": "3635", "text": "3546"}, {"range": "3636", "text": "3548"}, {"range": "3637", "text": "3550"}, {"range": "3638", "text": "3639"}, "Classnames 'h-8, w-8' could be replaced by the 'size-8' shorthand!", {"range": "3640", "text": "3641"}, {"range": "3642", "text": "3643"}, {"range": "3644", "text": "3645"}, {"range": "3646", "text": "3647"}, {"range": "3648", "text": "3649"}, {"range": "3650", "text": "3651"}, "Classnames 'h-2, w-2' could be replaced by the 'size-2' shorthand!", {"range": "3652", "text": "3653"}, {"range": "3654", "text": "3655"}, {"range": "3656", "text": "3657"}, {"range": "3658", "text": "3659"}, {"range": "3660", "text": "3661"}, {"range": "3662", "text": "3659"}, {"range": "3663", "text": "3661"}, {"range": "3664", "text": "3659"}, {"range": "3665", "text": "3661"}, {"range": "3666", "text": "3543"}, {"range": "3667", "text": "3543"}, {"range": "3668", "text": "3669"}, {"range": "3670", "text": "3569"}, {"range": "3671", "text": "3573"}, {"range": "3672", "text": "3589"}, {"range": "3673", "text": "3633"}, "Classnames 'h-64, w-64' could be replaced by the 'size-64' shorthand!", {"range": "3674", "text": "3675"}, "tailwindcss/no-custom-classname", "Classname 'bg-' is not a Tailwind CSS class!", "customClassnameDetected", "Classname '-100' is not a Tailwind CSS class!", "Classname 'group-hover:bg-' is not a Tailwind CSS class!", "Classname '-200' is not a Tailwind CSS class!", "Classnames 'h-6, w-6' could be replaced by the 'size-6' shorthand!", {"range": "3676", "text": "3677"}, "Classname 'text-' is not a Tailwind CSS class!", "Classname '-600' is not a Tailwind CSS class!", "Classnames 'left-0, right-0' could be replaced by the 'inset-x-0' shorthand!", {"range": "3678", "text": "3679"}, "Classname 'from-' is not a Tailwind CSS class!", "Classname '-400' is not a Tailwind CSS class!", "Classname 'to-' is not a Tailwind CSS class!", {"range": "3680", "text": "3573"}, {"range": "3681", "text": "3589"}, {"range": "3682", "text": "3683"}, {"range": "3684", "text": "3685"}, {"range": "3686", "text": "3687"}, {"range": "3688", "text": "3689"}, {"range": "3690", "text": "3691"}, {"range": "3692", "text": "3689"}, {"range": "3693", "text": "3694"}, {"range": "3695", "text": "3689"}, {"range": "3696", "text": "3697"}, {"range": "3698", "text": "3689"}, {"range": "3699", "text": "3569"}, {"range": "3700", "text": "3573"}, {"range": "3701", "text": "3589"}, {"range": "3702", "text": "3703"}, "'searchQuery' is assigned a value but never used.", ["3704"], "'setSearchQuery' is assigned a value but never used.", ["3705"], "'categoriesLoading' is assigned a value but never used.", ["3706"], "'brandsLoading' is assigned a value but never used.", ["3707"], "'materialsLoading' is assigned a value but never used.", ["3708"], "'colorsLoading' is assigned a value but never used.", ["3709"], ["3710"], ["3711"], ["3712"], ["3713"], {"range": "3714", "text": "3543"}, {"range": "3715", "text": "3543"}, {"range": "3716", "text": "3569"}, {"range": "3717", "text": "3718"}, {"range": "3719", "text": "3720"}, {"range": "3721", "text": "3597"}, {"range": "3722", "text": "3543"}, {"range": "3723", "text": "3569"}, {"range": "3724", "text": "3543"}, {"range": "3725", "text": "3573"}, {"range": "3726", "text": "3597"}, {"range": "3727", "text": "3597"}, {"range": "3728", "text": "3597"}, {"range": "3729", "text": "3597"}, {"range": "3730", "text": "3597"}, {"range": "3731", "text": "3597"}, {"range": "3732", "text": "3597"}, {"range": "3733", "text": "3543"}, {"range": "3734", "text": "3735"}, {"range": "3736", "text": "3589"}, {"range": "3737", "text": "3569"}, {"range": "3738", "text": "3633"}, {"range": "3739", "text": "3589"}, {"range": "3740", "text": "3543"}, {"range": "3741", "text": "3589"}, {"range": "3742", "text": "3597"}, {"range": "3743", "text": "3597"}, {"range": "3744", "text": "3597"}, {"range": "3745", "text": "3573"}, {"range": "3746", "text": "3569"}, {"range": "3747", "text": "3543"}, {"range": "3748", "text": "3749"}, {"range": "3750", "text": "3543"}, {"range": "3751", "text": "3543"}, {"range": "3752", "text": "3569"}, {"range": "3753", "text": "3754"}, {"range": "3755", "text": "3573"}, {"range": "3756", "text": "3633"}, {"range": "3757", "text": "3758"}, "Classnames 'h-12, w-12' could be replaced by the 'size-12' shorthand!", {"range": "3759", "text": "3760"}, {"range": "3761", "text": "3597"}, {"range": "3762", "text": "3543"}, {"range": "3763", "text": "3543"}, {"range": "3764", "text": "3543"}, {"range": "3765", "text": "3543"}, {"range": "3766", "text": "3543"}, {"range": "3767", "text": "3768"}, {"range": "3769", "text": "3543"}, {"range": "3770", "text": "3543"}, {"range": "3771", "text": "3772"}, "Classnames 'h-20, w-20' could be replaced by the 'size-20' shorthand!", {"range": "3773", "text": "3774"}, {"range": "3775", "text": "3776"}, {"range": "3777", "text": "3778"}, {"range": "3779", "text": "3573"}, {"range": "3780", "text": "3781"}, {"range": "3782", "text": "3569"}, {"range": "3783", "text": "3784"}, {"range": "3785", "text": "3589"}, {"range": "3786", "text": "3787"}, {"range": "3788", "text": "3633"}, {"range": "3789", "text": "3790"}, {"range": "3791", "text": "3735"}, {"range": "3792", "text": "3793"}, {"range": "3794", "text": "3754"}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["3795", "3796", "3797", "3798"], {"range": "3799", "text": "3639"}, {"range": "3800", "text": "3573"}, {"range": "3801", "text": "3573"}, {"range": "3802", "text": "3569"}, {"range": "3803", "text": "3804"}, "'Building2' is defined but never used.", "'Users' is defined but never used.", "'Badge' is defined but never used.", ["3805"], {"range": "3806", "text": "3543"}, {"range": "3807", "text": "3543"}, {"range": "3808", "text": "3569"}, {"range": "3809", "text": "3573"}, {"range": "3810", "text": "3543"}, {"range": "3811", "text": "3569"}, {"range": "3812", "text": "3597"}, {"range": "3813", "text": "3543"}, {"range": "3814", "text": "3573"}, {"range": "3815", "text": "3543"}, {"range": "3816", "text": "3569"}, {"range": "3817", "text": "3818"}, {"range": "3819", "text": "3647"}, {"range": "3820", "text": "3821"}, {"range": "3822", "text": "3823"}, {"range": "3824", "text": "3573"}, {"range": "3825", "text": "3826"}, {"range": "3827", "text": "3826"}, {"range": "3828", "text": "3826"}, {"range": "3829", "text": "3543"}, {"range": "3830", "text": "3735"}, {"range": "3831", "text": "3633"}, {"range": "3832", "text": "3543"}, {"range": "3833", "text": "3589"}, {"range": "3834", "text": "3835"}, {"range": "3836", "text": "3835"}, {"range": "3837", "text": "3835"}, {"range": "3838", "text": "3573"}, {"range": "3839", "text": "3543"}, {"range": "3840", "text": "3543"}, {"range": "3841", "text": "3543"}, {"range": "3842", "text": "3569"}, {"range": "3843", "text": "3543"}, "'req' is defined but never used.", ["3844"], ["3845"], "Classnames 'px-4, py-4' could be replaced by the 'p-4' shorthand!", {"range": "3846", "text": "3847"}, "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["3848", "3849", "3850", "3851"], ["3852", "3853", "3854", "3855"], ["3856", "3857", "3858", "3859"], ["3860", "3861", "3862", "3863"], ["3864", "3865", "3866", "3867"], ["3868", "3869", "3870", "3871"], "Classnames 'w-4, h-4' could be replaced by the 'size-4' shorthand!", {"range": "3872", "text": "3873"}, "'Hammer' is defined but never used.", "'Tag' is defined but never used.", {"range": "3874", "text": "3758"}, {"range": "3875", "text": "3571"}, "tailwindcss/migration-from-tailwind-2", "Classname 'flex-shrink-0' should be updated to 'shrink-0' in Tailwind CSS v3!", "classnameChanged", {"range": "3876", "text": "3877"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["3878", "3879"], "'router' is assigned a value but never used.", "consistent-return", "Expected to return a value at the end of arrow function.", "ArrowFunctionExpression", "missingReturn", {"range": "3880", "text": "3881"}, {"range": "3882", "text": "3597"}, {"range": "3883", "text": "3758"}, {"range": "3884", "text": "3597"}, {"range": "3885", "text": "3543"}, {"range": "3886", "text": "3887"}, {"range": "3888", "text": "3543"}, {"range": "3889", "text": "3543"}, "Classnames 'h-9, w-9' could be replaced by the 'size-9' shorthand!", {"range": "3890", "text": "3891"}, {"range": "3892", "text": "3597"}, {"range": "3893", "text": "3543"}, {"range": "3894", "text": "3543"}, {"range": "3895", "text": "3543"}, ["3896", "3897", "3898", "3899"], {"range": "3900", "text": "3891"}, {"range": "3901", "text": "3597"}, {"range": "3902", "text": "3597"}, {"range": "3903", "text": "3904"}, {"range": "3905", "text": "3597"}, {"range": "3906", "text": "3907"}, {"range": "3908", "text": "3657"}, {"range": "3909", "text": "3910"}, {"range": "3911", "text": "3543"}, {"range": "3912", "text": "3543"}, {"range": "3913", "text": "3543"}, {"range": "3914", "text": "3915"}, ["3916", "3917", "3918", "3919"], ["3920", "3921", "3922", "3923"], {"range": "3924", "text": "3925"}, {"range": "3926", "text": "3925"}, {"range": "3927", "text": "3543"}, ["3928", "3929", "3930", "3931"], ["3932", "3933", "3934", "3935"], "'Archive' is defined but never used.", "'Check' is defined but never used.", "'Copy' is defined but never used.", "'ExternalLink' is defined but never used.", "'Grid3X3' is defined but never used.", "'List' is defined but never used.", "'X' is defined but never used.", "'toast' is defined but never used.", "'Label' is defined but never used.", "'Select' is defined but never used.", "'SelectContent' is defined but never used.", "'SelectItem' is defined but never used.", "'SelectTrigger' is defined but never used.", "'SelectValue' is defined but never used.", "'Switch' is defined but never used.", "'Textarea' is defined but never used.", "'refetch' is assigned a value but never used.", "'createBrand' is assigned a value but never used.", "'update<PERSON>rand' is assigned a value but never used.", "'mutationLoading' is assigned a value but never used.", "'newBrand' is assigned a value but never used.", "'setNewBrand' is assigned a value but never used.", "'editingBrandId' is assigned a value but never used.", "'setEditingBrandId' is assigned a value but never used.", "'editForm' is assigned a value but never used.", "'setEditForm' is assigned a value but never used.", "'showAddForm' is assigned a value but never used.", "'setShowAddForm' is assigned a value but never used.", "'viewMode' is assigned a value but never used.", "'setViewMode' is assigned a value but never used.", "'sortBy' is assigned a value but never used.", "'setSortBy' is assigned a value but never used.", {"range": "3936", "text": "3543"}, {"range": "3937", "text": "3543"}, {"range": "3938", "text": "3939"}, "Classname 'transform' is not needed in Tailwind CSS v3!", "classnameNotNeeded", {"range": "3940", "text": "3941"}, {"range": "3942", "text": "3543"}, {"range": "3943", "text": "3944"}, {"range": "3945", "text": "3597"}, {"range": "3946", "text": "3543"}, {"range": "3947", "text": "3543"}, {"range": "3948", "text": "3543"}, "'Filter' is defined but never used.", "'Globe' is defined but never used.", "'RefreshCw' is defined but never used.", {"range": "3949", "text": "3573"}, {"range": "3950", "text": "3569"}, {"range": "3951", "text": "3589"}, {"range": "3952", "text": "3633"}, {"range": "3953", "text": "3758"}, ["3954", "3955"], {"range": "3956", "text": "3597"}, {"range": "3957", "text": "3597"}, {"range": "3958", "text": "3543"}, {"range": "3959", "text": "3543"}, {"range": "3960", "text": "3543"}, {"range": "3961", "text": "3543"}, {"range": "3962", "text": "3543"}, {"range": "3963", "text": "3964"}, {"range": "3965", "text": "3597"}, {"range": "3966", "text": "3543"}, {"range": "3967", "text": "3543"}, {"range": "3968", "text": "3543"}, {"range": "3969", "text": "3543"}, {"range": "3970", "text": "3543"}, {"range": "3971", "text": "3543"}, {"range": "3972", "text": "3571"}, "'Pencil' is defined but never used.", "'SortAsc' is defined but never used.", "'SortDesc' is defined but never used.", "prefer-const", "'l' is never reassigned. Use 'const' instead.", "Identifier", "useConst", ["3973", "3974"], {"range": "3975", "text": "3703"}, {"range": "3976", "text": "3573"}, {"range": "3977", "text": "3569"}, {"range": "3978", "text": "3661"}, {"range": "3979", "text": "3758"}, ["3980", "3981"], {"range": "3982", "text": "3597"}, {"range": "3983", "text": "3597"}, {"range": "3984", "text": "3543"}, ["3985", "3986"], ["3987", "3988"], {"range": "3989", "text": "3990"}, ["3991", "3992"], ["3993", "3994"], {"range": "3995", "text": "3543"}, {"range": "3996", "text": "3543"}, {"range": "3997", "text": "3998"}, {"range": "3999", "text": "3543"}, {"range": "4000", "text": "3543"}, {"range": "4001", "text": "4002"}, {"range": "4003", "text": "3597"}, {"range": "4004", "text": "3543"}, {"range": "4005", "text": "3543"}, {"range": "4006", "text": "3543"}, {"range": "4007", "text": "3543"}, "'Calendar' is defined but never used.", "'MapPin' is defined but never used.", "'Phone' is defined but never used.", "'Separator' is defined but never used.", ["4008"], ["4009"], {"range": "4010", "text": "3597"}, {"range": "4011", "text": "3760"}, {"range": "4012", "text": "3597"}, {"range": "4013", "text": "3597"}, {"range": "4014", "text": "3597"}, {"range": "4015", "text": "3543"}, {"range": "4016", "text": "3543"}, {"range": "4017", "text": "3543"}, {"range": "4018", "text": "3543"}, {"range": "4019", "text": "3569"}, {"range": "4020", "text": "3573"}, {"range": "4021", "text": "3589"}, {"range": "4022", "text": "3633"}, {"range": "4023", "text": "3569"}, {"range": "4024", "text": "3573"}, {"range": "4025", "text": "4026"}, {"range": "4027", "text": "3569"}, {"range": "4028", "text": "3772"}, ["4029", "4030", "4031", "4032"], {"range": "4033", "text": "3569"}, {"range": "4034", "text": "3589"}, {"range": "4035", "text": "3754"}, {"range": "4036", "text": "3543"}, {"range": "4037", "text": "3543"}, {"range": "4038", "text": "3543"}, {"range": "4039", "text": "3543"}, ["4040"], ["4041"], {"range": "4042", "text": "4043"}, {"range": "4044", "text": "4045"}, {"range": "4046", "text": "4045"}, {"range": "4047", "text": "4045"}, {"range": "4048", "text": "3925"}, {"range": "4049", "text": "4045"}, {"range": "4050", "text": "4051"}, {"range": "4052", "text": "3597"}, {"range": "4053", "text": "3543"}, {"range": "4054", "text": "3543"}, {"range": "4055", "text": "3543"}, {"range": "4056", "text": "3543"}, {"range": "4057", "text": "3758"}, {"range": "4058", "text": "3543"}, {"range": "4059", "text": "4060"}, {"range": "4061", "text": "3543"}, {"range": "4062", "text": "3571"}, {"range": "4063", "text": "3571"}, {"range": "4064", "text": "3571"}, {"range": "4065", "text": "3571"}, "'Plus' is defined but never used.", {"range": "4066", "text": "3543"}, {"range": "4067", "text": "3543"}, {"range": "4068", "text": "3543"}, {"range": "4069", "text": "3543"}, {"range": "4070", "text": "4051"}, {"range": "4071", "text": "3597"}, {"range": "4072", "text": "4051"}, {"range": "4073", "text": "4051"}, {"range": "4074", "text": "3597"}, "'FrontendBrand' is defined but never used.", {"range": "4075", "text": "3543"}, {"range": "4076", "text": "3543"}, {"range": "4077", "text": "3543"}, {"range": "4078", "text": "3597"}, {"range": "4079", "text": "3597"}, "'Image' is defined but never used.", "'Mail' is defined but never used.", "'Package' is defined but never used.", {"range": "4080", "text": "3573"}, {"range": "4081", "text": "3569"}, {"range": "4082", "text": "3589"}, {"range": "4083", "text": "3633"}, {"range": "4084", "text": "3758"}, {"range": "4085", "text": "3597"}, {"range": "4086", "text": "3597"}, {"range": "4087", "text": "3597"}, {"range": "4088", "text": "3597"}, {"range": "4089", "text": "3543"}, {"range": "4090", "text": "3573"}, {"range": "4091", "text": "3543"}, {"range": "4092", "text": "3543"}, {"range": "4093", "text": "4094"}, {"range": "4095", "text": "3571"}, {"range": "4096", "text": "3597"}, {"range": "4097", "text": "3543"}, {"range": "4098", "text": "3543"}, {"range": "4099", "text": "3543"}, {"range": "4100", "text": "3543"}, {"range": "4101", "text": "3543"}, {"range": "4102", "text": "3543"}, {"range": "4103", "text": "4104"}, {"range": "4105", "text": "3571"}, {"range": "4106", "text": "3571"}, {"range": "4107", "text": "3597"}, {"range": "4108", "text": "3597"}, {"range": "4109", "text": "4110"}, {"range": "4111", "text": "3543"}, ["4112", "4113"], {"range": "4114", "text": "3573"}, {"range": "4115", "text": "3543"}, {"range": "4116", "text": "3543"}, ["4117", "4118"], ["4119", "4120"], {"range": "4121", "text": "4094"}, {"range": "4122", "text": "3571"}, {"range": "4123", "text": "3597"}, {"range": "4124", "text": "3543"}, {"range": "4125", "text": "3543"}, {"range": "4126", "text": "3543"}, {"range": "4127", "text": "3543"}, {"range": "4128", "text": "3543"}, {"range": "4129", "text": "3543"}, {"range": "4130", "text": "4104"}, {"range": "4131", "text": "3571"}, {"range": "4132", "text": "3571"}, "'Eye' is defined but never used.", "'FolderOpen' is defined but never used.", "'MoreHorizontal' is defined but never used.", "'CardHeader' is defined but never used.", "'CardTitle' is defined but never used.", "'DropdownMenu' is defined but never used.", "'DropdownMenuContent' is defined but never used.", "'DropdownMenuItem' is defined but never used.", "'DropdownMenuSeparator' is defined but never used.", "'DropdownMenuTrigger' is defined but never used.", "'transformCategory' is assigned a value but never used.", "'filteredAndSortedCategories' is assigned a value but never used.", "'handleAddCategory' is assigned a value but never used.", {"range": "4133", "text": "3569"}, {"range": "4134", "text": "3573"}, {"range": "4135", "text": "3633"}, {"range": "4136", "text": "3589"}, {"range": "4137", "text": "3758"}, {"range": "4138", "text": "3597"}, {"range": "4139", "text": "3597"}, {"range": "4140", "text": "3597"}, {"range": "4141", "text": "3597"}, {"range": "4142", "text": "3543"}, {"range": "4143", "text": "3543"}, {"range": "4144", "text": "3543"}, {"range": "4145", "text": "3597"}, {"range": "4146", "text": "3597"}, "'FrontendCategory' is defined but never used.", "'generateSlug' is assigned a value but never used.", ["4147"], ["4148"], {"range": "4149", "text": "4150"}, {"range": "4151", "text": "4152"}, {"range": "4153", "text": "4154"}, {"range": "4155", "text": "3569"}, {"range": "4156", "text": "3573"}, {"range": "4157", "text": "3633"}, {"range": "4158", "text": "3589"}, {"range": "4159", "text": "3758"}, {"range": "4160", "text": "3597"}, {"range": "4161", "text": "3597"}, {"range": "4162", "text": "3597"}, {"range": "4163", "text": "3597"}, {"range": "4164", "text": "3543"}, {"range": "4165", "text": "3543"}, {"range": "4166", "text": "3569"}, {"range": "4167", "text": "3543"}, {"range": "4168", "text": "3543"}, ["4169"], {"range": "4170", "text": "4094"}, {"range": "4171", "text": "3571"}, {"range": "4172", "text": "3597"}, {"range": "4173", "text": "3543"}, {"range": "4174", "text": "3543"}, {"range": "4175", "text": "3543"}, {"range": "4176", "text": "3543"}, {"range": "4177", "text": "3543"}, {"range": "4178", "text": "4179"}, ["4180"], {"range": "4181", "text": "3597"}, {"range": "4182", "text": "3597"}, {"range": "4183", "text": "4179"}, {"range": "4184", "text": "3597"}, {"range": "4185", "text": "3597"}, {"range": "4186", "text": "4110"}, {"range": "4187", "text": "3543"}, "'ChevronDown' is defined but never used.", "'ChevronUp' is defined but never used.", "'editingColorId' is assigned a value but never used.", "'setEditingColorId' is assigned a value but never used.", {"range": "4188", "text": "3647"}, {"range": "4189", "text": "3548"}, {"range": "4190", "text": "3643"}, {"range": "4191", "text": "4192"}, {"range": "4193", "text": "3651"}, {"range": "4194", "text": "3758"}, {"range": "4195", "text": "3543"}, {"range": "4196", "text": "3597"}, {"range": "4197", "text": "3543"}, {"range": "4198", "text": "3639"}, {"range": "4199", "text": "3543"}, {"range": "4200", "text": "3543"}, {"range": "4201", "text": "4002"}, {"range": "4202", "text": "4094"}, {"range": "4203", "text": "3597"}, {"range": "4204", "text": "3543"}, {"range": "4205", "text": "3543"}, {"range": "4206", "text": "3543"}, {"range": "4207", "text": "3543"}, {"range": "4208", "text": "4110"}, {"range": "4209", "text": "3543"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'addSampleData'. Either include it or remove the dependency array.", "ArrayExpression", ["4210"], {"range": "4211", "text": "4212"}, "'Save' is defined but never used.", "'Sparkles' is defined but never used.", "'TrendingUp' is defined but never used.", "'handleAddMaterial' is assigned a value but never used.", "'handleEditCancel' is assigned a value but never used.", "'handleEditSave' is assigned a value but never used.", {"range": "4213", "text": "3589"}, {"range": "4214", "text": "3573"}, {"range": "4215", "text": "3569"}, {"range": "4216", "text": "3633"}, {"range": "4217", "text": "3758"}, {"range": "4218", "text": "3597"}, {"range": "4219", "text": "3597"}, {"range": "4220", "text": "3597"}, {"range": "4221", "text": "3597"}, {"range": "4222", "text": "3543"}, {"range": "4223", "text": "3597"}, {"range": "4224", "text": "3543"}, {"range": "4225", "text": "3543"}, {"range": "4226", "text": "3543"}, {"range": "4227", "text": "3543"}, {"range": "4228", "text": "3543"}, {"range": "4229", "text": "4110"}, {"range": "4230", "text": "3543"}, {"range": "4231", "text": "3589"}, {"range": "4232", "text": "3573"}, {"range": "4233", "text": "3569"}, {"range": "4234", "text": "3633"}, {"range": "4235", "text": "3758"}, ["4236", "4237"], {"range": "4238", "text": "3597"}, {"range": "4239", "text": "3597"}, {"range": "4240", "text": "3543"}, ["4241", "4242"], {"range": "4243", "text": "3543"}, {"range": "4244", "text": "3543"}, {"range": "4245", "text": "3543"}, {"range": "4246", "text": "3543"}, {"range": "4247", "text": "3597"}, {"range": "4248", "text": "3543"}, {"range": "4249", "text": "3543"}, {"range": "4250", "text": "3543"}, {"range": "4251", "text": "3543"}, "'CheckCircle' is defined but never used.", "'Clock' is defined but never used.", ["4252"], ["4253"], ["4254"], {"range": "4255", "text": "3597"}, {"range": "4256", "text": "3597"}, {"range": "4257", "text": "3597"}, {"range": "4258", "text": "3597"}, {"range": "4259", "text": "3597"}, {"range": "4260", "text": "3543"}, {"range": "4261", "text": "3543"}, {"range": "4262", "text": "3543"}, {"range": "4263", "text": "3569"}, {"range": "4264", "text": "3573"}, {"range": "4265", "text": "3589"}, {"range": "4266", "text": "3569"}, {"range": "4267", "text": "4268"}, "Classnames 'h-full, w-full' could be replaced by the 'size-full' shorthand!", {"range": "4269", "text": "4270"}, {"range": "4271", "text": "4272"}, {"range": "4273", "text": "3569"}, {"range": "4274", "text": "4026"}, {"range": "4275", "text": "3569"}, {"range": "4276", "text": "3925"}, {"range": "4277", "text": "3925"}, {"range": "4278", "text": "3573"}, {"range": "4279", "text": "3589"}, {"range": "4280", "text": "4281"}, {"range": "4282", "text": "3633"}, {"range": "4283", "text": "3925"}, ["4284"], ["4285"], {"range": "4286", "text": "4287"}, {"range": "4288", "text": "4289"}, {"range": "4290", "text": "4291"}, {"range": "4292", "text": "4270"}, {"range": "4293", "text": "3925"}, {"range": "4294", "text": "4295"}, {"range": "4296", "text": "4051"}, {"range": "4297", "text": "3597"}, {"range": "4298", "text": "3543"}, {"range": "4299", "text": "3543"}, {"range": "4300", "text": "3543"}, {"range": "4301", "text": "3758"}, {"range": "4302", "text": "3543"}, {"range": "4303", "text": "3543"}, {"range": "4304", "text": "3543"}, {"range": "4305", "text": "3543"}, {"range": "4306", "text": "3543"}, {"range": "4307", "text": "3543"}, {"range": "4308", "text": "3597"}, {"range": "4309", "text": "4051"}, {"range": "4310", "text": "4051"}, {"range": "4311", "text": "4051"}, {"range": "4312", "text": "4051"}, {"range": "4313", "text": "3597"}, {"range": "4314", "text": "4315"}, {"range": "4316", "text": "4317"}, {"range": "4318", "text": "4272"}, "'FormNavigation' is defined but never used.", "'MetadataGuide' is defined but never used.", "'SectionNavigator' is defined but never used.", {"range": "4319", "text": "4320"}, {"range": "4321", "text": "4322"}, {"range": "4323", "text": "4324"}, {"range": "4325", "text": "4326"}, {"range": "4327", "text": "4328"}, {"range": "4329", "text": "3597"}, {"range": "4330", "text": "3543"}, {"range": "4331", "text": "3543"}, "Classnames 'h-24, w-24' could be replaced by the 'size-24' shorthand!", {"range": "4332", "text": "4333"}, {"range": "4334", "text": "4335"}, {"range": "4336", "text": "3569"}, {"range": "4337", "text": "4338"}, {"range": "4339", "text": "4340"}, {"range": "4341", "text": "4094"}, {"range": "4342", "text": "4343"}, {"range": "4344", "text": "4343"}, {"range": "4345", "text": "4343"}, {"range": "4346", "text": "4343"}, {"range": "4347", "text": "4343"}, {"range": "4348", "text": "4343"}, {"range": "4349", "text": "4343"}, {"range": "4350", "text": "3597"}, {"range": "4351", "text": "3543"}, ["4352"], ["4353"], ["4354"], ["4355"], {"range": "4356", "text": "4357"}, {"range": "4358", "text": "3543"}, ["4359"], ["4360"], ["4361"], ["4362"], {"range": "4363", "text": "4364"}, {"range": "4365", "text": "3639"}, {"range": "4366", "text": "4367"}, {"range": "4368", "text": "4367"}, {"range": "4369", "text": "4367"}, "'Star' is defined but never used.", "'Input' is defined but never used.", "'errors' is defined but never used.", {"range": "4370", "text": "3661"}, {"range": "4371", "text": "3569"}, {"range": "4372", "text": "3597"}, {"range": "4373", "text": "4374"}, {"range": "4375", "text": "3633"}, {"range": "4376", "text": "3597"}, {"range": "4377", "text": "4378"}, {"range": "4379", "text": "3589"}, {"range": "4380", "text": "4381"}, {"range": "4382", "text": "3573"}, {"range": "4383", "text": "3661"}, {"range": "4384", "text": "4281"}, {"range": "4385", "text": "4281"}, {"range": "4386", "text": "3639"}, {"range": "4387", "text": "3647"}, {"range": "4388", "text": "3651"}, {"range": "4389", "text": "4192"}, "'Info' is defined but never used.", "'register' is defined but never used.", {"range": "4390", "text": "3573"}, {"range": "4391", "text": "3573"}, {"range": "4392", "text": "3749"}, {"range": "4393", "text": "3569"}, {"range": "4394", "text": "3543"}, {"range": "4395", "text": "3543"}, {"range": "4396", "text": "4374"}, {"range": "4397", "text": "3639"}, {"range": "4398", "text": "3643"}, {"range": "4399", "text": "3647"}, {"range": "4400", "text": "4192"}, {"range": "4401", "text": "3569"}, {"range": "4402", "text": "3569"}, {"range": "4403", "text": "4374"}, {"range": "4404", "text": "3573"}, {"range": "4405", "text": "3597"}, {"range": "4406", "text": "3597"}, {"range": "4407", "text": "3543"}, {"range": "4408", "text": "4409"}, {"range": "4410", "text": "3597"}, {"range": "4411", "text": "3597"}, {"range": "4412", "text": "3597"}, {"range": "4413", "text": "3597"}, {"range": "4414", "text": "3597"}, {"range": "4415", "text": "4416"}, {"range": "4417", "text": "4374"}, {"range": "4418", "text": "3633"}, {"range": "4419", "text": "4378"}, {"range": "4420", "text": "4421"}, {"range": "4422", "text": "3597"}, {"range": "4423", "text": "3597"}, {"range": "4424", "text": "3597"}, {"range": "4425", "text": "3597"}, {"range": "4426", "text": "4427"}, {"range": "4428", "text": "4427"}, {"range": "4429", "text": "4427"}, {"range": "4430", "text": "4427"}, {"range": "4431", "text": "3589"}, {"range": "4432", "text": "4433"}, {"range": "4434", "text": "4435"}, "'Boxes' is defined but never used.", "'Category' is defined but never used.", {"range": "4436", "text": "3633"}, {"range": "4437", "text": "3569"}, {"range": "4438", "text": "3597"}, {"range": "4439", "text": "4374"}, {"range": "4440", "text": "3573"}, {"range": "4441", "text": "3597"}, {"range": "4442", "text": "3597"}, {"range": "4443", "text": "4444"}, {"range": "4445", "text": "3703"}, {"range": "4446", "text": "3597"}, {"range": "4447", "text": "4448"}, {"range": "4449", "text": "3597"}, {"range": "4450", "text": "4451"}, {"range": "4452", "text": "4409"}, {"range": "4453", "text": "4416"}, {"range": "4454", "text": "4455"}, {"range": "4456", "text": "3589"}, {"range": "4457", "text": "3597"}, {"range": "4458", "text": "4381"}, {"range": "4459", "text": "4460"}, {"range": "4461", "text": "3597"}, {"range": "4462", "text": "4463"}, {"range": "4464", "text": "4433"}, {"range": "4465", "text": "4435"}, "'startUpload' is assigned a value but only used as a type.", "usedOnlyAsType", ["4466"], "'ShoppingCart' is defined but never used.", "'Truck' is defined but never used.", "'Checkbox' is defined but never used.", {"range": "4467", "text": "3589"}, {"range": "4468", "text": "3633"}, {"range": "4469", "text": "4320"}, {"range": "4470", "text": "4322"}, {"range": "4471", "text": "4472"}, {"range": "4473", "text": "4474"}, {"range": "4475", "text": "4476"}, {"range": "4477", "text": "4367"}, {"range": "4478", "text": "4479"}, {"range": "4480", "text": "4322"}, {"range": "4481", "text": "4320"}, {"range": "4482", "text": "4483"}, {"range": "4484", "text": "4444"}, {"range": "4485", "text": "4374"}, {"range": "4486", "text": "4381"}, {"range": "4487", "text": "4488"}, {"range": "4489", "text": "4435"}, {"range": "4490", "text": "4491"}, {"range": "4492", "text": "4493"}, {"range": "4494", "text": "4374"}, {"range": "4495", "text": "4444"}, {"range": "4496", "text": "4497"}, {"range": "4498", "text": "3569"}, {"range": "4499", "text": "3749"}, {"range": "4500", "text": "3573"}, {"range": "4501", "text": "3597"}, {"range": "4502", "text": "3597"}, {"range": "4503", "text": "3597"}, {"range": "4504", "text": "3589"}, {"range": "4505", "text": "3639"}, {"range": "4506", "text": "3647"}, {"range": "4507", "text": "4192"}, {"range": "4508", "text": "3643"}, "'setValue' is defined but never used.", "'watch' is defined but never used.", {"range": "4509", "text": "3703"}, "jsx-a11y/alt-text", "Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "JSXOpeningElement", {"range": "4510", "text": "3703"}, {"range": "4511", "text": "4451"}, {"range": "4512", "text": "4513"}, {"range": "4514", "text": "4513"}, {"range": "4515", "text": "4513"}, {"range": "4516", "text": "4513"}, {"range": "4517", "text": "3589"}, {"range": "4518", "text": "4381"}, {"range": "4519", "text": "3573"}, {"range": "4520", "text": "4444"}, {"range": "4521", "text": "3639"}, {"range": "4522", "text": "4513"}, {"range": "4523", "text": "4192"}, {"range": "4524", "text": "3573"}, {"range": "4525", "text": "3569"}, {"range": "4526", "text": "3569"}, {"range": "4527", "text": "3647"}, {"range": "4528", "text": "3735"}, {"range": "4529", "text": "3543"}, {"range": "4530", "text": "4531"}, "'BarChart3' is defined but never used.", "'MousePointer' is defined but never used.", "'Zap' is defined but never used.", "'focusKeyword' is assigned a value but never used.", {"range": "4532", "text": "3573"}, {"range": "4533", "text": "3573"}, {"range": "4534", "text": "3569"}, {"range": "4535", "text": "3589"}, {"range": "4536", "text": "4381"}, {"range": "4537", "text": "3639"}, {"range": "4538", "text": "3643"}, {"range": "4539", "text": "3647"}, "'Building' is defined but never used.", "'Home' is defined but never used.", "'Ruler' is defined but never used.", "'Weight' is defined but never used.", "'dimensionUnitValues' is defined but never used.", "'weightUnitValues' is defined but never used.", {"range": "4540", "text": "3569"}, {"range": "4541", "text": "3569"}, {"range": "4542", "text": "3589"}, {"range": "4543", "text": "3597"}, {"range": "4544", "text": "3597"}, {"range": "4545", "text": "3573"}, "'CreditCard' is defined but never used.", {"range": "4546", "text": "3569"}, {"range": "4547", "text": "3569"}, {"range": "4548", "text": "4474"}, {"range": "4549", "text": "4322"}, {"range": "4550", "text": "3647"}, {"range": "4551", "text": "4281"}, {"range": "4552", "text": "3573"}, {"range": "4553", "text": "4474"}, {"range": "4554", "text": "4320"}, {"range": "4555", "text": "3643"}, {"range": "4556", "text": "4281"}, {"range": "4557", "text": "3589"}, {"range": "4558", "text": "3639"}, {"range": "4559", "text": "3647"}, {"range": "4560", "text": "3643"}, "'useState' is defined but never used.", ["4561"], ["4562"], ["4563"], {"range": "4564", "text": "3543"}, {"range": "4565", "text": "3543"}, {"range": "4566", "text": "3543"}, {"range": "4567", "text": "3543"}, {"range": "4568", "text": "3597"}, {"range": "4569", "text": "3543"}, {"range": "4570", "text": "3543"}, {"range": "4571", "text": "3543"}, "'ProductSectionProps' is defined but never used.", "'onEditToggle' is defined but never used.", "'onSave' is defined but never used.", "'onCancel' is defined but never used.", "'onProductUpdate' is defined but never used.", "'editedProduct' is assigned a value but never used.", "'handleInputChange' is assigned a value but never used.", {"range": "4572", "text": "3639"}, {"range": "4573", "text": "4094"}, {"range": "4574", "text": "3597"}, {"range": "4575", "text": "3597"}, {"range": "4576", "text": "3597"}, {"range": "4577", "text": "3585"}, ["4578", "4579", "4580", "4581"], ["4582", "4583", "4584", "4585"], {"range": "4586", "text": "3543"}, "'isLoading' is assigned a value but never used.", {"range": "4587", "text": "4588"}, {"range": "4589", "text": "3543"}, {"range": "4590", "text": "4591"}, {"range": "4592", "text": "4593"}, {"range": "4594", "text": "3571"}, {"range": "4595", "text": "4596"}, {"range": "4597", "text": "3571"}, {"range": "4598", "text": "4599"}, {"range": "4600", "text": "4601"}, "'Edit' is defined but never used.", "'Button' is defined but never used.", {"range": "4602", "text": "3749"}, {"range": "4603", "text": "3639"}, {"range": "4604", "text": "4094"}, {"range": "4605", "text": "3571"}, {"range": "4606", "text": "3571"}, {"range": "4607", "text": "3571"}, {"range": "4608", "text": "3639"}, {"range": "4609", "text": "3571"}, {"range": "4610", "text": "3571"}, ["4611"], {"range": "4612", "text": "3639"}, {"range": "4613", "text": "4094"}, {"range": "4614", "text": "3585"}, {"range": "4615", "text": "3585"}, {"range": "4616", "text": "3585"}, {"range": "4617", "text": "3585"}, {"range": "4618", "text": "3543"}, {"range": "4619", "text": "4045"}, {"range": "4620", "text": "4094"}, {"range": "4621", "text": "4051"}, {"range": "4622", "text": "3597"}, {"range": "4623", "text": "3543"}, {"range": "4624", "text": "3543"}, {"range": "4625", "text": "3543"}, "'useEffect' is defined but never used.", {"range": "4626", "text": "3758"}, {"range": "4627", "text": "4628"}, {"range": "4629", "text": "3543"}, {"range": "4630", "text": "3597"}, {"range": "4631", "text": "3597"}, {"range": "4632", "text": "3543"}, {"range": "4633", "text": "4634"}, {"range": "4635", "text": "3597"}, {"range": "4636", "text": "3585"}, {"range": "4637", "text": "3543"}, ["4638", "4639"], ["4640", "4641"], ["4642", "4643"], {"range": "4644", "text": "4628"}, {"range": "4645", "text": "3543"}, {"range": "4646", "text": "3543"}, {"range": "4647", "text": "3543"}, {"range": "4648", "text": "3597"}, {"range": "4649", "text": "4051"}, {"range": "4650", "text": "4051"}, {"range": "4651", "text": "4051"}, {"range": "4652", "text": "4051"}, {"range": "4653", "text": "3597"}, "'Percent' is defined but never used.", "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "Classnames 'w-16, h-16' could be replaced by the 'size-16' shorthand!", {"range": "4654", "text": "4655"}, {"range": "4656", "text": "4094"}, {"range": "4657", "text": "3571"}, {"range": "4658", "text": "4094"}, {"range": "4659", "text": "3758"}, {"range": "4660", "text": "4110"}, {"range": "4661", "text": "3543"}, {"range": "4662", "text": "3543"}, {"range": "4663", "text": "3543"}, {"range": "4664", "text": "3543"}, {"range": "4665", "text": "3543"}, {"range": "4666", "text": "3643"}, {"range": "4667", "text": "4531"}, {"range": "4668", "text": "4281"}, {"range": "4669", "text": "3749"}, {"range": "4670", "text": "3633"}, {"range": "4671", "text": "3633"}, {"range": "4672", "text": "3577"}, "'Promotion' is defined but never used.", {"range": "4673", "text": "3647"}, {"range": "4674", "text": "3643"}, {"range": "4675", "text": "4192"}, {"range": "4676", "text": "3651"}, {"range": "4677", "text": "4281"}, {"range": "4678", "text": "4045"}, {"range": "4679", "text": "4045"}, {"range": "4680", "text": "3597"}, {"range": "4681", "text": "3543"}, {"range": "4682", "text": "3543"}, {"range": "4683", "text": "3543"}, {"range": "4684", "text": "3543"}, {"range": "4685", "text": "3543"}, {"range": "4686", "text": "3543"}, {"range": "4687", "text": "3597"}, {"range": "4688", "text": "3597"}, {"range": "4689", "text": "3597"}, {"range": "4690", "text": "3597"}, {"range": "4691", "text": "4110"}, {"range": "4692", "text": "4110"}, ["4693", "4694", "4695", "4696"], ["4697", "4698", "4699", "4700"], {"range": "4701", "text": "3543"}, {"range": "4702", "text": "3573"}, {"range": "4703", "text": "3754"}, {"range": "4704", "text": "3735"}, {"range": "4705", "text": "3661"}, {"range": "4706", "text": "3569"}, {"range": "4707", "text": "3661"}, {"range": "4708", "text": "3543"}, {"range": "4709", "text": "3597"}, {"range": "4710", "text": "3543"}, {"range": "4711", "text": "3887"}, {"range": "4712", "text": "4713"}, {"range": "4714", "text": "4715"}, {"range": "4716", "text": "3543"}, {"range": "4717", "text": "3543"}, {"range": "4718", "text": "3543"}, {"range": "4719", "text": "3758"}, {"range": "4720", "text": "3543"}, {"range": "4721", "text": "3543"}, {"range": "4722", "text": "3543"}, {"range": "4723", "text": "4060"}, {"range": "4724", "text": "3543"}, {"range": "4725", "text": "3543"}, {"range": "4726", "text": "3543"}, {"range": "4727", "text": "3543"}, {"range": "4728", "text": "3643"}, {"range": "4729", "text": "4531"}, {"range": "4730", "text": "4281"}, {"range": "4731", "text": "4281"}, {"range": "4732", "text": "3639"}, {"range": "4733", "text": "4734"}, {"range": "4735", "text": "4736"}, {"range": "4737", "text": "4738"}, {"range": "4739", "text": "4740"}, {"range": "4741", "text": "3639"}, "'Transaction' is defined but never used.", {"range": "4742", "text": "3597"}, {"range": "4743", "text": "3597"}, {"range": "4744", "text": "3597"}, {"range": "4745", "text": "3597"}, {"range": "4746", "text": "3597"}, {"range": "4747", "text": "3597"}, {"range": "4748", "text": "3543"}, {"range": "4749", "text": "3543"}, {"range": "4750", "text": "3543"}, {"range": "4751", "text": "3543"}, {"range": "4752", "text": "4753"}, "CallExpression", {"range": "4754", "text": "4755"}, {"range": "4756", "text": "4757"}, {"range": "4758", "text": "4759"}, "Classnames 'h-7, w-7' could be replaced by the 'size-7' shorthand!", {"range": "4760", "text": "4761"}, {"range": "4762", "text": "4763"}, {"range": "4764", "text": "3597"}, {"range": "4765", "text": "3597"}, {"range": "4766", "text": "4767"}, {"range": "4768", "text": "3597"}, {"range": "4769", "text": "3597"}, {"range": "4770", "text": "3597"}, {"range": "4771", "text": "4772"}, {"range": "4773", "text": "3735"}, {"range": "4774", "text": "4775"}, "tailwindcss/no-unnecessary-arbitrary-value", "The arbitrary class 'left-[50%]' could be replaced by 'left-1/2' or 'left-2/4'", "unnecessaryArbitraryValueDetected", "The arbitrary class 'top-[50%]' could be replaced by 'top-1/2' or 'top-2/4'", "The arbitrary class 'translate-x-[-50%]' could be replaced by '-translate-x-1/2' or '-translate-x-2/4'", "The arbitrary class 'translate-y-[-50%]' could be replaced by '-translate-y-1/2' or '-translate-y-2/4'", {"range": "4776", "text": "3597"}, {"range": "4777", "text": "4778"}, "The arbitrary class 'min-w-[8rem]' could be replaced by 'min-w-32'", {"range": "4779", "text": "4780"}, {"range": "4781", "text": "4782"}, "Classnames 'h-3.5, w-3.5' could be replaced by the 'size-3.5' shorthand!", {"range": "4783", "text": "4784"}, {"range": "4785", "text": "3597"}, {"range": "4786", "text": "4784"}, {"range": "4787", "text": "4788"}, {"range": "4789", "text": "4790"}, {"range": "4791", "text": "4792"}, {"range": "4793", "text": "4794"}, ["4795", "4796", "4797", "4798"], ["4799", "4800", "4801", "4802"], {"range": "4803", "text": "4804"}, {"range": "4805", "text": "4806"}, {"range": "4807", "text": "4808"}, "The arbitrary class 'p-[1px]' could be replaced by 'p-px'", {"range": "4809", "text": "4810"}, {"range": "4811", "text": "4812"}, {"range": "4813", "text": "4814"}, {"range": "4815", "text": "3597"}, {"range": "4816", "text": "3597"}, {"range": "4817", "text": "4818"}, {"range": "4819", "text": "4820"}, {"range": "4821", "text": "3597"}, "The arbitrary class 'h-[1px]' could be replaced by 'h-px'", {"range": "4822", "text": "4823"}, "The arbitrary class 'w-[1px]' could be replaced by 'w-px'", {"range": "4824", "text": "4825"}, {"range": "4826", "text": "4827"}, {"range": "4828", "text": "4829"}, ["4830"], ["4831"], ["4832", "4833"], ["4834", "4835"], ["4836", "4837"], ["4838", "4839"], ["4840"], ["4841", "4842"], ["4843"], ["4844"], ["4845"], ["4846"], ["4847"], ["4848", "4849"], ["4850", "4851"], ["4852", "4853"], ["4854", "4855"], ["4856", "4857"], ["4858", "4859"], ["4860", "4861"], ["4862", "4863"], ["4864"], ["4865"], ["4866", "4867"], ["4868"], ["4869", "4870"], ["4871", "4872"], ["4873", "4874"], ["4875", "4876"], ["4877", "4878"], ["4879", "4880"], ["4881"], ["4882"], ["4883"], ["4884"], ["4885"], ["4886"], ["4887"], ["4888"], ["4889"], ["4890"], ["4891"], ["4892"], ["4893"], ["4894"], ["4895"], ["4896"], ["4897"], ["4898"], ["4899"], ["4900", "4901"], ["4902", "4903"], ["4904", "4905"], ["4906"], ["4907"], ["4908"], ["4909"], ["4910"], ["4911"], ["4912"], ["4913"], ["4914"], ["4915"], ["4916"], ["4917"], ["4918"], ["4919"], ["4920"], ["4921"], ["4922"], ["4923"], ["4924"], "'error' is defined but never used.", ["4925"], ["4926"], ["4927"], ["4928"], ["4929"], ["4930"], ["4931"], ["4932"], ["4933", "4934"], [4125, 4137], "mr-2 size-4", [4268, 4280], [4843, 4872], "size-4 text-muted-foreground", [5235, 5262], "mr-1 size-3 text-green-500", [5334, 5359], "mr-1 size-3 text-red-500", [5636, 5660], "size-16 text-green-500", [6044, 6073], [6435, 6462], [6534, 6559], [6790, 6813], "size-16 text-blue-500", [7198, 7227], [7592, 7619], [7691, 7716], [7948, 7973], "size-16 text-purple-500", [8358, 8387], [8740, 8767], [8839, 8864], [9098, 9123], "size-16 text-orange-500", [9492, 9513], "size-5 text-blue-600", [11277, 11284], "size-3", [11637, 11659], "size-5 text-green-600", [11963, 11989], "relative mx-auto size-32", [13863, 13887], "`size-3 rounded-full ${", {"kind": "4935", "justification": "4936"}, {"kind": "4935", "justification": "4936"}, {"kind": "4935", "justification": "4936"}, {"fix": "4937", "messageId": "4938", "data": "4939", "desc": "4940"}, [6531, 6543], [6790, 6802], [7592, 7604], "mr-1 size-4", [7928, 7940], [8267, 8279], [9461, 9484], "size-5 text-purple-600", [11261, 11288], "size-4 rounded bg-blue-400", [11344, 11372], "size-4 rounded bg-green-400", [11428, 11457], "size-4 rounded bg-yellow-400", [11787, 11794], "size-4", [12392, 12413], [15939, 15951], [16569, 16590], [21403, 21425], [24689, 24701], [25287, 25310], [26125, 26164], "size-10 cursor-pointer rounded border", {"fix": "4941", "messageId": "4938", "data": "4942", "desc": "4940"}, [27273, 27312], [28190, 28229], [29071, 29110], [29588, 29609], [30324, 30363], [31086, 31125], [31945, 31984], [32806, 32845], [34747, 34759], [35348, 35369], [39995, 40017], [44648, 44660], [45251, 45274], [49104, 49116], [49532, 49553], [54387, 54399], [54993, 55014], [58589, 58611], [62051, 62063], {"fix": "4943", "messageId": "4938", "data": "4944", "desc": "4940"}, [2885, 2892], [3201, 3208], [3871, 3892], [6549, 6571], [8971, 8994], [10160, 10183], "size-5 text-orange-600", [12685, 12697], [2142, 2171], [2475, 2502], [2578, 2603], [3054, 3061], "size-5", [4451, 4517], "flex size-8 items-center justify-center rounded-full bg-green-100", [4566, 4588], "size-4 text-green-600", [5050, 5115], "flex size-8 items-center justify-center rounded-full bg-blue-100", [5157, 5178], "size-4 text-blue-600", [5685, 5752], "flex size-8 items-center justify-center rounded-full bg-orange-100", [5796, 5819], "size-4 text-orange-600", [6693, 6727], "size-2 rounded-full bg-yellow-500", [7111, 7142], "size-2 rounded-full bg-red-500", [7523, 7555], "size-2 rounded-full bg-blue-500", [11724, 11786], "flex size-10 items-center justify-center rounded bg-gray-100", [11832, 11853], "size-5 text-gray-600", [12529, 12591], [12637, 12658], [13324, 13386], [13432, 13453], [10440, 10452], [10585, 10597], [11767, 11788], "size-8 text-blue-600", [12473, 12494], [13201, 13223], [13986, 14009], [15081, 15104], [15805, 15911], "absolute right-0 top-0 size-64 rounded-full bg-gradient-to-br from-blue-200/30 to-purple-200/30 blur-3xl", [16646, 16662], "`size-6 text-${", [18107, 18169], "`absolute bottom-0 inset-x-0 h-1 bg-gradient-to-r from-${", [18565, 18587], [21605, 21628], [22191, 22207], "`size-4 text-${", [23061, 23104], "size-3 rounded-full border border-gray-300", [24224, 24258], "mx-auto mb-3 size-8 text-blue-600", [24720, 24732], "ml-1 size-4", [25089, 25124], "mx-auto mb-3 size-8 text-green-600", [25581, 25593], [25956, 25992], "mx-auto mb-3 size-8 text-purple-600", [26449, 26461], [26822, 26856], "mx-auto mb-3 size-8 text-pink-600", [27309, 27321], [27819, 27840], [28711, 28733], [29569, 29592], [30459, 30480], "size-5 text-pink-600", {"kind": "4935", "justification": "4936"}, {"kind": "4935", "justification": "4936"}, {"kind": "4935", "justification": "4936"}, {"kind": "4935", "justification": "4936"}, {"kind": "4935", "justification": "4936"}, {"kind": "4935", "justification": "4936"}, {"fix": "4945", "messageId": "4938", "data": "4946", "desc": "4940"}, {"fix": "4947", "messageId": "4938", "data": "4948", "desc": "4940"}, {"fix": "4949", "messageId": "4938", "data": "4950", "desc": "4940"}, {"fix": "4951", "messageId": "4938", "data": "4952", "desc": "4940"}, [5653, 5665], [5825, 5837], [6898, 6919], [7124, 7155], "relative mx-auto mb-4 size-32", [7571, 7621], "absolute bottom-0 right-0 size-8 rounded-full p-0", [7692, 7699], [7884, 7896], [9920, 9941], [13614, 13626], [14252, 14274], [15632, 15639], [15718, 15725], [16954, 16961], [17040, 17047], [18417, 18424], [18503, 18510], [18699, 18706], [19442, 19454], [19844, 19864], "size-5 text-red-600", [22985, 23008], [25023, 25044], [28087, 28110], [31195, 31218], [33135, 33147], [33717, 33740], [34563, 34570], [34854, 34861], [35150, 35157], [38124, 38146], [41179, 41200], [42724, 42736], [3430, 3441], "`size-4 ${", [3857, 3869], [4009, 4021], [4514, 4535], [5158, 5181], "size-5 text-yellow-600", [6038, 6060], [6728, 6751], [7513, 7575], "absolute left-3 top-1/2 size-4 -translate-y-1/2 text-gray-400", [10022, 10031], "size-12", [12052, 12059], [12299, 12311], [12497, 12509], [12699, 12711], [12957, 12969], [13185, 13197], [14102, 14128], "mt-1 size-4 text-gray-400", [15020, 15032], [15215, 15227], [15678, 15714], "mx-auto mb-4 size-12 text-gray-400", [803, 883], "mx-auto mb-6 flex size-20 items-center justify-center rounded-full bg-blue-100", [919, 942], "size-10 text-blue-600", [1897, 1968], "mb-3 flex size-10 items-center justify-center rounded-lg bg-green-100", [2008, 2030], [2458, 2528], "mb-3 flex size-10 items-center justify-center rounded-lg bg-blue-100", [2570, 2591], [3022, 3094], "mb-3 flex size-10 items-center justify-center rounded-lg bg-purple-100", [3139, 3162], [3592, 3664], "mb-3 flex size-10 items-center justify-center rounded-lg bg-orange-100", [3708, 3731], [4159, 4228], "mb-3 flex size-10 items-center justify-center rounded-lg bg-red-100", [4269, 4289], [4707, 4779], "mb-3 flex size-10 items-center justify-center rounded-lg bg-yellow-100", [4818, 4841], {"messageId": "4953", "data": "4954", "fix": "4955", "desc": "4956"}, {"messageId": "4953", "data": "4957", "fix": "4958", "desc": "4959"}, {"messageId": "4953", "data": "4960", "fix": "4961", "desc": "4962"}, {"messageId": "4953", "data": "4963", "fix": "4964", "desc": "4965"}, [6079, 6086], [6341, 6363], [6810, 6832], [7274, 7295], [7751, 7772], "size-5 text-gray-400", {"fix": "4966", "messageId": "4938", "data": "4967", "desc": "4968"}, [6287, 6299], [6546, 6558], [7682, 7703], [12240, 12262], [17650, 17662], [18265, 18286], [18513, 18520], [22837, 22849], [23222, 23244], [28358, 28370], [28972, 28993], [29429, 29489], "flex size-8 items-center justify-center rounded bg-blue-100", [29542, 29563], [32126, 32188], "flex size-8 items-center justify-center rounded bg-yellow-100", [32241, 32264], "size-4 text-yellow-600", [36346, 36368], [38627, 38660], "size-2 rounded-full bg-green-500", [38866, 38899], [39100, 39133], [39549, 39561], [40154, 40174], [45259, 45282], [47252, 47264], [47854, 47877], [48696, 48720], "size-10 rounded border", [49620, 49644], [50531, 50555], [52003, 52025], [52747, 52759], [53524, 53536], [55443, 55455], [55949, 55970], [63463, 63475], {"fix": "4969", "messageId": "4938", "data": "4970", "desc": "4968"}, {"fix": "4971", "messageId": "4938", "data": "4972", "desc": "4968"}, [336, 363], "container mx-auto p-4", {"messageId": "4953", "data": "4973", "fix": "4974", "desc": "4975"}, {"messageId": "4953", "data": "4976", "fix": "4977", "desc": "4978"}, {"messageId": "4953", "data": "4979", "fix": "4980", "desc": "4981"}, {"messageId": "4953", "data": "4982", "fix": "4983", "desc": "4984"}, {"messageId": "4953", "data": "4985", "fix": "4986", "desc": "4975"}, {"messageId": "4953", "data": "4987", "fix": "4988", "desc": "4978"}, {"messageId": "4953", "data": "4989", "fix": "4990", "desc": "4981"}, {"messageId": "4953", "data": "4991", "fix": "4992", "desc": "4984"}, {"messageId": "4953", "data": "4993", "fix": "4994", "desc": "4975"}, {"messageId": "4953", "data": "4995", "fix": "4996", "desc": "4978"}, {"messageId": "4953", "data": "4997", "fix": "4998", "desc": "4981"}, {"messageId": "4953", "data": "4999", "fix": "5000", "desc": "4984"}, {"messageId": "4953", "data": "5001", "fix": "5002", "desc": "4975"}, {"messageId": "4953", "data": "5003", "fix": "5004", "desc": "4978"}, {"messageId": "4953", "data": "5005", "fix": "5006", "desc": "4981"}, {"messageId": "4953", "data": "5007", "fix": "5008", "desc": "4984"}, {"messageId": "4953", "data": "5009", "fix": "5010", "desc": "4975"}, {"messageId": "4953", "data": "5011", "fix": "5012", "desc": "4978"}, {"messageId": "4953", "data": "5013", "fix": "5014", "desc": "4981"}, {"messageId": "4953", "data": "5015", "fix": "5016", "desc": "4984"}, {"messageId": "4953", "data": "5017", "fix": "5018", "desc": "4975"}, {"messageId": "4953", "data": "5019", "fix": "5020", "desc": "4978"}, {"messageId": "4953", "data": "5021", "fix": "5022", "desc": "4981"}, {"messageId": "4953", "data": "5023", "fix": "5024", "desc": "4984"}, [3156, 3191], "ml-auto size-4 rounded-full border", [5244, 5306], [8019, 8026], [11036, 11068], "mr-3 shrink-0 text-gray-400", {"messageId": "5025", "fix": "5026", "desc": "5027"}, {"messageId": "5028", "fix": "5029", "desc": "5030"}, [5371, 5383], "mx-2 size-4", [5813, 5820], [6131, 6193], [6929, 6936], [7341, 7353], [7430, 7442], "ml-2 size-4", [7858, 7870], [8173, 8185], [8715, 8726], "size-9 p-0", [8825, 8832], [9336, 9348], [9496, 9508], [9652, 9664], {"messageId": "4953", "data": "5031", "fix": "5032", "desc": "4956"}, {"messageId": "4953", "data": "5033", "fix": "5034", "desc": "4959"}, {"messageId": "4953", "data": "5035", "fix": "5036", "desc": "4962"}, {"messageId": "4953", "data": "5037", "fix": "5038", "desc": "4965"}, [10509, 10520], [10627, 10634], [10690, 10697], [10954, 10979], "relative flex size-9 p-0", [11032, 11039], [11120, 11244], "absolute -right-1 -top-1 flex size-5 items-center justify-center rounded-full bg-red-500 p-0 text-xs font-medium text-white", [12583, 12615], [13730, 13763], "relative size-9 rounded-full p-0", [15177, 15189], [15593, 15605], [16935, 16947], [17404, 17469], "absolute inset-x-0 top-full z-50 border-b bg-white shadow-lg", {"messageId": "4953", "data": "5039", "fix": "5040", "desc": "4975"}, {"messageId": "4953", "data": "5041", "fix": "5042", "desc": "4978"}, {"messageId": "4953", "data": "5043", "fix": "5044", "desc": "4981"}, {"messageId": "4953", "data": "5045", "fix": "5046", "desc": "4984"}, {"messageId": "4953", "data": "5047", "fix": "5048", "desc": "4975"}, {"messageId": "4953", "data": "5049", "fix": "5050", "desc": "4978"}, {"messageId": "4953", "data": "5051", "fix": "5052", "desc": "4981"}, {"messageId": "4953", "data": "5053", "fix": "5054", "desc": "4984"}, [17800, 17821], "size-4 text-gray-400", [18177, 18198], [18674, 18686], {"messageId": "4953", "data": "5055", "fix": "5056", "desc": "4975"}, {"messageId": "4953", "data": "5057", "fix": "5058", "desc": "4978"}, {"messageId": "4953", "data": "5059", "fix": "5060", "desc": "4981"}, {"messageId": "4953", "data": "5061", "fix": "5062", "desc": "4984"}, {"messageId": "4953", "data": "5063", "fix": "5064", "desc": "4975"}, {"messageId": "4953", "data": "5065", "fix": "5066", "desc": "4978"}, {"messageId": "4953", "data": "5067", "fix": "5068", "desc": "4981"}, {"messageId": "4953", "data": "5069", "fix": "5070", "desc": "4984"}, [4981, 4993], [5121, 5133], [7068, 7140], "absolute left-3 top-1/2 size-4 -translate-y-1/2 transform text-gray-400", [7068, 7140], "absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400", [7587, 7599], [9441, 9468], "size-4 rounded-full border", [11049, 11056], [11384, 11396], [11664, 11676], [12147, 12159], [7580, 7602], [8079, 8100], [8636, 8659], [9206, 9229], [9933, 9995], {"messageId": "5025", "fix": "5071", "desc": "5027"}, {"messageId": "5028", "fix": "5072", "desc": "5030"}, [11433, 11440], [11702, 11709], [11855, 11867], [15652, 15664], [15828, 15840], [19180, 19192], [19477, 19489], [20253, 20306], "size-8 rounded-lg border-2 border-gray-200 shadow-sm", [21572, 21579], [21909, 21921], [22191, 22203], [22485, 22497], [22909, 22921], [23507, 23519], [23944, 23956], [25462, 25469], {"messageId": "5025", "fix": "5073", "desc": "5027"}, {"messageId": "5028", "fix": "5074", "desc": "5030"}, [8500, 8521], [8997, 9019], [9490, 9511], [9980, 10001], [10625, 10687], {"messageId": "5025", "fix": "5075", "desc": "5027"}, {"messageId": "5028", "fix": "5076", "desc": "5030"}, [12802, 12809], [13071, 13078], [13224, 13236], {"messageId": "5025", "fix": "5077", "desc": "5027"}, {"messageId": "5028", "fix": "5078", "desc": "5030"}, {"messageId": "5025", "fix": "5079", "desc": "5027"}, {"messageId": "5028", "fix": "5080", "desc": "5030"}, [16349, 16429], "size-8 rounded border-2 border-gray-300 transition-colors hover:border-gray-500", {"messageId": "5025", "fix": "5081", "desc": "5027"}, {"messageId": "5028", "fix": "5082", "desc": "5030"}, {"messageId": "5025", "fix": "5083", "desc": "5027"}, {"messageId": "5028", "fix": "5084", "desc": "5030"}, [19231, 19243], [19407, 19419], [22664, 22742], "size-6 rounded border border-gray-300 transition-colors hover:border-gray-500", [23681, 23693], [24089, 24101], [24502, 24557], "size-12 rounded-lg border-2 border-gray-200 shadow-sm", [25914, 25921], [26370, 26382], [26555, 26567], [26750, 26762], [27148, 27160], {"fix": "5085", "messageId": "4938", "data": "5086", "desc": "4968"}, {"fix": "5087", "messageId": "4938", "data": "5088", "desc": "4968"}, [3226, 3233], [3377, 3386], [4430, 4437], [4696, 4703], [4923, 4930], [5145, 5157], [5302, 5314], [5501, 5513], [5675, 5687], [6175, 6196], [6797, 6819], [7393, 7416], [8151, 8174], [9129, 9150], [11339, 11361], [11944, 12011], "flex size-10 items-center justify-center rounded-full bg-blue-100", [12065, 12086], [14350, 14386], {"messageId": "4953", "data": "5089", "fix": "5090", "desc": "4956"}, {"messageId": "4953", "data": "5091", "fix": "5092", "desc": "4959"}, {"messageId": "4953", "data": "5093", "fix": "5094", "desc": "4962"}, {"messageId": "4953", "data": "5095", "fix": "5096", "desc": "4965"}, [15033, 15054], [16805, 16828], [17625, 17648], [17990, 18002], [18272, 18284], [18554, 18566], [18742, 18754], {"fix": "5097", "messageId": "4938", "data": "5098", "desc": "4968"}, {"fix": "5099", "messageId": "4938", "data": "5100", "desc": "4968"}, [3162, 3245], "absolute -bottom-1 -right-1 size-3 rounded-full border-2 border-white bg-green-500", [4514, 4535], "size-3 text-gray-400", [4719, 4740], [4925, 4946], [5441, 5462], [5940, 5961], [6362, 6373], "size-8 p-0", [6418, 6425], [6623, 6635], [6794, 6806], [6971, 6983], [7177, 7189], [1216, 1278], [1638, 1650], [1759, 1796], "ml-2 size-5 rounded-full p-0 text-xs", [4484, 4496], [4999, 5006], [5372, 5379], [5737, 5744], [6118, 6125], [648, 660], [796, 808], [945, 957], [1081, 1093], [3073, 3084], [3136, 3143], [3613, 3624], [3950, 3961], [4014, 4021], [5104, 5116], [6918, 6930], [7239, 7251], [8457, 8464], [8870, 8877], [11179, 11201], [11692, 11713], [12266, 12289], [12843, 12866], [13754, 13816], [16128, 16135], [16203, 16210], [16602, 16609], [16922, 16929], [17080, 17092], [17469, 17491], [22268, 22280], [22448, 22460], [23900, 23912], "mr-1 size-3", [24205, 24212], [25772, 25779], [26116, 26128], [26305, 26317], [26802, 26814], [27073, 27085], [27324, 27336], [27674, 27686], [28388, 28427], "size-3 fill-yellow-400 text-yellow-400", [28717, 28724], [29157, 29164], [32125, 32132], [32417, 32424], [32816, 32847], "mx-auto size-12 text-gray-400", [33446, 33458], {"messageId": "5025", "fix": "5101", "desc": "5027"}, {"messageId": "5028", "fix": "5102", "desc": "5030"}, [1146, 1168], [5457, 5469], [5621, 5633], {"messageId": "5025", "fix": "5103", "desc": "5027"}, {"messageId": "5028", "fix": "5104", "desc": "5030"}, {"messageId": "5025", "fix": "5105", "desc": "5027"}, {"messageId": "5028", "fix": "5106", "desc": "5030"}, [7035, 7047], [7308, 7315], [8743, 8750], [9010, 9022], [9183, 9195], [9640, 9652], [9887, 9899], [10118, 10130], [10441, 10453], [11103, 11142], [11408, 11415], [11812, 11819], [9347, 9368], [9868, 9890], [10453, 10476], [11048, 11071], [11976, 12038], [13730, 13737], [13805, 13812], [14204, 14211], [14524, 14531], [14682, 14694], [16745, 16757], [17082, 17094], [18036, 18043], [18476, 18483], {"fix": "5107", "messageId": "4938", "data": "5108", "desc": "4968"}, {"fix": "5109", "messageId": "4938", "data": "5110", "desc": "4968"}, [9467, 9556], "mx-auto size-12 animate-spin rounded-full border-4 border-blue-500 border-t-transparent", [10109, 10154], "mx-auto size-12 rounded-full bg-red-100 p-3", [10186, 10206], "size-6 text-red-600", [10960, 10981], [11481, 11503], [12066, 12089], [12661, 12684], [13589, 13651], [15343, 15350], [15418, 15425], [15817, 15824], [16137, 16144], [16427, 16439], [16594, 16606], [16988, 17009], [19844, 19856], [20157, 20169], {"fix": "5111", "messageId": "4938", "data": "5112", "desc": "4968"}, [23694, 23706], [24105, 24112], [25321, 25328], [25682, 25694], [25985, 25997], [26303, 26315], [26909, 26921], [27318, 27330], [27963, 27983], "size-3 rounded-full", {"fix": "5113", "messageId": "4938", "data": "5114", "desc": "4968"}, [31889, 31896], [32187, 32194], [33830, 33850], [34438, 34445], [34765, 34772], [35227, 35258], [35818, 35830], [8660, 8681], [8963, 8990], [9367, 9389], [10026, 10049], "size-4 text-purple-600", [10618, 10641], [11237, 11299], [11698, 11710], [13419, 13426], [13654, 13666], [13994, 14001], [17916, 17928], [18096, 18108], [18889, 18944], [20329, 20341], [20706, 20713], [20924, 20936], [21167, 21179], [21351, 21363], [21654, 21666], [22623, 22654], [23369, 23381], {"desc": "5115", "fix": "5116"}, [4563, 4575], "mr-2 size-5", [12814, 12837], [13332, 13354], [13913, 13934], [14484, 14507], [15226, 15288], [17621, 17628], [17696, 17703], [18095, 18102], [18415, 18422], [18573, 18585], [21091, 21098], [21410, 21422], [21583, 21595], [21766, 21778], [21997, 22009], [22325, 22337], [24381, 24412], [25157, 25169], [5660, 5683], [6162, 6184], [6658, 6679], [7150, 7173], [7801, 7863], {"messageId": "5025", "fix": "5117", "desc": "5027"}, {"messageId": "5028", "fix": "5118", "desc": "5030"}, [9257, 9264], [9526, 9533], [9679, 9691], {"messageId": "5025", "fix": "5119", "desc": "5027"}, {"messageId": "5028", "fix": "5120", "desc": "5030"}, [12329, 12341], [12508, 12520], [14730, 14742], [15141, 15153], [16730, 16737], [17195, 17207], [17380, 17392], [17575, 17587], [17979, 17991], {"fix": "5121", "messageId": "4938", "data": "5122", "desc": "4968"}, {"fix": "5123", "messageId": "4938", "data": "5124", "desc": "4968"}, {"fix": "5125", "messageId": "4938", "data": "5126", "desc": "4968"}, [3609, 3616], [5152, 5159], [5996, 6003], [6254, 6261], [6478, 6485], [6699, 6711], [6853, 6865], [7158, 7170], [7634, 7655], [8310, 8332], [9071, 9094], [10124, 10145], [10574, 10622], "size-16 overflow-hidden rounded-lg bg-gray-100", [11010, 11056], "flex size-full items-center justify-center", [11106, 11127], "size-6 text-gray-400", [14578, 14599], [14836, 14903], [14942, 14963], [15398, 15419], [15723, 15744], [16182, 16204], [17197, 17220], [17562, 17583], "size-4 text-gray-600", [18712, 18735], [19747, 19768], {"fix": "5127", "messageId": "4938", "data": "5128", "desc": "4968"}, {"fix": "5129", "messageId": "4938", "data": "5130", "desc": "4968"}, [3210, 3284], "flex size-8 shrink-0 items-center justify-center rounded-full bg-gray-100", [3317, 3338], "size-4 text-gray-500", [3897, 3981], "flex size-8 shrink-0 overflow-hidden rounded-full border-2 border-white bg-gray-100", [4325, 4371], [4415, 4436], [4595, 4725], "flex size-8 shrink-0 items-center justify-center rounded-full border-2 border-white bg-gray-200 text-xs font-medium text-gray-600", [6376, 6387], [6432, 6439], [6637, 6649], [6805, 6817], [7083, 7095], [615, 677], [966, 978], [1110, 1122], [1387, 1399], [320, 332], [453, 465], [567, 579], [1521, 1528], [1723, 1734], [1837, 1848], [1951, 1962], [2134, 2145], [2313, 2320], [1898, 1918], "size-8 rounded-full", [2994, 3009], "mx-auto size-8", [3537, 3558], [2854, 2876], "size-4 text-green-500", [2962, 2983], "size-4 text-blue-500", [3032, 3053], "size-4 text-gray-300", [3560, 3581], "size-6 text-blue-600", [4363, 4442], "size-4 animate-spin rounded-full border-2 border-blue-600 border-t-transparent", [4699, 4706], [5005, 5017], [5220, 5232], [6865, 6945], "absolute right-0 top-0 -mr-4 -mt-4 size-24 rounded-full bg-blue-200 opacity-20", [6975, 7059], "absolute bottom-0 left-0 -mb-4 -ml-4 size-16 rounded-full bg-purple-200 opacity-20", [7481, 7502], [8460, 8473], "shrink-0", [9367, 9402], "size-4 animate-pulse text-blue-500", [10596, 10608], [12581, 12608], "mt-0.5 size-5 text-red-600", [14275, 14302], [16017, 16044], [17713, 17740], [19843, 19870], [21916, 21943], [23567, 23594], [25119, 25126], [25576, 25588], {"fix": "5131", "messageId": "4938", "data": "5132", "desc": "4968"}, {"fix": "5133", "messageId": "4938", "data": "5134", "desc": "4968"}, {"fix": "5135", "messageId": "4938", "data": "5136", "desc": "4968"}, {"fix": "5137", "messageId": "4938", "data": "5138", "desc": "4968"}, [27250, 27331], "mr-2 size-4 animate-spin rounded-full border-2 border-white border-t-transparent", [27526, 27538], {"fix": "5139", "messageId": "4938", "data": "5140", "desc": "4968"}, {"fix": "5141", "messageId": "4938", "data": "5142", "desc": "4968"}, {"fix": "5143", "messageId": "4938", "data": "5144", "desc": "4968"}, {"fix": "5145", "messageId": "4938", "data": "5146", "desc": "4968"}, [28943, 28966], "ml-2 size-4 rotate-180", [29489, 29496], [29840, 29862], "size-4 text-amber-600", [30408, 30430], [30925, 30947], [2333, 2354], [2881, 2902], [4096, 4103], [4664, 4692], "mt-0.5 size-4 text-blue-600", [6011, 6034], [7319, 7326], [7885, 7915], "mt-0.5 size-4 text-orange-600", [8637, 8660], [10226, 10256], "mt-0.5 size-4 text-purple-600", [11064, 11086], [12798, 12819], [13415, 13436], [13805, 13826], [14417, 14424], [14737, 14758], [15156, 15179], [15703, 15726], [2863, 2885], [3429, 3451], [4969, 4980], [6609, 6630], [7413, 7425], [8869, 8881], [9931, 9959], [10525, 10532], [10855, 10877], [11337, 11358], [11896, 11919], [2017, 2038], [3065, 3086], [4018, 4046], [4574, 4596], [5870, 5877], [6174, 6181], [7431, 7443], [7960, 7983], "size-5 text-indigo-600", [10134, 10141], [10423, 10430], [10715, 10722], [11002, 11009], [11289, 11296], [11768, 11798], "mt-0.5 size-4 text-indigo-600", [13323, 13351], [13874, 13897], [15063, 15093], [15673, 15694], "size-5 text-cyan-600", [16359, 16366], [16628, 16635], [16897, 16904], [17175, 17182], [17692, 17720], "mt-0.5 size-4 text-cyan-600", [17834, 17862], [17975, 18003], [18126, 18154], [19527, 19550], [20232, 20254], "size-5 text-amber-600", [20917, 20946], "mt-0.5 size-4 text-amber-600", [3732, 3755], [4743, 4764], [6934, 6941], [7620, 7648], [8389, 8411], [9709, 9716], [10053, 10060], [10741, 10770], "mt-0.5 size-4 text-green-600", [11484, 11505], [12558, 12565], [13040, 13078], "size-4 rounded border border-gray-300", [13667, 13674], [14044, 14072], "mt-0.5 size-4 text-pink-600", [14802, 14825], [15694, 15724], [16242, 16270], "mt-0.5 size-4 text-gray-600", [16810, 16833], [20987, 20994], [21491, 21521], [22140, 22161], "size-5 text-teal-600", [24773, 24780], [25275, 25303], "mt-0.5 size-4 text-teal-600", [25900, 25922], [26729, 26758], {"kind": "4935", "justification": "4936"}, [2356, 2379], [3304, 3327], [4533, 4555], [4683, 4704], [4836, 4859], "size-4 text-purple-500", [4994, 5014], "size-4 text-red-500", [5141, 5163], "size-4 text-amber-500", [5290, 5312], [5439, 5461], "size-4 text-green-400", [5589, 5610], [5740, 5762], [5894, 5917], "size-4 text-yellow-500", [6572, 6601], [6720, 6748], [6871, 6901], [7027, 7054], "mt-0.5 size-4 text-red-600", [7172, 7201], [7319, 7348], "mt-0.5 size-4 text-amber-700", [7466, 7495], "mt-0.5 size-4 text-green-500", [7614, 7642], [7763, 7792], [7915, 7945], "mt-0.5 size-4 text-yellow-600", [10658, 10679], [12490, 12501], [13868, 13890], [14955, 14962], [15031, 15038], [15110, 15117], [17108, 17131], [19152, 19159], [19474, 19495], [19956, 19979], [20436, 20458], [1613, 1634], [2562, 2583], [3605, 3633], [3954, 3975], "size-4 text-pink-600", [4243, 4264], [4531, 4552], [4825, 4846], [5357, 5380], [6108, 6138], [6665, 6687], [7436, 7465], [8006, 8013], [8328, 8349], [8910, 8933], [4572, 4594], [5490, 5511], [9470, 9491], [10992, 11013], [12222, 12242], [16055, 16067], [18830, 18850], "size-4 text-red-600", [1510, 1532], [2037, 2059], [5119, 5140], [6651, 6674], [8232, 8262], [9340, 9347], [9662, 9684], [10206, 10227], [3185, 3206], [4531, 4552], [6396, 6419], [7946, 7953], [10843, 10850], [11557, 11579], [2483, 2504], [3034, 3055], [4299, 4319], [4401, 4422], [5795, 5816], [5891, 5912], [6703, 6725], [7894, 7914], [7999, 8021], [9417, 9439], [9514, 9535], [10325, 10348], [11909, 11916], [12227, 12248], [12817, 12839], {"fix": "5147", "messageId": "4938", "data": "5148", "desc": "4968"}, {"fix": "5149", "messageId": "4938", "data": "5150", "desc": "4968"}, {"fix": "5151", "messageId": "4938", "data": "5152", "desc": "4968"}, [1313, 1325], [1463, 1475], [1715, 1727], [1889, 1901], [2096, 2103], [2291, 2303], [2452, 2464], [2678, 2690], [1306, 1313], [2589, 2601], [3174, 3181], [3816, 3823], [2855, 2862], [3179, 3191], {"messageId": "4953", "data": "5153", "fix": "5154", "desc": "4956"}, {"messageId": "4953", "data": "5155", "fix": "5156", "desc": "4959"}, {"messageId": "4953", "data": "5157", "fix": "5158", "desc": "4962"}, {"messageId": "4953", "data": "5159", "fix": "5160", "desc": "4965"}, {"messageId": "4953", "data": "5161", "fix": "5162", "desc": "4956"}, {"messageId": "4953", "data": "5163", "fix": "5164", "desc": "4959"}, {"messageId": "4953", "data": "5165", "fix": "5166", "desc": "4962"}, {"messageId": "4953", "data": "5167", "fix": "5168", "desc": "4965"}, [3766, 3778], [3277, 3299], "mx-auto mb-2 size-12", [3682, 3694], [5300, 5315], "mx-auto size-6", [5732, 5754], "size-6 p-0 text-white", [5820, 5827], [6805, 6816], "size-6 p-0", [6857, 6864], [8139, 8146], "size-6", [10395, 10465], "`relative size-16 cursor-pointer overflow-hidden rounded border-2 ${", [3097, 3108], [1717, 1724], [2848, 2860], [3804, 3811], [4050, 4057], [4274, 4281], [1604, 1611], [4284, 4291], [4642, 4649], {"fix": "5169", "messageId": "4938", "data": "5170", "desc": "4968"}, [2044, 2051], [3020, 3032], [2169, 2181], [2389, 2401], [2613, 2625], [2861, 2873], [6159, 6171], [8015, 8036], [8538, 8550], [8747, 8758], [8805, 8812], [9020, 9032], [9198, 9210], [9533, 9545], [2805, 2867], [3368, 3384], "`mr-2 size-4 ${", [3557, 3569], [3845, 3852], [3989, 3996], [4345, 4357], [5717, 5744], "size-3 rounded-full border", [1252, 1259], [1579, 1591], [1972, 1984], {"messageId": "5025", "fix": "5171", "desc": "5027"}, {"messageId": "5028", "fix": "5172", "desc": "5030"}, {"messageId": "5025", "fix": "5173", "desc": "5027"}, {"messageId": "5028", "fix": "5174", "desc": "5030"}, {"messageId": "5025", "fix": "5175", "desc": "5027"}, {"messageId": "5028", "fix": "5176", "desc": "5030"}, [894, 910], [1086, 1098], [1235, 1247], [1362, 1374], [1541, 1548], [1743, 1754], [1857, 1868], [1971, 1982], [2154, 2165], [2332, 2339], [3852, 3885], "size-16 object-cover rounded-lg", [6377, 6389], [6563, 6570], [7506, 7518], [8819, 8881], [10047, 10078], [301, 313], [427, 439], [555, 567], [676, 688], [784, 796], [1713, 1735], [1802, 1822], [1878, 1899], [2701, 2712], [3571, 3594], [5101, 5124], [6091, 6115], [1653, 1674], [1743, 1765], [1827, 1850], [1915, 1938], [1992, 2013], [5709, 5730], [6728, 6749], [8012, 8019], [8239, 8251], [8421, 8433], [8605, 8617], [8948, 8960], [9254, 9266], [9553, 9565], [970, 977], [1184, 1191], [1405, 1412], [1620, 1627], [2862, 2893], [4855, 4886], {"messageId": "4953", "data": "5177", "fix": "5178", "desc": "4956"}, {"messageId": "4953", "data": "5179", "fix": "5180", "desc": "4959"}, {"messageId": "4953", "data": "5181", "fix": "5182", "desc": "4962"}, {"messageId": "4953", "data": "5183", "fix": "5184", "desc": "4965"}, {"messageId": "4953", "data": "5185", "fix": "5186", "desc": "4956"}, {"messageId": "4953", "data": "5187", "fix": "5188", "desc": "4959"}, {"messageId": "4953", "data": "5189", "fix": "5190", "desc": "4962"}, {"messageId": "4953", "data": "5191", "fix": "5192", "desc": "4965"}, [1310, 1322], [1544, 1566], [1629, 1652], [1716, 1736], [1803, 1824], [1892, 1913], [1975, 1996], [3133, 3145], [5219, 5226], [7238, 7250], [7351, 7363], [9242, 9279], "mt-1 size-2 rounded-full bg-blue-600", [9806, 9844], "mt-1 size-2 rounded-full bg-green-600", [10616, 10628], [10967, 10979], [11302, 11314], [1112, 1174], [1584, 1596], [1728, 1740], [1980, 1992], [2107, 2144], [303, 315], [440, 452], [567, 579], [671, 683], [1762, 1784], [1851, 1871], [1931, 1952], [2684, 2705], [3613, 3620], [3934, 3966], "size-3 rounded-full bg-blue-500", [4617, 4651], "size-3 rounded-full bg-yellow-500", [5295, 5328], "size-3 rounded-full bg-green-500", [5991, 6025], "size-3 rounded-full bg-purple-500", [6758, 6765], [1742, 1749], [1812, 1819], [1887, 1894], [1964, 1971], [2028, 2035], [7047, 7054], [7360, 7372], [7633, 7645], [7853, 7865], [8175, 8187], [1193, 1265], "size-4 shrink-0 text-muted-foreground transition-transform duration-200", [398, 459], "relative flex size-10 shrink-0 overflow-hidden rounded-full", [824, 851], "aspect-square size-full", [1229, 1297], "flex size-full items-center justify-center rounded-full bg-muted", [915, 970], "size-7 bg-transparent p-0 opacity-50 hover:opacity-100", [1892, 1941], "size-8 p-0 font-normal aria-selected:opacity-100", [2735, 2742], [2874, 2881], [448, 706], "peer size-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground", [892, 899], [2198, 2205], [2363, 2370], [1173, 1239], "flex size-10 items-center justify-center rounded-full bg-red-100", [1285, 1305], [2096, 2121], "mr-2 size-4 animate-spin", [2159, 2166], [1169, 1184], "ml-auto size-4", [1626, 2063], "z-50 min-w-32 overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2", [2588, 3025], "z-50 min-w-32 overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2", [4549, 4609], "absolute left-2 flex size-3.5 items-center justify-center", [4684, 4691], [5519, 5579], [5655, 5675], "size-2 fill-current", [9099, 9138], "size-4 opacity-50 transition-transform", [9458, 9528], "absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground", [10750, 10778], "size-10 p-0 hover:bg-muted", {"messageId": "4953", "data": "5193", "fix": "5194", "desc": "4975"}, {"messageId": "4953", "data": "5195", "fix": "5196", "desc": "4978"}, {"messageId": "4953", "data": "5197", "fix": "5198", "desc": "4981"}, {"messageId": "4953", "data": "5199", "fix": "5200", "desc": "4984"}, {"messageId": "4953", "data": "5201", "fix": "5202", "desc": "4975"}, {"messageId": "4953", "data": "5203", "fix": "5204", "desc": "4978"}, {"messageId": "4953", "data": "5205", "fix": "5206", "desc": "4981"}, {"messageId": "4953", "data": "5207", "fix": "5208", "desc": "4984"}, [1566, 1584], "`mt-0.5 size-5 ${", [580, 626], "size-full flex-1 bg-primary transition-all", [515, 546], "size-full rounded-[inherit]", [1206, 1256], "h-full w-2.5 border-l border-l-transparent p-px", [1306, 1358], "h-2.5 flex-col border-t border-t-transparent p-px", [1065, 1083], "size-4 opacity-50", [1644, 1651], [2214, 2221], [2694, 3251], "relative z-50 max-h-[--radix-select-content-available-height] min-w-32 overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]", [4946, 5007], "absolute right-2 flex size-3.5 items-center justify-center", [5076, 5083], [616, 630], "h-px w-full", [635, 649], "h-full w-px", [755, 966], "block size-4 rounded-full border border-primary/50 bg-background shadow transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50", [891, 1061], "pointer-events-none block size-4 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0", {"fix": "5209", "messageId": "4938", "data": "5210", "desc": "4968"}, {"fix": "5211", "messageId": "4938", "data": "5212", "desc": "4968"}, {"messageId": "5025", "fix": "5213", "desc": "5027"}, {"messageId": "5028", "fix": "5214", "desc": "5030"}, {"messageId": "5025", "fix": "5215", "desc": "5027"}, {"messageId": "5028", "fix": "5216", "desc": "5030"}, {"messageId": "5025", "fix": "5217", "desc": "5027"}, {"messageId": "5028", "fix": "5218", "desc": "5030"}, {"messageId": "5025", "fix": "5219", "desc": "5027"}, {"messageId": "5028", "fix": "5220", "desc": "5030"}, {"fix": "5221", "messageId": "4938", "data": "5222", "desc": "4968"}, {"messageId": "5025", "fix": "5223", "desc": "5027"}, {"messageId": "5028", "fix": "5224", "desc": "5030"}, {"fix": "5225", "messageId": "4938", "data": "5226", "desc": "4968"}, {"fix": "5227", "messageId": "4938", "data": "5228", "desc": "4968"}, {"fix": "5229", "messageId": "4938", "data": "5230", "desc": "4968"}, {"fix": "5231", "messageId": "4938", "data": "5232", "desc": "4968"}, {"fix": "5233", "messageId": "4938", "data": "5234", "desc": "4968"}, {"messageId": "5025", "fix": "5235", "desc": "5027"}, {"messageId": "5028", "fix": "5236", "desc": "5030"}, {"messageId": "5025", "fix": "5237", "desc": "5027"}, {"messageId": "5028", "fix": "5238", "desc": "5030"}, {"messageId": "5025", "fix": "5239", "desc": "5027"}, {"messageId": "5028", "fix": "5240", "desc": "5030"}, {"messageId": "5025", "fix": "5241", "desc": "5027"}, {"messageId": "5028", "fix": "5242", "desc": "5030"}, {"messageId": "5025", "fix": "5243", "desc": "5027"}, {"messageId": "5028", "fix": "5244", "desc": "5030"}, {"messageId": "5025", "fix": "5245", "desc": "5027"}, {"messageId": "5028", "fix": "5246", "desc": "5030"}, {"messageId": "5025", "fix": "5247", "desc": "5027"}, {"messageId": "5028", "fix": "5248", "desc": "5030"}, {"messageId": "5025", "fix": "5249", "desc": "5027"}, {"messageId": "5028", "fix": "5250", "desc": "5030"}, {"fix": "5251", "messageId": "4938", "data": "5252", "desc": "4968"}, {"fix": "5253", "messageId": "4938", "data": "5254", "desc": "4968"}, {"messageId": "5025", "fix": "5255", "desc": "5027"}, {"messageId": "5028", "fix": "5256", "desc": "5030"}, {"fix": "5257", "messageId": "4938", "data": "5258", "desc": "4968"}, {"messageId": "5025", "fix": "5259", "desc": "5027"}, {"messageId": "5028", "fix": "5260", "desc": "5030"}, {"messageId": "5025", "fix": "5261", "desc": "5027"}, {"messageId": "5028", "fix": "5262", "desc": "5030"}, {"messageId": "5025", "fix": "5263", "desc": "5027"}, {"messageId": "5028", "fix": "5264", "desc": "5030"}, {"messageId": "5025", "fix": "5265", "desc": "5027"}, {"messageId": "5028", "fix": "5266", "desc": "5030"}, {"messageId": "5025", "fix": "5267", "desc": "5027"}, {"messageId": "5028", "fix": "5268", "desc": "5030"}, {"messageId": "5025", "fix": "5269", "desc": "5027"}, {"messageId": "5028", "fix": "5270", "desc": "5030"}, {"fix": "5271", "messageId": "4938", "data": "5272", "desc": "4968"}, {"fix": "5273", "messageId": "4938", "data": "5274", "desc": "4968"}, {"fix": "5275", "messageId": "4938", "data": "5276", "desc": "4968"}, {"fix": "5277", "messageId": "4938", "data": "5278", "desc": "4968"}, {"fix": "5279", "messageId": "4938", "data": "5280", "desc": "4968"}, {"fix": "5281", "messageId": "4938", "data": "5282", "desc": "4968"}, {"fix": "5283", "messageId": "4938", "data": "5284", "desc": "4968"}, {"fix": "5285", "messageId": "4938", "data": "5286", "desc": "4968"}, {"fix": "5287", "messageId": "4938", "data": "5288", "desc": "4968"}, {"fix": "5289", "messageId": "4938", "data": "5290", "desc": "4968"}, {"fix": "5291", "messageId": "4938", "data": "5292", "desc": "4968"}, {"fix": "5293", "messageId": "4938", "data": "5294", "desc": "4968"}, {"fix": "5295", "messageId": "4938", "data": "5296", "desc": "4968"}, {"fix": "5297", "messageId": "4938", "data": "5298", "desc": "4968"}, {"fix": "5299", "messageId": "4938", "data": "5300", "desc": "4968"}, {"fix": "5301", "messageId": "4938", "data": "5302", "desc": "4968"}, {"fix": "5303", "messageId": "4938", "data": "5304", "desc": "4968"}, {"fix": "5305", "messageId": "4938", "data": "5306", "desc": "4968"}, {"fix": "5307", "messageId": "4938", "data": "5308", "desc": "4968"}, {"messageId": "5025", "fix": "5309", "desc": "5027"}, {"messageId": "5028", "fix": "5310", "desc": "5030"}, {"messageId": "5025", "fix": "5311", "desc": "5027"}, {"messageId": "5028", "fix": "5312", "desc": "5030"}, {"messageId": "5025", "fix": "5313", "desc": "5027"}, {"messageId": "5028", "fix": "5314", "desc": "5030"}, {"fix": "5315", "messageId": "4938", "data": "5316", "desc": "4968"}, {"fix": "5317", "messageId": "4938", "data": "5318", "desc": "4968"}, {"fix": "5319", "messageId": "4938", "data": "5320", "desc": "4968"}, {"fix": "5321", "messageId": "4938", "data": "5322", "desc": "4968"}, {"fix": "5323", "messageId": "4938", "data": "5324", "desc": "4968"}, {"fix": "5325", "messageId": "4938", "data": "5326", "desc": "4968"}, {"fix": "5327", "messageId": "4938", "data": "5328", "desc": "4968"}, {"fix": "5329", "messageId": "4938", "data": "5330", "desc": "4968"}, {"fix": "5331", "messageId": "4938", "data": "5332", "desc": "4968"}, {"fix": "5333", "messageId": "4938", "data": "5334", "desc": "4968"}, {"fix": "5335", "messageId": "4938", "data": "5336", "desc": "4968"}, {"fix": "5337", "messageId": "4938", "data": "5338", "desc": "4968"}, {"fix": "5339", "messageId": "4938", "data": "5340", "desc": "4968"}, {"fix": "5341", "messageId": "4938", "data": "5342", "desc": "4968"}, {"fix": "5343", "messageId": "4938", "data": "5344", "desc": "4968"}, {"fix": "5345", "messageId": "4938", "data": "5346", "desc": "4968"}, {"fix": "5347", "messageId": "4938", "data": "5348", "desc": "4968"}, {"fix": "5349", "messageId": "4938", "data": "5350", "desc": "4968"}, {"fix": "5351", "messageId": "4938", "data": "5352", "desc": "4968"}, {"fix": "5353", "messageId": "4938", "data": "5354", "desc": "4968"}, {"fix": "5355", "messageId": "4938", "data": "5356", "desc": "4968"}, {"fix": "5357", "messageId": "4938", "data": "5358", "desc": "4968"}, {"fix": "5359", "messageId": "4938", "data": "5360", "desc": "4968"}, {"fix": "5361", "messageId": "4938", "data": "5362", "desc": "4968"}, {"fix": "5363", "messageId": "4938", "data": "5364", "desc": "4968"}, {"fix": "5365", "messageId": "4938", "data": "5366", "desc": "4968"}, {"fix": "5367", "messageId": "4938", "data": "5368", "desc": "4968"}, {"messageId": "5025", "fix": "5369", "desc": "5027"}, {"messageId": "5028", "fix": "5370", "desc": "5030"}, "directive", "", {"range": "5371", "text": "4936"}, "removeConsole", {"propertyName": "5372"}, "Remove the console.info().", {"range": "5373", "text": "4936"}, {"propertyName": "5372"}, {"range": "5374", "text": "4936"}, {"propertyName": "5372"}, {"range": "5375", "text": "4936"}, {"propertyName": "5372"}, {"range": "5376", "text": "4936"}, {"propertyName": "5372"}, {"range": "5377", "text": "4936"}, {"propertyName": "5372"}, {"range": "5378", "text": "4936"}, {"propertyName": "5372"}, "replaceWithAlt", {"alt": "5379"}, {"range": "5380", "text": "5381"}, "Replace with `&apos;`.", {"alt": "5382"}, {"range": "5383", "text": "5384"}, "Replace with `&lsquo;`.", {"alt": "5385"}, {"range": "5386", "text": "5387"}, "Replace with `&#39;`.", {"alt": "5388"}, {"range": "5389", "text": "5390"}, "Replace with `&rsquo;`.", {"range": "5391", "text": "4936"}, {"propertyName": "5392"}, "Remove the console.log().", {"range": "5393", "text": "4936"}, {"propertyName": "5392"}, {"range": "5394", "text": "4936"}, {"propertyName": "5392"}, {"alt": "5395"}, {"range": "5396", "text": "5397"}, "Replace with `&quot;`.", {"alt": "5398"}, {"range": "5399", "text": "5400"}, "Replace with `&ldquo;`.", {"alt": "5401"}, {"range": "5402", "text": "5403"}, "Replace with `&#34;`.", {"alt": "5404"}, {"range": "5405", "text": "5406"}, "Replace with `&rdquo;`.", {"alt": "5395"}, {"range": "5407", "text": "5408"}, {"alt": "5398"}, {"range": "5409", "text": "5410"}, {"alt": "5401"}, {"range": "5411", "text": "5412"}, {"alt": "5404"}, {"range": "5413", "text": "5414"}, {"alt": "5395"}, {"range": "5415", "text": "5416"}, {"alt": "5398"}, {"range": "5417", "text": "5418"}, {"alt": "5401"}, {"range": "5419", "text": "5420"}, {"alt": "5404"}, {"range": "5421", "text": "5422"}, {"alt": "5395"}, {"range": "5423", "text": "5424"}, {"alt": "5398"}, {"range": "5425", "text": "5426"}, {"alt": "5401"}, {"range": "5427", "text": "5428"}, {"alt": "5404"}, {"range": "5429", "text": "5430"}, {"alt": "5395"}, {"range": "5431", "text": "5432"}, {"alt": "5398"}, {"range": "5433", "text": "5434"}, {"alt": "5401"}, {"range": "5435", "text": "5436"}, {"alt": "5404"}, {"range": "5437", "text": "5438"}, {"alt": "5395"}, {"range": "5439", "text": "5440"}, {"alt": "5398"}, {"range": "5441", "text": "5442"}, {"alt": "5401"}, {"range": "5443", "text": "5444"}, {"alt": "5404"}, {"range": "5445", "text": "5446"}, "suggestUnknown", {"range": "5447", "text": "5448"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "5449", "text": "5450"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"alt": "5379"}, {"range": "5451", "text": "5452"}, {"alt": "5382"}, {"range": "5453", "text": "5454"}, {"alt": "5385"}, {"range": "5455", "text": "5456"}, {"alt": "5388"}, {"range": "5457", "text": "5458"}, {"alt": "5395"}, {"range": "5459", "text": "5460"}, {"alt": "5398"}, {"range": "5461", "text": "5462"}, {"alt": "5401"}, {"range": "5463", "text": "5464"}, {"alt": "5404"}, {"range": "5465", "text": "5466"}, {"alt": "5395"}, {"range": "5467", "text": "5468"}, {"alt": "5398"}, {"range": "5469", "text": "5470"}, {"alt": "5401"}, {"range": "5471", "text": "5472"}, {"alt": "5404"}, {"range": "5473", "text": "5474"}, {"alt": "5395"}, {"range": "5475", "text": "5476"}, {"alt": "5398"}, {"range": "5477", "text": "5478"}, {"alt": "5401"}, {"range": "5479", "text": "5480"}, {"alt": "5404"}, {"range": "5481", "text": "5482"}, {"alt": "5395"}, {"range": "5483", "text": "5484"}, {"alt": "5398"}, {"range": "5485", "text": "5486"}, {"alt": "5401"}, {"range": "5487", "text": "5488"}, {"alt": "5404"}, {"range": "5489", "text": "5490"}, {"range": "5491", "text": "5448"}, {"range": "5492", "text": "5450"}, {"range": "5493", "text": "5448"}, {"range": "5494", "text": "5450"}, {"range": "5495", "text": "5448"}, {"range": "5496", "text": "5450"}, {"range": "5497", "text": "5448"}, {"range": "5498", "text": "5450"}, {"range": "5499", "text": "5448"}, {"range": "5500", "text": "5450"}, {"range": "5501", "text": "5448"}, {"range": "5502", "text": "5450"}, {"range": "5503", "text": "5448"}, {"range": "5504", "text": "5450"}, {"range": "5505", "text": "4936"}, {"propertyName": "5392"}, {"range": "5506", "text": "4936"}, {"propertyName": "5392"}, {"alt": "5379"}, {"range": "5507", "text": "5508"}, {"alt": "5382"}, {"range": "5509", "text": "5510"}, {"alt": "5385"}, {"range": "5511", "text": "5512"}, {"alt": "5388"}, {"range": "5513", "text": "5514"}, {"range": "5515", "text": "4936"}, {"propertyName": "5392"}, {"range": "5516", "text": "4936"}, {"propertyName": "5392"}, {"range": "5517", "text": "5448"}, {"range": "5518", "text": "5450"}, {"range": "5519", "text": "5448"}, {"range": "5520", "text": "5450"}, {"range": "5521", "text": "5448"}, {"range": "5522", "text": "5450"}, {"range": "5523", "text": "4936"}, {"propertyName": "5392"}, {"range": "5524", "text": "4936"}, {"propertyName": "5392"}, {"range": "5525", "text": "4936"}, {"propertyName": "5392"}, {"range": "5526", "text": "4936"}, {"propertyName": "5392"}, "Update the dependencies array to be: [isLoaded, brands.length, categories.length, addSampleData]", {"range": "5527", "text": "5528"}, {"range": "5529", "text": "5448"}, {"range": "5530", "text": "5450"}, {"range": "5531", "text": "5448"}, {"range": "5532", "text": "5450"}, {"range": "5533", "text": "4936"}, {"propertyName": "5392"}, {"range": "5534", "text": "4936"}, {"propertyName": "5392"}, {"range": "5535", "text": "4936"}, {"propertyName": "5392"}, {"range": "5536", "text": "4936"}, {"propertyName": "5392"}, {"range": "5537", "text": "4936"}, {"propertyName": "5392"}, {"range": "5538", "text": "4936"}, {"propertyName": "5392"}, {"range": "5539", "text": "4936"}, {"propertyName": "5392"}, {"range": "5540", "text": "4936"}, {"propertyName": "5392"}, {"range": "5541", "text": "4936"}, {"propertyName": "5392"}, {"range": "5542", "text": "4936"}, {"propertyName": "5392"}, {"range": "5543", "text": "4936"}, {"propertyName": "5392"}, {"range": "5544", "text": "4936"}, {"propertyName": "5392"}, {"range": "5545", "text": "4936"}, {"propertyName": "5392"}, {"range": "5546", "text": "4936"}, {"propertyName": "5392"}, {"range": "5547", "text": "4936"}, {"propertyName": "5392"}, {"range": "5548", "text": "4936"}, {"propertyName": "5392"}, {"alt": "5379"}, {"range": "5549", "text": "5550"}, {"alt": "5382"}, {"range": "5551", "text": "5552"}, {"alt": "5385"}, {"range": "5553", "text": "5554"}, {"alt": "5388"}, {"range": "5555", "text": "5556"}, {"alt": "5379"}, {"range": "5557", "text": "5558"}, {"alt": "5382"}, {"range": "5559", "text": "5560"}, {"alt": "5385"}, {"range": "5561", "text": "5562"}, {"alt": "5388"}, {"range": "5563", "text": "5564"}, {"range": "5565", "text": "4936"}, {"propertyName": "5392"}, {"range": "5566", "text": "5448"}, {"range": "5567", "text": "5450"}, {"range": "5568", "text": "5448"}, {"range": "5569", "text": "5450"}, {"range": "5570", "text": "5448"}, {"range": "5571", "text": "5450"}, {"alt": "5379"}, {"range": "5572", "text": "5573"}, {"alt": "5382"}, {"range": "5574", "text": "5575"}, {"alt": "5385"}, {"range": "5576", "text": "5577"}, {"alt": "5388"}, {"range": "5578", "text": "5579"}, {"alt": "5379"}, {"range": "5580", "text": "5581"}, {"alt": "5382"}, {"range": "5582", "text": "5583"}, {"alt": "5385"}, {"range": "5584", "text": "5585"}, {"alt": "5388"}, {"range": "5586", "text": "5587"}, {"alt": "5395"}, {"range": "5588", "text": "5589"}, {"alt": "5398"}, {"range": "5590", "text": "5591"}, {"alt": "5401"}, {"range": "5592", "text": "5593"}, {"alt": "5404"}, {"range": "5594", "text": "5595"}, {"alt": "5395"}, {"range": "5596", "text": "5597"}, {"alt": "5398"}, {"range": "5598", "text": "5599"}, {"alt": "5401"}, {"range": "5600", "text": "5601"}, {"alt": "5404"}, {"range": "5602", "text": "5603"}, {"range": "5604", "text": "4936"}, {"propertyName": "5392"}, {"range": "5605", "text": "4936"}, {"propertyName": "5392"}, {"range": "5606", "text": "5448"}, {"range": "5607", "text": "5450"}, {"range": "5608", "text": "5448"}, {"range": "5609", "text": "5450"}, {"range": "5610", "text": "5448"}, {"range": "5611", "text": "5450"}, {"range": "5612", "text": "5448"}, {"range": "5613", "text": "5450"}, {"range": "5614", "text": "4936"}, {"propertyName": "5392"}, {"range": "5615", "text": "5448"}, {"range": "5616", "text": "5450"}, {"range": "5617", "text": "4936"}, {"propertyName": "5392"}, {"range": "5618", "text": "4936"}, {"propertyName": "5392"}, {"range": "5619", "text": "4936"}, {"propertyName": "5392"}, {"range": "5620", "text": "4936"}, {"propertyName": "5392"}, {"range": "5621", "text": "4936"}, {"propertyName": "5392"}, {"range": "5622", "text": "5448"}, {"range": "5623", "text": "5450"}, {"range": "5624", "text": "5448"}, {"range": "5625", "text": "5450"}, {"range": "5626", "text": "5448"}, {"range": "5627", "text": "5450"}, {"range": "5628", "text": "5448"}, {"range": "5629", "text": "5450"}, {"range": "5630", "text": "5448"}, {"range": "5631", "text": "5450"}, {"range": "5632", "text": "5448"}, {"range": "5633", "text": "5450"}, {"range": "5634", "text": "5448"}, {"range": "5635", "text": "5450"}, {"range": "5636", "text": "5448"}, {"range": "5637", "text": "5450"}, {"range": "5638", "text": "4936"}, {"propertyName": "5392"}, {"range": "5639", "text": "4936"}, {"propertyName": "5392"}, {"range": "5640", "text": "5448"}, {"range": "5641", "text": "5450"}, {"range": "5642", "text": "4936"}, {"propertyName": "5392"}, {"range": "5643", "text": "5448"}, {"range": "5644", "text": "5450"}, {"range": "5645", "text": "5448"}, {"range": "5646", "text": "5450"}, {"range": "5647", "text": "5448"}, {"range": "5648", "text": "5450"}, {"range": "5649", "text": "5448"}, {"range": "5650", "text": "5450"}, {"range": "5651", "text": "5448"}, {"range": "5652", "text": "5450"}, {"range": "5653", "text": "5448"}, {"range": "5654", "text": "5450"}, {"range": "5655", "text": "4936"}, {"propertyName": "5392"}, {"range": "5656", "text": "4936"}, {"propertyName": "5392"}, {"range": "5657", "text": "4936"}, {"propertyName": "5392"}, {"range": "5658", "text": "4936"}, {"propertyName": "5392"}, {"range": "5659", "text": "4936"}, {"propertyName": "5392"}, {"range": "5660", "text": "4936"}, {"propertyName": "5392"}, {"range": "5661", "text": "4936"}, {"propertyName": "5392"}, {"range": "5662", "text": "4936"}, {"propertyName": "5392"}, {"range": "5663", "text": "4936"}, {"propertyName": "5392"}, {"range": "5664", "text": "4936"}, {"propertyName": "5392"}, {"range": "5665", "text": "4936"}, {"propertyName": "5392"}, {"range": "5666", "text": "4936"}, {"propertyName": "5392"}, {"range": "5667", "text": "4936"}, {"propertyName": "5392"}, {"range": "5668", "text": "4936"}, {"propertyName": "5392"}, {"range": "5669", "text": "4936"}, {"propertyName": "5392"}, {"range": "5670", "text": "4936"}, {"propertyName": "5392"}, {"range": "5671", "text": "4936"}, {"propertyName": "5392"}, {"range": "5672", "text": "4936"}, {"propertyName": "5392"}, {"range": "5673", "text": "4936"}, {"propertyName": "5392"}, {"range": "5674", "text": "5448"}, {"range": "5675", "text": "5450"}, {"range": "5676", "text": "5448"}, {"range": "5677", "text": "5450"}, {"range": "5678", "text": "5448"}, {"range": "5679", "text": "5450"}, {"range": "5680", "text": "4936"}, {"propertyName": "5392"}, {"range": "5681", "text": "4936"}, {"propertyName": "5392"}, {"range": "5682", "text": "4936"}, {"propertyName": "5392"}, {"range": "5683", "text": "4936"}, {"propertyName": "5392"}, {"range": "5684", "text": "4936"}, {"propertyName": "5392"}, {"range": "5685", "text": "4936"}, {"propertyName": "5392"}, {"range": "5686", "text": "4936"}, {"propertyName": "5392"}, {"range": "5687", "text": "4936"}, {"propertyName": "5392"}, {"range": "5688", "text": "4936"}, {"propertyName": "5392"}, {"range": "5689", "text": "4936"}, {"propertyName": "5392"}, {"range": "5690", "text": "4936"}, {"propertyName": "5392"}, {"range": "5691", "text": "4936"}, {"propertyName": "5392"}, {"range": "5692", "text": "4936"}, {"propertyName": "5392"}, {"range": "5693", "text": "4936"}, {"propertyName": "5392"}, {"range": "5694", "text": "4936"}, {"propertyName": "5392"}, {"range": "5695", "text": "4936"}, {"propertyName": "5392"}, {"range": "5696", "text": "4936"}, {"propertyName": "5392"}, {"range": "5697", "text": "4936"}, {"propertyName": "5392"}, {"range": "5698", "text": "4936"}, {"propertyName": "5392"}, {"range": "5699", "text": "4936"}, {"propertyName": "5392"}, {"range": "5700", "text": "4936"}, {"propertyName": "5392"}, {"range": "5701", "text": "4936"}, {"propertyName": "5392"}, {"range": "5702", "text": "4936"}, {"propertyName": "5392"}, {"range": "5703", "text": "4936"}, {"propertyName": "5392"}, {"range": "5704", "text": "4936"}, {"propertyName": "5392"}, {"range": "5705", "text": "4936"}, {"propertyName": "5392"}, {"range": "5706", "text": "4936"}, {"propertyName": "5392"}, {"range": "5707", "text": "5448"}, {"range": "5708", "text": "5450"}, [4837, 4880], "info", [26370, 26489], [2053, 2098], [3677, 3722], [4195, 4229], [4692, 4752], [5093, 5148], "&apos;", [5464, 5817], "\r\n                We&apos;re currently perfecting the single-vendor e-commerce\r\n                experience with advanced product management, order processing,\r\n                customer relationships, and promotional tools. This solid\r\n                foundation will enable a seamless transition to multi-vendor\r\n                capabilities.\r\n              ", "&lsquo;", [5464, 5817], "\r\n                We&lsquo;re currently perfecting the single-vendor e-commerce\r\n                experience with advanced product management, order processing,\r\n                customer relationships, and promotional tools. This solid\r\n                foundation will enable a seamless transition to multi-vendor\r\n                capabilities.\r\n              ", "&#39;", [5464, 5817], "\r\n                We&#39;re currently perfecting the single-vendor e-commerce\r\n                experience with advanced product management, order processing,\r\n                customer relationships, and promotional tools. This solid\r\n                foundation will enable a seamless transition to multi-vendor\r\n                capabilities.\r\n              ", "&rsquo;", [5464, 5817], "\r\n                We&rsquo;re currently perfecting the single-vendor e-commerce\r\n                experience with advanced product management, order processing,\r\n                customer relationships, and promotional tools. This solid\r\n                foundation will enable a seamless transition to multi-vendor\r\n                capabilities.\r\n              ", [5127, 5169], "log", [1182, 1242], [1250, 1290], "&quot;", [4325, 4388], "Click &quot;Test API Connection\" to verify the backend is accessible", "&ldquo;", [4325, 4388], "Click &ldquo;Test API Connection\" to verify the backend is accessible", "&#34;", [4325, 4388], "Click &#34;Test API Connection\" to verify the backend is accessible", "&rdquo;", [4325, 4388], "Click &rdquo;Test API Connection\" to verify the backend is accessible", [4325, 4388], "Click \"Test API Connection&quot; to verify the backend is accessible", [4325, 4388], "Click \"Test API Connection&ldquo; to verify the backend is accessible", [4325, 4388], "Click \"Test API Connection&#34; to verify the backend is accessible", [4325, 4388], "Click \"Test API Connection&rdquo; to verify the backend is accessible", [4415, 4478], "Click &quot;Test Add Product\" to test the full product creation flow", [4415, 4478], "Click &ldquo;Test Add Product\" to test the full product creation flow", [4415, 4478], "Click &#34;Test Add Product\" to test the full product creation flow", [4415, 4478], "Click &rdquo;Test Add Product\" to test the full product creation flow", [4415, 4478], "Click \"Test Add Product&quot; to test the full product creation flow", [4415, 4478], "Click \"Test Add Product&ldquo; to test the full product creation flow", [4415, 4478], "Click \"Test Add Product&#34; to test the full product creation flow", [4415, 4478], "Click \"Test Add Product&rdquo; to test the full product creation flow", [4505, 4565], "Click &quot;Test Simple Create\" to test a basic HTTP POST request", [4505, 4565], "Click &ldquo;Test Simple Create\" to test a basic HTTP POST request", [4505, 4565], "Click &#34;Test Simple Create\" to test a basic HTTP POST request", [4505, 4565], "Click &rdquo;Test Simple Create\" to test a basic HTTP POST request", [4505, 4565], "Click \"Test Simple Create&quot; to test a basic HTTP POST request", [4505, 4565], "Click \"Test Simple Create&ldquo; to test a basic HTTP POST request", [4505, 4565], "Click \"Test Simple Create&#34; to test a basic HTTP POST request", [4505, 4565], "Click \"Test Simple Create&rdquo; to test a basic HTTP POST request", [864, 867], "unknown", [864, 867], "never", [9936, 9946], "What&apos;s New", [9936, 9946], "What&lsquo;s New", [9936, 9946], "What&#39;s New", [9936, 9946], "What&rsquo;s New", [17562, 17598], "\r\n              Search results for &quot;", [17562, 17598], "\r\n              Search results for &ldquo;", [17562, 17598], "\r\n              Search results for &#34;", [17562, 17598], "\r\n              Search results for &rdquo;", [17611, 17626], "&quot;\r\n            ", [17611, 17626], "&ldquo;\r\n            ", [17611, 17626], "&#34;\r\n            ", [17611, 17626], "&rdquo;\r\n            ", [18690, 18732], "\r\n                Search all results for &quot;", [18690, 18732], "\r\n                Search all results for &ldquo;", [18690, 18732], "\r\n                Search all results for &#34;", [18690, 18732], "\r\n                Search all results for &rdquo;", [18745, 18762], "&quot;\r\n              ", [18745, 18762], "&ldquo;\r\n              ", [18745, 18762], "&#34;\r\n              ", [18745, 18762], "&rdquo;\r\n              ", [10344, 10347], [10344, 10347], [6312, 6315], [6312, 6315], [11713, 11716], [11713, 11716], [14770, 14773], [14770, 14773], [15282, 15285], [15282, 15285], [16733, 16736], [16733, 16736], [17255, 17258], [17255, 17258], [1216, 1258], [1307, 1358], [14574, 14655], "\r\n                    This customer hasn&apos;t placed any orders.\r\n                  ", [14574, 14655], "\r\n                    This customer hasn&lsquo;t placed any orders.\r\n                  ", [14574, 14655], "\r\n                    This customer hasn&#39;t placed any orders.\r\n                  ", [14574, 14655], "\r\n                    This customer hasn&rsquo;t placed any orders.\r\n                  ", [1148, 1191], [1348, 1394], [918, 921], [918, 921], [5937, 5940], [5937, 5940], [6067, 6070], [6067, 6070], [6588, 6654], [6675, 6729], [20505, 20550], [28485, 28543], [3320, 3364], "[isLoaded, brands.length, categories.length, addSampleData]", [8183, 8186], [8183, 8186], [10821, 10824], [10821, 10824], [1226, 1262], [1378, 1416], [1467, 1505], [2442, 2480], [2576, 2616], [25891, 26051], [26079, 26129], [26157, 26325], [26353, 26497], [27846, 28003], [28031, 28081], [28109, 28277], [28305, 28555], [791, 836], [930, 972], [1068, 1109], [3582, 3653], "\r\n            The product you&apos;re looking for doesn't exist.\r\n          ", [3582, 3653], "\r\n            The product you&lsquo;re looking for doesn't exist.\r\n          ", [3582, 3653], "\r\n            The product you&#39;re looking for doesn't exist.\r\n          ", [3582, 3653], "\r\n            The product you&rsquo;re looking for doesn't exist.\r\n          ", [3582, 3653], "\r\n            The product you're looking for doesn&apos;t exist.\r\n          ", [3582, 3653], "\r\n            The product you're looking for doesn&lsquo;t exist.\r\n          ", [3582, 3653], "\r\n            The product you're looking for doesn&#39;t exist.\r\n          ", [3582, 3653], "\r\n            The product you're looking for doesn&rsquo;t exist.\r\n          ", [1142, 1186], [2271, 2274], [2271, 2274], [2296, 2299], [2296, 2299], [2346, 2349], [2346, 2349], [1146, 1195], "The transaction you&apos;re looking for doesn't exist.", [1146, 1195], "The transaction you&lsquo;re looking for doesn't exist.", [1146, 1195], "The transaction you&#39;re looking for doesn't exist.", [1146, 1195], "The transaction you&rsquo;re looking for doesn't exist.", [1146, 1195], "The transaction you're looking for doesn&apos;t exist.", [1146, 1195], "The transaction you're looking for doesn&lsquo;t exist.", [1146, 1195], "The transaction you're looking for doesn&#39;t exist.", [1146, 1195], "The transaction you're looking for doesn&rsquo;t exist.", [11263, 11303], "\n                  No emojis found for &quot;", [11263, 11303], "\n                  No emojis found for &ldquo;", [11263, 11303], "\n                  No emojis found for &#34;", [11263, 11303], "\n                  No emojis found for &rdquo;", [11316, 11334], "&quot;\n                ", [11316, 11334], "&ldquo;\n                ", [11316, 11334], "&#34;\n                ", [11316, 11334], "&rdquo;\n                ", [710, 765], [1455, 1505], [2991, 2994], [2991, 2994], [3692, 3695], [3692, 3695], [4553, 4556], [4553, 4556], [5440, 5443], [5440, 5443], [5938, 5984], [7045, 7048], [7045, 7048], [1574, 1622], [1626, 1677], [1752, 1806], [1812, 1858], [2156, 2195], [2479, 2482], [2479, 2482], [3047, 3050], [3047, 3050], [3182, 3185], [3182, 3185], [3211, 3214], [3211, 3214], [3378, 3381], [3378, 3381], [3407, 3410], [3407, 3410], [3531, 3534], [3531, 3534], [3559, 3562], [3559, 3562], [3570, 3637], [3641, 3687], [3736, 3739], [3736, 3739], [3835, 3889], [4127, 4130], [4127, 4130], [4155, 4158], [4155, 4158], [4184, 4187], [4184, 4187], [4409, 4412], [4409, 4412], [4437, 4440], [4437, 4440], [4466, 4469], [4466, 4469], [285, 329], [417, 487], [520, 639], [706, 753], [816, 969], [1033, 1082], [1097, 1158], [1341, 1392], [1445, 1485], [1838, 1898], [2221, 2280], [2346, 2401], [2409, 2476], [2669, 2723], [3889, 3950], [4020, 4093], [4201, 4256], [4264, 4331], [4346, 4418], [4663, 4666], [4663, 4666], [4721, 4724], [4721, 4724], [4791, 4794], [4791, 4794], [226, 284], [340, 386], [521, 563], [583, 640], [688, 739], [809, 874], [879, 918], [968, 1027], [1132, 1228], [1272, 1324], [1630, 1691], [1737, 1792], [2004, 2065], [2105, 2154], [2262, 2329], [2375, 2430], [2501, 2544], [2581, 2639], [2725, 2783], [2812, 2883], [2895, 2970], [3056, 3098], [3103, 3174], [3179, 3227], [3232, 3304], [3309, 3377], [3382, 3442], [3576, 3579], [3576, 3579]]