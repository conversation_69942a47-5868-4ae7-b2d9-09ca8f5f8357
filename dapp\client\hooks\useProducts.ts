/**
 * React hooks for product data management
 * Provides easy-to-use hooks for CRUD operations on products
 */
import { useCallback, useEffect, useState } from "react";

import { toast } from "sonner";

import {
  type ApiResponse,
  type ProductFilters,
  createProduct,
  deleteProduct,
  getProductById,
  getProductForEdit,
  getProducts,
  updateProduct,
  updateProductSafe,
} from "@/lib/api/products";
import { Product } from "@/types/product";

// Hook state types
type UseProductsState = {
  products: Product[];
  loading: boolean;
  error: string | null;
  meta?: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
};

type UseProductState = {
  product: Product | null;
  loading: boolean;
  error: string | null;
};

/**
 * Hook for fetching and managing multiple products
 */
export function useProducts(initialFilters: ProductFilters = {}) {
  const [state, setState] = useState<UseProductsState>({
    products: [],
    loading: true,
    error: null,
  });

  const [filters, setFilters] = useState<ProductFilters>(initialFilters);

  const fetchProducts = useCallback(
    async (newFilters?: ProductFilters) => {
      setState((prev) => ({ ...prev, loading: true, error: null }));

      try {
        const filtersToUse = newFilters || filters;
        const response = await getProducts(filtersToUse);

        setState({
          products: response.data,
          loading: false,
          error: null,
          meta: response.meta,
        });
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to fetch products";
        setState((prev) => ({
          ...prev,
          loading: false,
          error: errorMessage,
        }));
        toast.error("Failed to load products");
      }
    },
    [filters]
  );

  const updateFilters = useCallback(
    (newFilters: ProductFilters) => {
      setFilters(newFilters);
      fetchProducts(newFilters);
    },
    [fetchProducts]
  );

  const refreshProducts = useCallback(() => {
    fetchProducts();
  }, [fetchProducts]);

  // Initial fetch
  useEffect(() => {
    fetchProducts();
  }, [fetchProducts]);

  return {
    ...state,
    filters,
    updateFilters,
    refreshProducts,
    refetch: fetchProducts,
  };
}

/**
 * Hook for fetching and managing a single product
 */
export function useProduct(id: string | null) {
  const [state, setState] = useState<UseProductState>({
    product: null,
    loading: true,
    error: null,
  });

  const fetchProduct = useCallback(async () => {
    if (!id) {
      setState({ product: null, loading: false, error: null });
      return;
    }

    setState((prev) => ({ ...prev, loading: true, error: null }));

    try {
      const response = await getProductById(id);
      setState({
        product: response.data,
        loading: false,
        error: null,
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch product";
      setState((prev) => ({
        ...prev,
        loading: false,
        error: errorMessage,
      }));
      toast.error("Failed to load product");
    }
  }, [id]);

  useEffect(() => {
    fetchProduct();
  }, [fetchProduct]);

  return {
    ...state,
    refetch: fetchProduct,
  };
}

/**
 * Hook for product CRUD operations
 */
export function useProductMutations() {
  const [loading, setLoading] = useState(false);

  const createProductMutation = useCallback(
    async (productData: Partial<Product>) => {
      setLoading(true);
      try {
        const response = await createProduct(productData);
        toast.success("Product created successfully");
        return response.data;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to create product";
        toast.error(errorMessage);
        throw error;
      } finally {
        setLoading(false);
      }
    },
    []
  );

  const updateProductMutation = useCallback(
    async (id: string, productData: Partial<Product>) => {
      setLoading(true);
      try {
        const response = await updateProduct(id, productData);
        toast.success("Product updated successfully");
        return response.data;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to update product";
        toast.error(errorMessage);
        throw error;
      } finally {
        setLoading(false);
      }
    },
    []
  );

  const updateProductSafeMutation = useCallback(
    async (id: string, productData: Partial<Product>) => {
      setLoading(true);
      try {
        const response = await updateProductSafe(id, productData);
        toast.success("Product updated successfully");
        return response.data;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to update product";
        toast.error(errorMessage);
        throw error;
      } finally {
        setLoading(false);
      }
    },
    []
  );

  const deleteProductMutation = useCallback(async (id: string) => {
    setLoading(true);
    try {
      await deleteProduct(id);
      toast.success("Product deleted successfully");
      return true;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to delete product";
      toast.error(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    createProduct: createProductMutation,
    updateProduct: updateProductMutation,
    updateProductSafe: updateProductSafeMutation,
    deleteProduct: deleteProductMutation,
  };
}

/**
 * Hook for fetching product for editing
 */
export function useProductForEdit(id: string | null) {
  const [state, setState] = useState<
    UseProductState & { editableFields?: string[] }
  >({
    product: null,
    loading: true,
    error: null,
    editableFields: [],
  });

  const fetchProductForEdit = useCallback(async () => {
    if (!id) {
      setState({
        product: null,
        loading: false,
        error: null,
        editableFields: [],
      });
      return;
    }

    setState((prev) => ({ ...prev, loading: true, error: null }));

    try {
      const response = await getProductForEdit(id);
      setState({
        product: response.data,
        loading: false,
        error: null,
        editableFields: (response as any).editableFields || [],
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch product for editing";
      setState((prev) => ({
        ...prev,
        loading: false,
        error: errorMessage,
      }));
      toast.error("Failed to load product for editing");
    }
  }, [id]);

  useEffect(() => {
    fetchProductForEdit();
  }, [fetchProductForEdit]);

  return {
    ...state,
    refetch: fetchProductForEdit,
  };
}
