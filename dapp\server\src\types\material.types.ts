import { Document } from "mongoose";

/**
 * Material properties type
 */
export type MaterialProperties = {
  durability: "low" | "medium" | "high";
  waterResistant: boolean;
  recyclable: boolean;
  weight: "light" | "medium" | "heavy";
};

/**
 * Material type matching the frontend Material type
 */
export type IMaterial = {
  name: string;
  description: string;
  slug: string;
  icon?: string;
  color?: string;
  properties: MaterialProperties;
  isActive: boolean;
  productCount: number;
  sortOrder: number;
};

/**
 * Material document interface for MongoDB (keeping as interface for Document extension)
 */
export interface MaterialDocument extends IMaterial, Document {
  _id: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * DTO for creating a new material
 */
export type CreateMaterialDto = {
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  properties?: Partial<MaterialProperties>;
  isActive?: boolean;
  sortOrder?: number;
};

/**
 * DTO for updating a material
 */
export type UpdateMaterialDto = {
  name?: string;
  description?: string;
  slug?: string;
  icon?: string;
  color?: string;
  properties?: Partial<MaterialProperties>;
  isActive?: boolean;
  sortOrder?: number;
};

/**
 * Material filters for querying
 */
export type MaterialFilters = {
  isActive?: boolean;
  search?: string;
  durability?: "low" | "medium" | "high";
  waterResistant?: boolean;
  recyclable?: boolean;
  weight?: "light" | "medium" | "heavy";
};
