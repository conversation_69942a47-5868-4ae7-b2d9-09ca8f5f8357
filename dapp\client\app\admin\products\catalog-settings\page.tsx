"use client";

import { useEffect, useState } from "react";

import {
  Activity,
  ArrowRight,
  BarChart3,
  Calendar,
  Database,
  Download,
  Filter,
  Hammer,
  Layers,
  LayoutGrid,
  Package,
  Palette,
  Plus,
  Search,
  Settings,
  Sparkles,
  Star,
  Tag,
  Target,
  TrendingUp,
  Upload,
  Users,
  Zap,
} from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "sonner";

import { PageHeaderWrapper } from "@/components/common/PageHeaderWrapper";
import { BrandManagerEnhanced } from "@/components/pages/brands/BrandManagerEnhanced";
import { ColorManagerEnhanced } from "@/components/pages/colors/ColorManagerEnhanced";
import { CategoryManagerEnhanced } from "@/components/pages/management/CategoryManagerEnhanced";
import { MaterialManagerEnhanced } from "@/components/pages/materials/MaterialManagerEnhanced";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, <PERSON><PERSON>ead<PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useBrands } from "@/hooks/useBrands";
import { useCategories } from "@/hooks/useCategories";
import { useColors } from "@/hooks/useColors";
import { useMaterials } from "@/hooks/useMaterials";

export default function CatalogSettings() {
  const [activeTab, setActiveTab] = useState("overview");
  const [searchQuery, setSearchQuery] = useState("");
  const searchParams = useSearchParams();
  const router = useRouter();

  // Real data hooks
  const { categories, loading: categoriesLoading } = useCategories();
  const { brands, loading: brandsLoading } = useBrands();
  const { materials, loading: materialsLoading } = useMaterials();
  const { colors, loading: colorsLoading } = useColors();

  // Handle URL parameters for direct tab navigation
  useEffect(() => {
    const tab = searchParams.get("tab");
    if (
      tab &&
      ["overview", "categories", "brands", "materials", "colors"].includes(tab)
    ) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  // Function to handle tab changes and update URL
  const handleTabChange = (newTab: string) => {
    setActiveTab(newTab);
    // Update URL with new tab parameter
    const newSearchParams = new URLSearchParams(searchParams.toString());
    newSearchParams.set("tab", newTab);
    router.push(
      `/admin/products/catalog-settings?${newSearchParams.toString()}`
    );
  };

  // Calculate real statistics
  const totalCategories = categories.length;
  const activeCategories = categories.filter((c) => c.isActive).length;
  const totalBrands = brands.length;
  const activeBrands = brands.filter((b) => b.isActive).length;
  const totalMaterials = materials.length;
  const activeMaterials = materials.filter((m) => m.isActive).length;
  const totalColors = colors.length;
  const activeColors = colors.filter((c) => c.isActive).length;

  // Calculate total products across all entities
  const totalProducts =
    [
      ...categories.map((c) => c.productCount || 0),
      ...brands.map((b) => b.productCount || 0),
      ...materials.map((m) => m.productCount || 0),
      ...colors.map((c) => c.productCount || 0),
    ].reduce((sum, count) => sum + count, 0) / 4; // Divide by 4 to avoid counting same products multiple times

  // Real statistics with dynamic data
  const overallStats = [
    {
      label: "Total Categories",
      value: totalCategories.toString(),
      active: activeCategories,
      percentage:
        totalCategories > 0
          ? Math.round((activeCategories / totalCategories) * 100)
          : 0,
      icon: LayoutGrid,
      color: "blue",
      description: "Product categories",
    },
    {
      label: "Total Brands",
      value: totalBrands.toString(),
      active: activeBrands,
      percentage:
        totalBrands > 0 ? Math.round((activeBrands / totalBrands) * 100) : 0,
      icon: Tag,
      color: "green",
      description: "Brand partners",
    },
    {
      label: "Total Materials",
      value: totalMaterials.toString(),
      active: activeMaterials,
      percentage:
        totalMaterials > 0
          ? Math.round((activeMaterials / totalMaterials) * 100)
          : 0,
      icon: Hammer,
      color: "purple",
      description: "Material types",
    },
    {
      label: "Total Colors",
      value: totalColors.toString(),
      active: activeColors,
      percentage:
        totalColors > 0 ? Math.round((activeColors / totalColors) * 100) : 0,
      icon: Palette,
      color: "pink",
      description: "Color options",
    },
  ];

  const quickActions = [
    {
      label: "Import Data",
      icon: Upload,
      action: () => {
        // Create file input for CSV/JSON import
        const input = document.createElement("input");
        input.type = "file";
        input.accept = ".csv,.json";
        input.onchange = (e) => {
          const file = (e.target as HTMLInputElement).files?.[0];
          if (file) {
            toast.success(`Importing ${file.name}...`);
            // TODO: Implement actual import logic
          }
        };
        input.click();
      },
    },
    {
      label: "Export All",
      icon: Download,
      action: () => {
        // Generate export data
        const exportData = {
          categories: categories.map((c) => ({
            name: c.name,
            description: c.description,
            isActive: c.isActive,
          })),
          brands: brands.map((b) => ({
            name: b.name,
            description: b.description,
            isActive: b.isActive,
          })),
          materials: materials.map((m) => ({
            name: m.name,
            description: m.description,
            type: m.type,
            isActive: m.isActive,
          })),
          colors: colors.map((c) => ({
            name: c.name,
            hexCode: c.hexValue,
            family: c.colorFamily,
            isActive: c.isActive,
          })),
        };

        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
          type: "application/json",
        });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `catalog-export-${new Date().toISOString().split("T")[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
        toast.success("Catalog data exported successfully!");
      },
    },
    {
      label: "Catalog Analytics",
      icon: BarChart3,
      action: () => router.push("/admin/analytics/catalog"),
    },
    {
      label: "Bulk Operations",
      icon: Settings,
      action: () => toast.info("Bulk operations panel coming soon!"),
    },
  ];

  // Generate insights from real data
  const catalogInsights = [
    {
      title: "Most Popular Categories",
      data: categories
        .sort((a, b) => (b.productCount || 0) - (a.productCount || 0))
        .slice(0, 3)
        .map((c) => ({ name: c.name, count: c.productCount || 0 })),
      icon: TrendingUp,
      color: "blue",
    },
    {
      title: "Top Brands",
      data: brands
        .sort((a, b) => (b.productCount || 0) - (a.productCount || 0))
        .slice(0, 3)
        .map((b) => ({ name: b.name, count: b.productCount || 0 })),
      icon: Star,
      color: "green",
    },
    {
      title: "Popular Materials",
      data: materials
        .sort((a, b) => (b.productCount || 0) - (a.productCount || 0))
        .slice(0, 3)
        .map((m) => ({ name: m.name, count: m.productCount || 0 })),
      icon: Hammer,
      color: "purple",
    },
    {
      title: "Trending Colors",
      data: colors
        .sort((a, b) => (b.productCount || 0) - (a.productCount || 0))
        .slice(0, 3)
        .map((c) => ({
          name: c.name,
          count: c.productCount || 0,
          hex: c.hexValue,
        })),
      icon: Palette,
      color: "pink",
    },
  ];

  // Catalog health metrics
  const catalogHealth = {
    completeness: Math.round(
      ((activeCategories + activeBrands + activeMaterials + activeColors) /
        Math.max(
          totalCategories + totalBrands + totalMaterials + totalColors,
          1
        )) *
        100
    ),
    diversity: Math.min(
      100,
      Math.round(
        ((totalCategories * totalBrands * totalMaterials * totalColors) /
          1000) *
          100
      )
    ),
    activity: Math.round(
      (totalProducts /
        Math.max(
          totalCategories + totalBrands + totalMaterials + totalColors,
          1
        )) *
        10
    ),
    consistency: Math.round(
      ((categories.filter((c) => c.description).length /
        Math.max(totalCategories, 1) +
        brands.filter((b) => b.description).length / Math.max(totalBrands, 1) +
        materials.filter((m) => m.description).length /
          Math.max(totalMaterials, 1) +
        colors.filter((c) => c.description).length / Math.max(totalColors, 1)) /
        4) *
        100
    ),
  };

  return (
    <>
      <PageHeaderWrapper
        title="Catalog Settings"
        description="Manage categories, brands, and materials for your product catalog in one unified interface"
      >
        <div className="flex gap-2">
          {quickActions.map((action, index) => (
            <Button
              key={index}
              variant="outline"
              size="sm"
              onClick={action.action}
              className="hidden sm:flex"
            >
              <action.icon className="mr-2 h-4 w-4" />
              {action.label}
            </Button>
          ))}
          <Button size="sm">
            <Plus className="mr-2 h-4 w-4" />
            Quick Add
          </Button>
        </div>
      </PageHeaderWrapper>

      <div className="container mx-auto mt-6 space-y-6">
        <Tabs
          value={activeTab}
          onValueChange={handleTabChange}
          className="w-full"
        >
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="categories">Categories</TabsTrigger>
            <TabsTrigger value="brands">Brands</TabsTrigger>
            <TabsTrigger value="materials">Materials</TabsTrigger>
            <TabsTrigger value="colors">Colors</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-8">
            {/* Hero Section */}
            <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-8">
              <div className="relative z-10">
                <div className="mb-4 flex items-center gap-3">
                  <div className="rounded-full bg-blue-100 p-3">
                    <Database className="h-8 w-8 text-blue-600" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900">
                      Catalog Overview
                    </h2>
                    <p className="text-gray-600">
                      Manage your entire product catalog ecosystem
                    </p>
                  </div>
                </div>
                <div className="mt-6 grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                  <div className="rounded-lg border border-white/20 bg-white/70 p-4 backdrop-blur-sm">
                    <div className="mb-2 flex items-center gap-2">
                      <Package className="h-5 w-5 text-blue-600" />
                      <span className="text-sm font-medium text-gray-700">
                        Total Products
                      </span>
                    </div>
                    <div className="text-2xl font-bold text-gray-900">
                      {Math.round(totalProducts)}
                    </div>
                    <div className="text-xs text-gray-600">
                      Across all categories
                    </div>
                  </div>
                  <div className="rounded-lg border border-white/20 bg-white/70 p-4 backdrop-blur-sm">
                    <div className="mb-2 flex items-center gap-2">
                      <Activity className="h-5 w-5 text-green-600" />
                      <span className="text-sm font-medium text-gray-700">
                        Active Items
                      </span>
                    </div>
                    <div className="text-2xl font-bold text-gray-900">
                      {activeCategories +
                        activeBrands +
                        activeMaterials +
                        activeColors}
                    </div>
                    <div className="text-xs text-gray-600">Ready for use</div>
                  </div>
                  <div className="rounded-lg border border-white/20 bg-white/70 p-4 backdrop-blur-sm">
                    <div className="mb-2 flex items-center gap-2">
                      <Target className="h-5 w-5 text-purple-600" />
                      <span className="text-sm font-medium text-gray-700">
                        Completion
                      </span>
                    </div>
                    <div className="text-2xl font-bold text-gray-900">
                      {Math.round(
                        ((activeCategories +
                          activeBrands +
                          activeMaterials +
                          activeColors) /
                          (totalCategories +
                            totalBrands +
                            totalMaterials +
                            totalColors)) *
                          100
                      ) || 0}
                      %
                    </div>
                    <div className="text-xs text-gray-600">Catalog setup</div>
                  </div>
                  <div className="rounded-lg border border-white/20 bg-white/70 p-4 backdrop-blur-sm">
                    <div className="mb-2 flex items-center gap-2">
                      <Zap className="h-5 w-5 text-orange-600" />
                      <span className="text-sm font-medium text-gray-700">
                        Health Score
                      </span>
                    </div>
                    <div className="text-2xl font-bold text-gray-900">
                      {totalCategories > 0 &&
                      totalBrands > 0 &&
                      totalMaterials > 0 &&
                      totalColors > 0
                        ? "98%"
                        : "75%"}
                    </div>
                    <div className="text-xs text-gray-600">System health</div>
                  </div>
                </div>
              </div>
              <div className="absolute right-0 top-0 h-64 w-64 rounded-full bg-gradient-to-br from-blue-200/30 to-purple-200/30 blur-3xl"></div>
            </div>

            {/* Detailed Stats */}
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              {overallStats.map((stat, index) => (
                <Card
                  key={index}
                  className="group relative overflow-hidden transition-all duration-300 hover:shadow-lg"
                >
                  <CardContent className="p-6">
                    <div className="flex items-center gap-4">
                      <div
                        className={`rounded-xl p-3 bg-${stat.color}-100 group-hover:bg-${stat.color}-200 transition-colors`}
                      >
                        <stat.icon
                          className={`h-6 w-6 text-${stat.color}-600`}
                        />
                      </div>
                      <div className="flex-1">
                        <div className="mb-1 flex items-center justify-between">
                          <p className="text-sm font-medium text-gray-600">
                            {stat.label}
                          </p>
                          <Badge variant="outline" className="text-xs">
                            {stat.active} active
                          </Badge>
                        </div>
                        <div className="mb-2 text-3xl font-bold text-gray-900">
                          {stat.value}
                        </div>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between text-xs">
                            <span className="text-gray-500">
                              {stat.description}
                            </span>
                            <span className="font-medium text-gray-700">
                              {stat.percentage}%
                            </span>
                          </div>
                          <Progress value={stat.percentage} className="h-2" />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                  <div
                    className={`absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-${stat.color}-400 to-${stat.color}-600`}
                  ></div>
                </Card>
              ))}
            </div>

            {/* Catalog Health Dashboard */}
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5 text-green-600" />
                    Catalog Health
                  </CardTitle>
                  <p className="text-sm text-gray-600">
                    Monitor the overall health and completeness of your catalog
                  </p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">
                        Completeness
                      </span>
                      <span className="text-sm font-bold text-gray-900">
                        {catalogHealth.completeness}%
                      </span>
                    </div>
                    <Progress
                      value={catalogHealth.completeness}
                      className="h-2"
                    />
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">
                        Diversity
                      </span>
                      <span className="text-sm font-bold text-gray-900">
                        {catalogHealth.diversity}%
                      </span>
                    </div>
                    <Progress value={catalogHealth.diversity} className="h-2" />
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">
                        Activity Level
                      </span>
                      <span className="text-sm font-bold text-gray-900">
                        {Math.min(100, catalogHealth.activity)}%
                      </span>
                    </div>
                    <Progress
                      value={Math.min(100, catalogHealth.activity)}
                      className="h-2"
                    />
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">
                        Data Consistency
                      </span>
                      <span className="text-sm font-bold text-gray-900">
                        {catalogHealth.consistency}%
                      </span>
                    </div>
                    <Progress
                      value={catalogHealth.consistency}
                      className="h-2"
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5 text-purple-600" />
                    Catalog Insights
                  </CardTitle>
                  <p className="text-sm text-gray-600">
                    Top performing items in your catalog
                  </p>
                </CardHeader>
                <CardContent className="space-y-4">
                  {catalogInsights.map((insight, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex items-center gap-2">
                        <insight.icon
                          className={`h-4 w-4 text-${insight.color}-600`}
                        />
                        <span className="text-sm font-medium text-gray-700">
                          {insight.title}
                        </span>
                      </div>
                      <div className="space-y-1">
                        {insight.data.map((item, idx) => (
                          <div
                            key={idx}
                            className="flex items-center justify-between text-xs"
                          >
                            <div className="flex items-center gap-2">
                              {insight.title === "Trending Colors" &&
                                "hex" in item &&
                                item.hex && (
                                  <div
                                    className="h-3 w-3 rounded-full border border-gray-300"
                                    style={{
                                      backgroundColor: item.hex as string,
                                    }}
                                  />
                                )}
                              <span className="text-gray-600">{item.name}</span>
                            </div>
                            <Badge variant="outline" className="text-xs">
                              {item.count} products
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>

            {/* Quick Actions Grid */}
            <div className="grid gap-4 md:grid-cols-4">
              <Card
                className="cursor-pointer transition-shadow hover:shadow-md"
                onClick={() => handleTabChange("categories")}
              >
                <CardContent className="p-6 text-center">
                  <LayoutGrid className="mx-auto mb-3 h-8 w-8 text-blue-600" />
                  <h3 className="mb-2 font-semibold text-gray-900">
                    Manage Categories
                  </h3>
                  <p className="mb-3 text-sm text-gray-600">
                    Organize your product catalog
                  </p>
                  <div className="flex items-center justify-center text-sm text-blue-600">
                    <span>Go to Categories</span>
                    <ArrowRight className="ml-1 h-4 w-4" />
                  </div>
                </CardContent>
              </Card>

              <Card
                className="cursor-pointer transition-shadow hover:shadow-md"
                onClick={() => handleTabChange("brands")}
              >
                <CardContent className="p-6 text-center">
                  <Tag className="mx-auto mb-3 h-8 w-8 text-green-600" />
                  <h3 className="mb-2 font-semibold text-gray-900">
                    Manage Brands
                  </h3>
                  <p className="mb-3 text-sm text-gray-600">
                    Add and organize product brands
                  </p>
                  <div className="flex items-center justify-center text-sm text-green-600">
                    <span>Go to Brands</span>
                    <ArrowRight className="ml-1 h-4 w-4" />
                  </div>
                </CardContent>
              </Card>

              <Card
                className="cursor-pointer transition-shadow hover:shadow-md"
                onClick={() => handleTabChange("materials")}
              >
                <CardContent className="p-6 text-center">
                  <Hammer className="mx-auto mb-3 h-8 w-8 text-purple-600" />
                  <h3 className="mb-2 font-semibold text-gray-900">
                    Manage Materials
                  </h3>
                  <p className="mb-3 text-sm text-gray-600">
                    Define product materials
                  </p>
                  <div className="flex items-center justify-center text-sm text-purple-600">
                    <span>Go to Materials</span>
                    <ArrowRight className="ml-1 h-4 w-4" />
                  </div>
                </CardContent>
              </Card>

              <Card
                className="cursor-pointer transition-shadow hover:shadow-md"
                onClick={() => handleTabChange("colors")}
              >
                <CardContent className="p-6 text-center">
                  <Palette className="mx-auto mb-3 h-8 w-8 text-pink-600" />
                  <h3 className="mb-2 font-semibold text-gray-900">
                    Manage Colors
                  </h3>
                  <p className="mb-3 text-sm text-gray-600">
                    Define product color options
                  </p>
                  <div className="flex items-center justify-center text-sm text-pink-600">
                    <span>Go to Colors</span>
                    <ArrowRight className="ml-1 h-4 w-4" />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Categories Tab */}
          <TabsContent value="categories" className="space-y-6">
            <div className="mb-6 rounded-lg border bg-gradient-to-r from-blue-50 to-indigo-50 p-4">
              <div className="flex items-center gap-3">
                <div className="rounded-full bg-blue-100 p-2">
                  <LayoutGrid className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">
                    Category Management
                  </h3>
                  <p className="text-sm text-gray-600">
                    Organize your products with categories and subcategories for
                    better navigation
                  </p>
                </div>
              </div>
            </div>
            <CategoryManagerEnhanced />
          </TabsContent>

          {/* Brands Tab */}
          <TabsContent value="brands" className="space-y-6">
            <div className="mb-6 rounded-lg border bg-gradient-to-r from-green-50 to-emerald-50 p-4">
              <div className="flex items-center gap-3">
                <div className="rounded-full bg-green-100 p-2">
                  <Tag className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">
                    Brand Management
                  </h3>
                  <p className="text-sm text-gray-600">
                    Create, organize, and manage product brands for your catalog
                  </p>
                </div>
              </div>
            </div>
            <BrandManagerEnhanced />
          </TabsContent>

          {/* Materials Tab */}
          <TabsContent value="materials" className="space-y-6">
            <div className="mb-6 rounded-lg border bg-gradient-to-r from-purple-50 to-violet-50 p-4">
              <div className="flex items-center gap-3">
                <div className="rounded-full bg-purple-100 p-2">
                  <Hammer className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">
                    Material Management
                  </h3>
                  <p className="text-sm text-gray-600">
                    Define and manage materials used in your products for better
                    specifications
                  </p>
                </div>
              </div>
            </div>
            <MaterialManagerEnhanced />
          </TabsContent>

          {/* Colors Tab */}
          <TabsContent value="colors" className="space-y-6">
            <div className="mb-6 rounded-lg border bg-gradient-to-r from-pink-50 to-rose-50 p-4">
              <div className="flex items-center gap-3">
                <div className="rounded-full bg-pink-100 p-2">
                  <Palette className="h-5 w-5 text-pink-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">
                    Color Management
                  </h3>
                  <p className="text-sm text-gray-600">
                    Create and manage color options for your products to enhance
                    customer choice and visual appeal
                  </p>
                </div>
              </div>
            </div>
            <ColorManagerEnhanced />
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
}
