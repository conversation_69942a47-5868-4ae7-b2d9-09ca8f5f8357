export type Brand = {
  _id: string;
  name: string;
  description: string;
  slug: string;
  logo?: string;
  website?: string;
  color?: string;
  isActive: boolean;
  productCount: number;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
};

export type CreateBrandDto = {
  name: string;
  description?: string;
  logo?: string;
  website?: string;
  color?: string;
  isActive?: boolean;
  sortOrder?: number;
};

export type UpdateBrandDto = {
  name?: string;
  description?: string;
  slug?: string;
  logo?: string;
  website?: string;
  color?: string;
  isActive?: boolean;
  sortOrder?: number;
};

export type BrandFilters = {
  isActive?: boolean;
  search?: string;
};

export type BrandsResponse = {
  brands: Brand[];
  total: number;
  page: number;
  totalPages: number;
};
