[{"C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\static_page\\app\\(root)\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\static_page\\app\\layout.tsx": "2", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\static_page\\components\\AboutSection.tsx": "3", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\static_page\\components\\ContactSection.tsx": "4", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\static_page\\components\\HeroSection.tsx": "5", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\static_page\\components\\Navbar.tsx": "6", "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\static_page\\components\\ServicesSection.tsx": "7"}, {"size": 1392, "mtime": 1743885716707, "results": "8", "hashOfConfig": "9"}, {"size": 829, "mtime": 1743885716708, "results": "10", "hashOfConfig": "9"}, {"size": 1965, "mtime": 1743957909142, "results": "11", "hashOfConfig": "9"}, {"size": 3435, "mtime": 1743885727828, "results": "12", "hashOfConfig": "9"}, {"size": 2595, "mtime": 1743885716710, "results": "13", "hashOfConfig": "9"}, {"size": 4271, "mtime": 1743885716710, "results": "14", "hashOfConfig": "9"}, {"size": 2100, "mtime": 1743885716710, "results": "15", "hashOfConfig": "9"}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "17o17pe", {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\static_page\\app\\(root)\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\static_page\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\static_page\\components\\AboutSection.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\static_page\\components\\ContactSection.tsx", ["37"], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\static_page\\components\\HeroSection.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\static_page\\components\\Navbar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Learning\\Codes\\static_page\\components\\ServicesSection.tsx", [], [], {"ruleId": "38", "severity": 1, "message": "39", "line": 15, "column": 5, "nodeType": "40", "messageId": "41", "endLine": 15, "endColumn": 16, "suggestions": "42"}, "no-console", "Unexpected console statement. Only these console methods are allowed: warn, error.", "MemberExpression", "limited", ["43"], {"fix": "44", "messageId": "45", "data": "46", "desc": "47"}, {"range": "48", "text": "49"}, "removeConsole", {"propertyName": "50"}, "Remove the console.log().", [431, 481], "", "log"]