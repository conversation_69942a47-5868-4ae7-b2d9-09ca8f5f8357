"use client";

import React, { useEffect, useState } from "react";

import {
  Archive,
  Check,
  Copy,
  Edit,
  Eye,
  Filter,
  Grid3X3,
  Hammer,
  List,
  MoreHorizontal,
  Package,
  Pencil,
  Plus,
  Save,
  Search,
  SortAsc,
  SortDesc,
  Star,
  Trash2,
  TrendingUp,
  X,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { useMaterials } from "@/hooks/useMaterials";
import { CreateMaterialDto, Material } from "@/lib/api/materials";

interface MaterialManagerEnhancedProps {
  initialMaterials?: Material[];
  onMaterialsChange?: (materials: Material[]) => void;
}

/**
 * Enhanced component for managing product materials with real API integration
 */
export const MaterialManagerEnhanced = ({
  initialMaterials = [],
  onMaterialsChange,
}: MaterialManagerEnhancedProps) => {
  const router = useRouter();

  // API hooks
  const {
    materials: apiMaterials,
    loading,
    error,
    createMaterial,
    updateMaterial,
    deleteMaterial,
    refreshMaterials,
  } = useMaterials();

  // Local state
  const [materials, setMaterials] = useState<Material[]>(initialMaterials);
  const [newMaterial, setNewMaterial] = useState<Partial<CreateMaterialDto>>({
    name: "",
    description: "",
    type: "natural",
    properties: {
      durability: "medium",
      waterResistant: false,
      recyclable: true,
      weight: "medium",
    },
    isActive: true,
  });
  const [editingMaterialId, setEditingMaterialId] = useState<string | null>(
    null
  );
  const [editForm, setEditForm] = useState<Partial<Material>>({});
  const [showAddForm, setShowAddForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [showInactive, setShowInactive] = useState(false);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [sortBy, setSortBy] = useState<"name" | "created" | "products">("name");

  // Update local materials when API data changes
  useEffect(() => {
    setMaterials(apiMaterials);
    if (onMaterialsChange) {
      onMaterialsChange(apiMaterials);
    }
  }, [apiMaterials, onMaterialsChange]);

  // Filter and sort materials
  const filteredMaterials = materials
    .filter((material) => {
      const matchesSearch = material.name
        .toLowerCase()
        .includes(searchTerm.toLowerCase());
      const matchesStatus = showInactive || material.isActive;
      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case "name":
          return a.name.localeCompare(b.name);
        case "created":
          return (
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          );
        case "products":
          return (b.productCount || 0) - (a.productCount || 0);
        default:
          return 0;
      }
    });

  // Handle create material
  const handleCreateMaterial = async () => {
    if (!newMaterial.name?.trim()) {
      toast.error("Material name is required");
      return;
    }

    try {
      await createMaterial(newMaterial as CreateMaterialDto);
      setNewMaterial({
        name: "",
        description: "",
        type: "natural",
        properties: {
          durability: "medium",
          waterResistant: false,
          recyclable: true,
          weight: "medium",
        },
        isActive: true,
      });
      setShowAddForm(false);
      refreshMaterials();
    } catch (error) {
      console.error("Error creating material:", error);
    }
  };

  // Handle update material
  const handleUpdateMaterial = async () => {
    if (!editingMaterialId || !editForm.name?.trim()) {
      toast.error("Material name is required");
      return;
    }

    try {
      await updateMaterial(editingMaterialId, editForm);
      setEditingMaterialId(null);
      setEditForm({});
      refreshMaterials();
    } catch (error) {
      console.error("Error updating material:", error);
    }
  };

  // Handle delete material
  const handleDeleteMaterial = async (materialId: string) => {
    if (!confirm("Are you sure you want to delete this material?")) {
      return;
    }

    try {
      await deleteMaterial(materialId);
      refreshMaterials();
    } catch (error) {
      console.error("Error deleting material:", error);
    }
  };

  // Statistics
  const stats = {
    total: materials.length,
    active: materials.filter((m) => m.isActive).length,
    natural: materials.filter((m) => m.type === "natural").length,
    synthetic: materials.filter((m) => m.type === "synthetic").length,
  };

  if (error) {
    return (
      <div className="rounded-lg border border-red-200 bg-red-50 p-4">
        <p className="text-red-600">Error loading materials: {error}</p>
        <Button onClick={refreshMaterials} className="mt-2">
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Statistics */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-purple-100 p-2">
                <Hammer className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Materials</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-green-100 p-2">
                <TrendingUp className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Active Materials</p>
                <p className="text-2xl font-bold">{stats.active}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-blue-100 p-2">
                <Star className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Natural</p>
                <p className="text-2xl font-bold">{stats.natural}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-orange-100 p-2">
                <Package className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Synthetic</p>
                <p className="text-2xl font-bold">{stats.synthetic}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Controls */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            {/* Search and Filters */}
            <div className="flex flex-1 gap-4">
              <div className="relative max-w-md flex-1">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Search materials..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              <Select
                value={sortBy}
                onValueChange={(value: any) => setSortBy(value)}
              >
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="created">Created</SelectItem>
                  <SelectItem value="products">Products</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Actions */}
            <div className="flex gap-2">
              <div className="flex items-center gap-2">
                <Switch
                  checked={showInactive}
                  onCheckedChange={setShowInactive}
                />
                <Label className="text-sm">Show inactive</Label>
              </div>

              <div className="flex gap-1">
                <Button
                  variant={viewMode === "grid" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === "list" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setViewMode("list")}
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>

              <Button onClick={() => setShowAddForm(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add Material
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Add Material Form */}
      {showAddForm && (
        <Card>
          <CardHeader>
            <CardTitle>Add New Material</CardTitle>
            <CardDescription>
              Create a new material for your product catalog
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <Label htmlFor="material-name">Name *</Label>
                <Input
                  id="material-name"
                  value={newMaterial.name || ""}
                  onChange={(e) =>
                    setNewMaterial({ ...newMaterial, name: e.target.value })
                  }
                  placeholder="e.g., Organic Cotton"
                />
              </div>
              <div>
                <Label htmlFor="material-type">Type</Label>
                <Select
                  value={newMaterial.type}
                  onValueChange={(value: any) =>
                    setNewMaterial({ ...newMaterial, type: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="natural">Natural</SelectItem>
                    <SelectItem value="synthetic">Synthetic</SelectItem>
                    <SelectItem value="blend">Blend</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="material-description">Description</Label>
              <Textarea
                id="material-description"
                value={newMaterial.description || ""}
                onChange={(e) =>
                  setNewMaterial({
                    ...newMaterial,
                    description: e.target.value,
                  })
                }
                placeholder="Describe the material properties and uses..."
                rows={3}
              />
            </div>

            <div className="flex items-center gap-2">
              <Switch
                checked={newMaterial.isActive}
                onCheckedChange={(checked) =>
                  setNewMaterial({ ...newMaterial, isActive: checked })
                }
              />
              <Label>Active</Label>
            </div>

            <div className="flex gap-2 pt-2">
              <Button onClick={handleCreateMaterial}>
                <Check className="mr-2 h-4 w-4" />
                Add Material
              </Button>
              <Button variant="outline" onClick={() => setShowAddForm(false)}>
                <X className="mr-2 h-4 w-4" />
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Materials Display */}
      {loading ? (
        <div className="space-y-2">
          {[...Array(5)].map((_, i) => (
            <Skeleton key={i} className="h-20 w-full" />
          ))}
        </div>
      ) : filteredMaterials.length === 0 ? (
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-muted-foreground">No materials found</p>
            {searchTerm && (
              <Button
                variant="link"
                onClick={() => setSearchTerm("")}
                className="mt-2"
              >
                Clear search
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div
          className={
            viewMode === "grid"
              ? "grid gap-4 md:grid-cols-2 lg:grid-cols-3"
              : "space-y-3"
          }
        >
          {filteredMaterials.map((material) => (
            <Card
              key={material._id}
              className={`transition-shadow hover:shadow-md ${
                !material.isActive ? "opacity-60" : ""
              }`}
            >
              <CardContent className="p-4">
                {editingMaterialId === material._id ? (
                  // Edit form
                  <div className="space-y-3">
                    <Input
                      value={editForm.name || ""}
                      onChange={(e) =>
                        setEditForm({ ...editForm, name: e.target.value })
                      }
                      placeholder="Material name"
                    />
                    <Textarea
                      value={editForm.description || ""}
                      onChange={(e) =>
                        setEditForm({
                          ...editForm,
                          description: e.target.value,
                        })
                      }
                      placeholder="Description"
                      rows={2}
                    />
                    <div className="flex gap-2">
                      <Button size="sm" onClick={handleUpdateMaterial}>
                        <Save className="mr-2 h-4 w-4" />
                        Save
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          setEditingMaterialId(null);
                          setEditForm({});
                        }}
                      >
                        <X className="mr-2 h-4 w-4" />
                        Cancel
                      </Button>
                    </div>
                  </div>
                ) : (
                  // Display mode
                  <div>
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900">
                          {material.name}
                        </h3>
                        {material.description && (
                          <p className="mt-1 text-sm text-gray-600">
                            {material.description}
                          </p>
                        )}
                        <div className="mt-2 flex items-center gap-2">
                          <Badge variant="outline" className="text-xs">
                            {material.type}
                          </Badge>
                          {!material.isActive && (
                            <Badge variant="secondary" className="text-xs">
                              Inactive
                            </Badge>
                          )}
                          <span className="text-xs text-gray-500">
                            {material.productCount || 0} products
                          </span>
                        </div>
                      </div>

                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => {
                              setEditingMaterialId(material._id);
                              setEditForm(material);
                            }}
                          >
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Eye className="mr-2 h-4 w-4" />
                            View Products
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Copy className="mr-2 h-4 w-4" />
                            Duplicate
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => handleDeleteMaterial(material._id)}
                            className="text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};
