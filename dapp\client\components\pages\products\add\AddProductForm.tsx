"use client";

import { useEffect, useState } from "react";

import {
  AlertCircle,
  ArrowLeft,
  CheckCircle,
  Clock,
  Eye,
  Package,
  Save,
  Sparkles,
  Zap,
} from "lucide-react";
import { toast } from "sonner";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useProductFormWithSections } from "@/hooks/useProductFormWithSections";

import { FormNavigation } from "./FormNavigation";
import { FormSectionRenderer } from "./FormSectionRenderer";
import { getFormSections } from "./FormSections";
import { MetadataGuide } from "./MetadataGuide";
import { SectionNavigator } from "./TabSections";

/**
 * Enhanced multi-step form for adding a new product
 * Features improved UI, better visual feedback, and user-friendly design
 */
export const AddProductForm = () => {
  const [showPreview, setShowPreview] = useState(false);
  const [saveStatus, setSaveStatus] = useState<
    "idle" | "saving" | "saved" | "error"
  >("idle");

  const {
    // Form state
    register,
    handleSubmit,
    setValue,
    watch,
    errors,

    // Section navigation
    currentSection,
    completedSections,
    SECTIONS_ORDER,
    progress,
    handleNext,
    handlePrevious,
    handleSectionChange,

    // Additional state
    tags,
    setTags,
    images,
    setImages,
    imageAltTexts,
    setImageAltTexts,

    // Form submission
    onSubmit,
    creating,
    validateAllSections,
  } = useProductFormWithSections();

  // Get formatted sections for the navigator
  const sections = getFormSections(completedSections);

  // Check if we're on the last section
  const isLastSection =
    currentSection === SECTIONS_ORDER[SECTIONS_ORDER.length - 1];

  // Get current section info
  const currentSectionInfo = sections.find((s) => s.id === currentSection);
  const completionPercentage = Math.round(
    (completedSections.length / SECTIONS_ORDER.length) * 100
  );

  // Update form values when images or tags change
  useEffect(() => {
    if (images.length > 0) {
      setValue("mainImage", images[0].url);
    }
  }, [images, setValue]);

  useEffect(() => {
    setValue("tags", tags);
  }, [tags, setValue]);

  // Auto-save functionality
  const handleAutoSave = async () => {
    setSaveStatus("saving");
    // Simulate auto-save
    setTimeout(() => {
      setSaveStatus("saved");
      setTimeout(() => setSaveStatus("idle"), 2000);
    }, 1000);
  };

  // Get status icon based on completion
  const getStatusIcon = (sectionId: string) => {
    if (completedSections.includes(sectionId)) {
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    }
    if (sectionId === currentSection) {
      return <Clock className="h-4 w-4 text-blue-500" />;
    }
    return <AlertCircle className="h-4 w-4 text-gray-300" />;
  };

  return (
    <div className="mx-auto max-w-7xl space-y-8">
      {/* Enhanced Header with Progress */}
      <div className="relative overflow-hidden rounded-xl bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 p-8">
        <div className="relative z-10">
          <div className="mb-6 flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="rounded-full bg-blue-100 p-3">
                <Package className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Create New Product
                </h1>
                <p className="text-gray-600">
                  {currentSectionInfo?.title} • Step{" "}
                  {SECTIONS_ORDER.indexOf(currentSection) + 1} of{" "}
                  {SECTIONS_ORDER.length}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              {/* Auto-save status */}
              <div className="flex items-center gap-2">
                {saveStatus === "saving" && (
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-600 border-t-transparent" />
                    Saving...
                  </div>
                )}
                {saveStatus === "saved" && (
                  <div className="flex items-center gap-2 text-sm text-green-600">
                    <CheckCircle className="h-4 w-4" />
                    Saved
                  </div>
                )}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowPreview(!showPreview)}
              >
                <Eye className="mr-2 h-4 w-4" />
                {showPreview ? "Hide Preview" : "Preview"}
              </Button>

              <Button variant="outline" size="sm" onClick={handleAutoSave}>
                <Save className="mr-2 h-4 w-4" />
                Save Draft
              </Button>
            </div>
          </div>

          {/* Enhanced Progress Bar */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">
                Progress: {completionPercentage}% Complete
              </span>
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                {completedSections.length}/{SECTIONS_ORDER.length} sections
              </Badge>
            </div>

            <div className="relative">
              <Progress value={progress} className="h-3 bg-gray-200" />
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-xs font-medium text-white drop-shadow">
                  {completionPercentage}%
                </span>
              </div>
            </div>

            {/* Section indicators */}
            <div className="flex justify-between">
              {sections.map((section, index) => (
                <div
                  key={section.id}
                  className="flex flex-col items-center gap-1"
                >
                  {getStatusIcon(section.id)}
                  <span className="max-w-16 truncate text-center text-xs text-gray-500">
                    {section.title}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Background decoration */}
        <div className="absolute right-0 top-0 -mr-4 -mt-4 h-24 w-24 rounded-full bg-blue-200 opacity-20" />
        <div className="absolute bottom-0 left-0 -mb-4 -ml-4 h-16 w-16 rounded-full bg-purple-200 opacity-20" />
      </div>

      <div className="grid grid-cols-1 gap-8 lg:grid-cols-4">
        {/* Enhanced Section Navigator */}
        <div className="lg:col-span-1">
          <Card className="sticky top-4 overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 pb-4">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Sparkles className="h-5 w-5 text-blue-600" />
                Form Sections
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="space-y-1 p-4">
                {sections.map((section, index) => (
                  <button
                    key={section.id}
                    onClick={() => handleSectionChange(section.id)}
                    className={`flex w-full items-center gap-3 rounded-lg p-3 text-left transition-all duration-200 ${
                      currentSection === section.id
                        ? "border-2 border-blue-200 bg-blue-50 text-blue-900 shadow-sm"
                        : section.isCompleted
                          ? "border border-green-200 bg-green-50 text-green-900 hover:bg-green-100"
                          : "border border-gray-200 bg-gray-50 text-gray-600 hover:bg-gray-100"
                    }`}
                  >
                    <div className="flex-shrink-0">
                      {getStatusIcon(section.id)}
                    </div>

                    <div className="min-w-0 flex-1">
                      <div className="flex items-center gap-2">
                        <span className="truncate text-sm font-medium">
                          {section.title}
                        </span>
                        {section.isOptional && (
                          <Badge variant="outline" className="text-xs">
                            Optional
                          </Badge>
                        )}
                      </div>
                      <div className="mt-1 text-xs text-gray-500">
                        Step {index + 1} of {sections.length}
                      </div>
                    </div>

                    {currentSection === section.id && (
                      <Zap className="h-4 w-4 animate-pulse text-blue-500" />
                    )}
                  </button>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Enhanced Form Content */}
        <div className="space-y-6 lg:col-span-3">
          {/* Current Section Header */}
          <Card className="border-l-4 border-l-blue-500">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {currentSectionInfo?.icon}
                  <div>
                    <CardTitle className="text-xl">
                      {currentSectionInfo?.title}
                    </CardTitle>
                    <p className="mt-1 text-sm text-gray-600">
                      Complete this section to continue to the next step
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  {completedSections.includes(currentSection) && (
                    <Badge className="bg-green-100 text-green-800">
                      <CheckCircle className="mr-1 h-3 w-3" />
                      Completed
                    </Badge>
                  )}
                  <Badge variant="outline">
                    Step {SECTIONS_ORDER.indexOf(currentSection) + 1}
                  </Badge>
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Form Content Card */}
          <Card className="overflow-hidden shadow-lg">
            <CardContent className="p-8">
              <form
                onSubmit={handleSubmit(onSubmit, (errors) => {
                  console.error("Form validation errors:", errors);
                  toast.error("Please fix the form errors before submitting");
                })}
                className="space-y-8"
              >
                {/* Render the current form section */}
                <div className="min-h-[400px]">
                  <FormSectionRenderer
                    currentSection={currentSection}
                    register={register}
                    errors={errors}
                    setValue={setValue}
                    watch={watch}
                    tags={tags}
                    setTags={setTags}
                    images={images}
                    setImages={setImages}
                    imageAltTexts={imageAltTexts}
                    setImageAltTexts={setImageAltTexts}
                  />
                </div>

                {/* Validation Error Notices for All Sections */}

                {/* Basic Info Section Validation */}
                {currentSection === "basic-info" &&
                  (errors.name ||
                    errors.brand ||
                    errors.description ||
                    errors.productType) && (
                    <div className="rounded-lg border border-red-200 bg-red-50 p-4">
                      <div className="flex items-start gap-3">
                        <AlertCircle className="mt-0.5 h-5 w-5 text-red-600" />
                        <div>
                          <h3 className="font-semibold text-red-900">
                            Cannot Proceed - Missing Required Information
                          </h3>
                          <p className="mt-1 text-sm text-red-800">
                            Please complete the following required fields before
                            continuing:
                          </p>
                          <ul className="mt-2 list-inside list-disc text-sm text-red-700">
                            {errors.name && <li>Product Name</li>}
                            {errors.brand && <li>Brand</li>}
                            {errors.description && <li>Product Description</li>}
                            {errors.productType && <li>Product Type</li>}
                          </ul>
                          <div className="mt-2 text-xs text-red-600">
                            💡 These fields are essential for product
                            identification and categorization.
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                {/* Pricing Section Validation */}
                {currentSection === "pricing" &&
                  (errors.price ||
                    errors.currency ||
                    errors.taxStatus ||
                    errors.costPrice) && (
                    <div className="rounded-lg border border-red-200 bg-red-50 p-4">
                      <div className="flex items-start gap-3">
                        <AlertCircle className="mt-0.5 h-5 w-5 text-red-600" />
                        <div>
                          <h3 className="font-semibold text-red-900">
                            Cannot Proceed - Missing Required Information
                          </h3>
                          <p className="mt-1 text-sm text-red-800">
                            Please complete the following required fields before
                            continuing:
                          </p>
                          <ul className="mt-2 list-inside list-disc text-sm text-red-700">
                            {errors.price && <li>Regular Price</li>}
                            {errors.currency && <li>Currency</li>}
                            {errors.taxStatus && <li>Tax Status</li>}
                            {errors.costPrice && (
                              <li>Cost Price (Business Intelligence)</li>
                            )}
                          </ul>
                          <div className="mt-2 text-xs text-red-600">
                            💡 These fields are essential for business analytics
                            and profit tracking.
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                {/* Inventory Section Validation */}
                {currentSection === "inventory" &&
                  (errors.stock ||
                    errors.condition ||
                    errors.stockManagement) && (
                    <div className="rounded-lg border border-red-200 bg-red-50 p-4">
                      <div className="flex items-start gap-3">
                        <AlertCircle className="mt-0.5 h-5 w-5 text-red-600" />
                        <div>
                          <h3 className="font-semibold text-red-900">
                            Cannot Proceed - Missing Required Information
                          </h3>
                          <p className="mt-1 text-sm text-red-800">
                            Please complete the following required fields before
                            continuing:
                          </p>
                          <ul className="mt-2 list-inside list-disc text-sm text-red-700">
                            {errors.stock && <li>Stock Quantity</li>}
                            {errors.condition && <li>Product Condition</li>}
                            {errors.stockManagement && (
                              <li>Stock Management</li>
                            )}
                          </ul>
                          <div className="mt-2 text-xs text-red-600">
                            💡 These fields are essential for inventory tracking
                            and order fulfillment.
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                {/* Details Section Validation */}
                {currentSection === "details" &&
                  (errors.category ||
                    errors.material ||
                    errors.tags ||
                    errors.color) && (
                    <div className="rounded-lg border border-red-200 bg-red-50 p-4">
                      <div className="flex items-start gap-3">
                        <AlertCircle className="mt-0.5 h-5 w-5 text-red-600" />
                        <div>
                          <h3 className="font-semibold text-red-900">
                            Cannot Proceed - Missing Required Information
                          </h3>
                          <p className="mt-1 text-sm text-red-800">
                            Please complete the following required fields before
                            continuing:
                          </p>
                          <ul className="mt-2 list-inside list-disc text-sm text-red-700">
                            {errors.category && <li>Product Category</li>}
                            {errors.material && <li>Product Material</li>}
                            {errors.tags && (
                              <li>Product Tags (at least one)</li>
                            )}
                            {errors.color && <li>Product Color</li>}
                          </ul>
                          <div className="mt-2 text-xs text-red-600">
                            💡 Category helps organize products for customers,
                            Material information is essential for business
                            intelligence, Tags improve search visibility, and
                            Color enhances product presentation and customer
                            choice.
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                {/* Media Section Validation */}
                {currentSection === "media" &&
                  (errors.mainImage ||
                    (images.length > 0 &&
                      images.some(
                        (_, index) =>
                          !imageAltTexts[index] ||
                          imageAltTexts[index].trim() === ""
                      ))) && (
                    <div className="rounded-lg border border-red-200 bg-red-50 p-4">
                      <div className="flex items-start gap-3">
                        <AlertCircle className="mt-0.5 h-5 w-5 text-red-600" />
                        <div>
                          <h3 className="font-semibold text-red-900">
                            Cannot Proceed - Missing Required Information
                          </h3>
                          <p className="mt-1 text-sm text-red-800">
                            Please complete the following required fields before
                            continuing:
                          </p>
                          <ul className="mt-2 list-inside list-disc text-sm text-red-700">
                            {errors.mainImage && <li>Main Product Image</li>}
                            {images.length > 0 &&
                              images.some(
                                (_, index) =>
                                  !imageAltTexts[index] ||
                                  imageAltTexts[index].trim() === ""
                              ) && (
                                <li>
                                  Alt text for all images (required for
                                  accessibility and SEO)
                                </li>
                              )}
                          </ul>
                          <div className="mt-2 text-xs text-red-600">
                            💡 Product images and alt text are essential for
                            customer engagement, accessibility, and SEO
                            optimization.
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                {/* Shipping Section Validation */}
                {currentSection === "shipping" &&
                  (errors.shippingClass ||
                    errors.shippingCost ||
                    errors.shippingTime) && (
                    <div className="rounded-lg border border-red-200 bg-red-50 p-4">
                      <div className="flex items-start gap-3">
                        <AlertCircle className="mt-0.5 h-5 w-5 text-red-600" />
                        <div>
                          <h3 className="font-semibold text-red-900">
                            Cannot Proceed - Missing Required Information
                          </h3>
                          <p className="mt-1 text-sm text-red-800">
                            Please complete the following required fields before
                            continuing:
                          </p>
                          <ul className="mt-2 list-inside list-disc text-sm text-red-700">
                            {errors.shippingClass && <li>Shipping Class</li>}
                            {errors.shippingCost && <li>Shipping Cost</li>}
                            {errors.shippingTime && (
                              <li>Estimated Shipping Time</li>
                            )}
                          </ul>
                          <div className="mt-2 text-xs text-red-600">
                            💡 Complete shipping information helps customers
                            make informed purchasing decisions and sets proper
                            delivery expectations.
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                {/* Availability Section Validation */}
                {currentSection === "availability" && errors.status && (
                  <div className="rounded-lg border border-red-200 bg-red-50 p-4">
                    <div className="flex items-start gap-3">
                      <AlertCircle className="mt-0.5 h-5 w-5 text-red-600" />
                      <div>
                        <h3 className="font-semibold text-red-900">
                          Cannot Proceed - Missing Required Information
                        </h3>
                        <p className="mt-1 text-sm text-red-800">
                          Please complete the following required fields before
                          continuing:
                        </p>
                        <ul className="mt-2 list-inside list-disc text-sm text-red-700">
                          {errors.status && <li>Product Status</li>}
                        </ul>
                        <div className="mt-2 text-xs text-red-600">
                          💡 Product status determines visibility and
                          availability to customers.
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Enhanced Navigation Buttons */}
                <div className="flex items-center justify-between border-t border-gray-200 pt-6">
                  <div className="flex items-center gap-3">
                    {SECTIONS_ORDER.indexOf(currentSection) > 0 && (
                      <Button
                        type="button"
                        variant="outline"
                        onClick={handlePrevious}
                        className="flex items-center gap-2"
                      >
                        <ArrowLeft className="h-4 w-4" />
                        Previous
                      </Button>
                    )}
                  </div>

                  <div className="flex items-center gap-3">
                    <Button
                      type="button"
                      variant="ghost"
                      onClick={handleAutoSave}
                      className="text-gray-600"
                    >
                      <Save className="mr-2 h-4 w-4" />
                      Save Draft
                    </Button>

                    {isLastSection ? (
                      <Button
                        type="submit"
                        disabled={creating}
                        onClick={async (e) => {
                          console.log(
                            "🔍 Submit button clicked on last section:",
                            currentSection
                          );
                          console.log("🔍 Is last section:", isLastSection);
                          console.log(
                            "🔍 Current section index:",
                            SECTIONS_ORDER.indexOf(currentSection)
                          );
                          console.log(
                            "🔍 Total sections:",
                            SECTIONS_ORDER.length
                          );

                          // Pre-validate before submission
                          const isValid = await validateAllSections();
                          if (!isValid) {
                            e.preventDefault();
                            toast.error(
                              "Please complete all required fields before creating the product"
                            );
                          }
                        }}
                        className="bg-gradient-to-r from-green-600 to-green-700 px-8 text-white hover:from-green-700 hover:to-green-800 disabled:opacity-50"
                      >
                        {creating ? (
                          <>
                            <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                            Creating Product...
                          </>
                        ) : (
                          <>
                            <Package className="mr-2 h-4 w-4" />
                            Create Product
                          </>
                        )}
                      </Button>
                    ) : (
                      <Button
                        type="button"
                        onClick={(e) => {
                          console.log(
                            "🔍 Continue button clicked on section:",
                            currentSection
                          );
                          console.log("🔍 Is last section:", isLastSection);
                          console.log(
                            "🔍 Current section index:",
                            SECTIONS_ORDER.indexOf(currentSection)
                          );
                          console.log(
                            "🔍 Next section would be:",
                            SECTIONS_ORDER[
                              SECTIONS_ORDER.indexOf(currentSection) + 1
                            ]
                          );
                          e.preventDefault(); // Prevent any form submission
                          handleNext();
                        }}
                        className="bg-gradient-to-r from-blue-600 to-blue-700 px-8 text-white hover:from-blue-700 hover:to-blue-800"
                      >
                        Continue
                        <ArrowLeft className="ml-2 h-4 w-4 rotate-180" />
                      </Button>
                    )}
                  </div>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Enhanced Metadata Guide */}
      {currentSection === "basic-info" && (
        <Card className="border-amber-200 bg-gradient-to-r from-amber-50 to-orange-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-amber-800">
              <Sparkles className="h-5 w-5" />
              Quick Tips for Better Products
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="flex items-start gap-3">
                <div className="rounded-full bg-amber-100 p-2">
                  <Package className="h-4 w-4 text-amber-600" />
                </div>
                <div>
                  <h4 className="font-medium text-amber-900">
                    Clear Product Names
                  </h4>
                  <p className="mt-1 text-sm text-amber-700">
                    Use descriptive names that customers will search for
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="rounded-full bg-amber-100 p-2">
                  <Eye className="h-4 w-4 text-amber-600" />
                </div>
                <div>
                  <h4 className="font-medium text-amber-900">Quality Images</h4>
                  <p className="mt-1 text-sm text-amber-700">
                    High-resolution images increase conversion rates
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="rounded-full bg-amber-100 p-2">
                  <Zap className="h-4 w-4 text-amber-600" />
                </div>
                <div>
                  <h4 className="font-medium text-amber-900">
                    Detailed Descriptions
                  </h4>
                  <p className="mt-1 text-sm text-amber-700">
                    Include features, benefits, and specifications
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
