import { Order } from "../models/order.model";
import {
  CreateOrderDto,
  UpdateOrderDto,
  OrderFilters,
  OrderDocument,
  OrderStats,
  OrderStatus,
  PaymentStatus,
  ShippingStatus,
} from "../types/order.types";

/**
 * Service class for handling order-related business logic
 */
export class OrderService {
  /**
   * Create a new order
   */
  async createOrder(orderData: CreateOrderDto): Promise<OrderDocument> {
    try {
      // Calculate pricing
      const subtotal = orderData.items.reduce(
        (sum, item) => sum + item.price * item.quantity,
        0
      );
      
      const shippingCost = orderData.shipping.cost;
      const tax = subtotal * 0.1; // 10% tax rate - should be configurable
      const discount = 0; // No discount for now
      const totalAmount = subtotal + shippingCost + tax - discount;

      const order = new Order({
        customer: orderData.customer,
        items: orderData.items,
        subtotal,
        shippingCost,
        tax,
        discount,
        totalAmount,
        currency: orderData.currency || "EUR",
        shippingAddress: orderData.shippingAddress,
        billingAddress: orderData.billingAddress || orderData.shippingAddress,
        payment: {
          method: orderData.payment.method,
          status: "pending",
          transactionId: orderData.payment.transactionId,
        },
        shipping: {
          method: orderData.shipping.method,
          cost: orderData.shipping.cost,
        },
        notes: orderData.notes,
        placedAt: new Date(),
      });

      return await order.save();
    } catch (error) {
      throw new Error(`Failed to create order: ${error}`);
    }
  }

  /**
   * Get all orders with optional filtering and pagination
   */
  async getOrders(
    filters: OrderFilters = {},
    page: number = 1,
    limit: number = 20,
    sortBy: string = "placedAt",
    sortOrder: "asc" | "desc" = "desc"
  ): Promise<{ orders: OrderDocument[]; total: number; page: number; totalPages: number }> {
    try {
      const query: any = {};

      // Apply filters
      if (filters.status) {
        query.status = Array.isArray(filters.status) 
          ? { $in: filters.status } 
          : filters.status;
      }

      if (filters.paymentStatus) {
        query.paymentStatus = Array.isArray(filters.paymentStatus)
          ? { $in: filters.paymentStatus }
          : filters.paymentStatus;
      }

      if (filters.shippingStatus) {
        query.shippingStatus = Array.isArray(filters.shippingStatus)
          ? { $in: filters.shippingStatus }
          : filters.shippingStatus;
      }

      if (filters.customerId) {
        query["customer.customerId"] = filters.customerId;
      }

      if (filters.customerEmail) {
        query["customer.email"] = filters.customerEmail;
      }

      if (filters.orderNumber) {
        query.orderNumber = { $regex: filters.orderNumber, $options: "i" };
      }

      if (filters.dateFrom || filters.dateTo) {
        query.placedAt = {};
        if (filters.dateFrom) query.placedAt.$gte = filters.dateFrom;
        if (filters.dateTo) query.placedAt.$lte = filters.dateTo;
      }

      if (filters.minAmount || filters.maxAmount) {
        query.totalAmount = {};
        if (filters.minAmount) query.totalAmount.$gte = filters.minAmount;
        if (filters.maxAmount) query.totalAmount.$lte = filters.maxAmount;
      }

      if (filters.search) {
        query.$or = [
          { orderNumber: { $regex: filters.search, $options: "i" } },
          { "customer.name": { $regex: filters.search, $options: "i" } },
          { "customer.email": { $regex: filters.search, $options: "i" } },
          { "items.name": { $regex: filters.search, $options: "i" } },
        ];
      }

      const skip = (page - 1) * limit;
      const sort: any = {};
      sort[sortBy] = sortOrder === "asc" ? 1 : -1;

      const [orders, total] = await Promise.all([
        Order.find(query)
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        Order.countDocuments(query),
      ]);

      return {
        orders: orders as OrderDocument[],
        total,
        page,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error) {
      throw new Error(`Failed to get orders: ${error}`);
    }
  }

  /**
   * Get order by ID
   */
  async getOrderById(orderId: string): Promise<OrderDocument | null> {
    try {
      return await Order.findById(orderId).lean();
    } catch (error) {
      throw new Error(`Failed to get order: ${error}`);
    }
  }

  /**
   * Get order by order number
   */
  async getOrderByNumber(orderNumber: string): Promise<OrderDocument | null> {
    try {
      return await Order.findOne({ orderNumber }).lean();
    } catch (error) {
      throw new Error(`Failed to get order: ${error}`);
    }
  }

  /**
   * Update an order
   */
  async updateOrder(orderId: string, updateData: UpdateOrderDto): Promise<OrderDocument | null> {
    try {
      const order = await Order.findByIdAndUpdate(
        orderId,
        { $set: updateData },
        { new: true, runValidators: true }
      ).lean();

      if (!order) {
        throw new Error("Order not found");
      }

      return order;
    } catch (error) {
      throw new Error(`Failed to update order: ${error}`);
    }
  }

  /**
   * Delete an order (soft delete by setting status to cancelled)
   */
  async deleteOrder(orderId: string): Promise<boolean> {
    try {
      const order = await Order.findById(orderId);
      
      if (!order) {
        throw new Error("Order not found");
      }

      // Only allow deletion of pending orders
      if (order.status !== "pending") {
        throw new Error("Cannot delete order that is not pending");
      }

      await Order.findByIdAndUpdate(orderId, { 
        status: "cancelled",
        shippingStatus: "cancelled" 
      });

      return true;
    } catch (error) {
      throw new Error(`Failed to delete order: ${error}`);
    }
  }

  /**
   * Get order statistics
   */
  async getOrderStats(dateFrom?: Date, dateTo?: Date): Promise<OrderStats> {
    try {
      const matchStage: any = {};
      
      if (dateFrom || dateTo) {
        matchStage.placedAt = {};
        if (dateFrom) matchStage.placedAt.$gte = dateFrom;
        if (dateTo) matchStage.placedAt.$lte = dateTo;
      }

      const pipeline = [
        ...(Object.keys(matchStage).length > 0 ? [{ $match: matchStage }] : []),
        {
          $group: {
            _id: null,
            totalOrders: { $sum: 1 },
            totalRevenue: { $sum: "$totalAmount" },
            averageOrderValue: { $avg: "$totalAmount" },
            ordersByStatus: {
              $push: "$status"
            },
            ordersByPaymentStatus: {
              $push: "$paymentStatus"
            },
            ordersByShippingStatus: {
              $push: "$shippingStatus"
            },
          }
        }
      ];

      const [result] = await Order.aggregate(pipeline);

      if (!result) {
        return {
          totalOrders: 0,
          totalRevenue: 0,
          averageOrderValue: 0,
          ordersByStatus: {} as Record<OrderStatus, number>,
          ordersByPaymentStatus: {} as Record<PaymentStatus, number>,
          ordersByShippingStatus: {} as Record<ShippingStatus, number>,
          recentOrders: 0,
          pendingOrders: 0,
        };
      }

      // Count orders by status
      const ordersByStatus = result.ordersByStatus.reduce((acc: any, status: string) => {
        acc[status] = (acc[status] || 0) + 1;
        return acc;
      }, {});

      const ordersByPaymentStatus = result.ordersByPaymentStatus.reduce((acc: any, status: string) => {
        acc[status] = (acc[status] || 0) + 1;
        return acc;
      }, {});

      const ordersByShippingStatus = result.ordersByShippingStatus.reduce((acc: any, status: string) => {
        acc[status] = (acc[status] || 0) + 1;
        return acc;
      }, {});

      // Get recent orders (last 7 days)
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      const recentOrders = await Order.countDocuments({
        placedAt: { $gte: sevenDaysAgo }
      });

      // Get pending orders
      const pendingOrders = await Order.countDocuments({
        status: "pending"
      });

      return {
        totalOrders: result.totalOrders,
        totalRevenue: result.totalRevenue,
        averageOrderValue: result.averageOrderValue,
        ordersByStatus,
        ordersByPaymentStatus,
        ordersByShippingStatus,
        recentOrders,
        pendingOrders,
      };
    } catch (error) {
      throw new Error(`Failed to get order statistics: ${error}`);
    }
  }

  /**
   * Update order status
   */
  async updateOrderStatus(
    orderId: string, 
    status: OrderStatus,
    paymentStatus?: PaymentStatus,
    shippingStatus?: ShippingStatus
  ): Promise<OrderDocument | null> {
    try {
      const updateData: any = { status };
      
      if (paymentStatus) updateData.paymentStatus = paymentStatus;
      if (shippingStatus) updateData.shippingStatus = shippingStatus;

      // Add timestamps for specific status changes
      if (shippingStatus === "shipped") {
        updateData["shipping.shippedAt"] = new Date();
      }
      
      if (shippingStatus === "delivered") {
        updateData["shipping.deliveredAt"] = new Date();
      }

      if (paymentStatus === "paid") {
        updateData["payment.paidAt"] = new Date();
      }

      return await Order.findByIdAndUpdate(
        orderId,
        { $set: updateData },
        { new: true, runValidators: true }
      ).lean();
    } catch (error) {
      throw new Error(`Failed to update order status: ${error}`);
    }
  }
}
