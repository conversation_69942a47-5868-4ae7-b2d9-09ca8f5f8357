import { Document } from "mongoose";

/**
 * Category type matching the frontend Category type
 */
export type ICategory = {
  name: string;
  description: string;
  slug: string;
  icon?: string;
  color?: string;
  isActive: boolean;
  productCount: number;
  parentId?: string;
  sortOrder: number;
};

/**
 * Category document interface for MongoDB (keeping as interface for Document extension)
 */
export interface CategoryDocument extends ICategory, Document {
  _id: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * DTO for creating a new category
 */
export type CreateCategoryDto = {
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  isActive?: boolean;
  parentId?: string;
  sortOrder?: number;
};

/**
 * DTO for updating a category
 */
export type UpdateCategoryDto = {
  name?: string;
  description?: string;
  slug?: string;
  icon?: string;
  color?: string;
  isActive?: boolean;
  parentId?: string;
  sortOrder?: number;
};

/**
 * Category filters for querying
 */
export type CategoryFilters = {
  isActive?: boolean;
  parentId?: string;
  search?: string;
};
