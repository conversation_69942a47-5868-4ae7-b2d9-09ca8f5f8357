import { PageHeaderWrapper } from "@/components/common/PageHeaderWrapper";
import { OrderDetailsWrapper } from "@/components/pages/orders/details/OrderDetailsWrapper";

type Props = {
  params: Promise<{
    id: string;
  }>;
};

export default async function AdminOrderDetails({ params }: Props) {
  const { id } = await params;

  return (
    <>
      <PageHeaderWrapper
        title={`Order #${id}`}
        description="View and manage order details"
      />

      <div className="container mx-auto mt-6">
        <OrderDetailsWrapper orderId={id} />
      </div>
    </>
  );
}
