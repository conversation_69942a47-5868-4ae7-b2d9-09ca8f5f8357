"use client";

import React, { useEffect, useState } from "react";

import {
  Archive,
  Check,
  Copy,
  Edit,
  Eye,
  Filter,
  Grid3X3,
  List,
  MoreHorizontal,
  Package,
  Palette,
  Pencil,
  Plus,
  Save,
  Search,
  SortAsc,
  SortDesc,
  Star,
  Trash2,
  TrendingUp,
  X,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { useColors } from "@/hooks/useColors";
import { Color, CreateColorDto } from "@/lib/api/colors";

interface ColorManagerEnhancedProps {
  initialColors?: Color[];
  onColorsChange?: (colors: Color[]) => void;
}

/**
 * Enhanced component for managing product colors with real API integration
 */
export const ColorManagerEnhanced = ({
  initialColors = [],
  onColorsChange,
}: ColorManagerEnhancedProps) => {
  const router = useRouter();

  // API hooks
  const {
    colors: apiColors,
    loading,
    error,
    createColor,
    updateColor,
    deleteColor,
    refreshColors,
  } = useColors();

  // Local state
  const [colors, setColors] = useState<Color[]>(initialColors);
  const [newColor, setNewColor] = useState<Partial<CreateColorDto>>({
    name: "",
    description: "",
    hexValue: "#000000",
    colorFamily: "gray",
    isActive: true,
  });
  const [editingColorId, setEditingColorId] = useState<string | null>(null);
  const [editForm, setEditForm] = useState<Partial<Color>>({});
  const [showAddForm, setShowAddForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [showInactive, setShowInactive] = useState(false);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [sortBy, setSortBy] = useState<"name" | "created" | "products">("name");
  const [filterFamily, setFilterFamily] = useState<string>("all");

  // Helper function to convert hex to RGB
  const hexToRgb = (hex: string) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result
      ? {
          r: parseInt(result[1], 16),
          g: parseInt(result[2], 16),
          b: parseInt(result[3], 16),
        }
      : { r: 0, g: 0, b: 0 };
  };

  // Helper function to convert RGB to HSL
  const rgbToHsl = (r: number, g: number, b: number) => {
    r /= 255;
    g /= 255;
    b /= 255;

    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h = 0,
      s = 0,
      l = (max + min) / 2;

    if (max === min) {
      h = s = 0; // achromatic
    } else {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

      switch (max) {
        case r:
          h = (g - b) / d + (g < b ? 6 : 0);
          break;
        case g:
          h = (b - r) / d + 2;
          break;
        case b:
          h = (r - g) / d + 4;
          break;
      }
      h /= 6;
    }

    return {
      h: Math.round(h * 360),
      s: Math.round(s * 100),
      l: Math.round(l * 100),
    };
  };

  // Helper function to determine color family from hex
  const getColorFamily = (hex: string): string => {
    const rgb = hexToRgb(hex);
    const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);

    // Determine color family based on HSL values
    if (hsl.s < 10) {
      if (hsl.l < 20) return "black";
      if (hsl.l > 90) return "white";
      return "gray";
    }

    const hue = hsl.h;
    if (hue >= 0 && hue < 15) return "red";
    if (hue >= 15 && hue < 45) return "orange";
    if (hue >= 45 && hue < 75) return "yellow";
    if (hue >= 75 && hue < 150) return "green";
    if (hue >= 150 && hue < 250) return "blue";
    if (hue >= 250 && hue < 290) return "purple";
    if (hue >= 290 && hue < 330) return "pink";
    if (hue >= 330 && hue < 360) return "red";
    return "gray";
  };

  // Update local colors when API data changes
  useEffect(() => {
    setColors(apiColors);
    if (onColorsChange) {
      onColorsChange(apiColors);
    }
  }, [apiColors, onColorsChange]);

  // Filter and sort colors
  const filteredColors = colors
    .filter((color) => {
      const matchesSearch = color.name
        .toLowerCase()
        .includes(searchTerm.toLowerCase());
      const matchesStatus = showInactive || color.isActive;
      const matchesFamily =
        filterFamily === "all" || color.colorFamily === filterFamily;
      return matchesSearch && matchesStatus && matchesFamily;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case "name":
          return a.name.localeCompare(b.name);
        case "created":
          return (
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          );
        case "products":
          return (b.productCount || 0) - (a.productCount || 0);
        default:
          return 0;
      }
    });

  // Handle create color
  const handleCreateColor = async () => {
    if (!newColor.name?.trim()) {
      toast.error("Color name is required");
      return;
    }

    if (!newColor.hexValue || !/^#[0-9A-F]{6}$/i.test(newColor.hexValue)) {
      toast.error("Valid hex color code is required (e.g., #FF0000)");
      return;
    }

    try {
      // Generate RGB and HSL values from hex
      const rgb = hexToRgb(newColor.hexValue);
      const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);

      // Auto-detect color family if not set
      const colorFamily =
        newColor.colorFamily || getColorFamily(newColor.hexValue);

      const colorData: CreateColorDto = {
        name: newColor.name,
        description: newColor.description || "",
        hexValue: newColor.hexValue,
        rgbValue: rgb,
        hslValue: hsl,
        colorFamily: colorFamily as any,
        isActive: newColor.isActive ?? true,
      };

      await createColor(colorData);
      setNewColor({
        name: "",
        description: "",
        hexValue: "#000000",
        colorFamily: "gray",
        isActive: true,
      });
      setShowAddForm(false);
      refreshColors();
    } catch (error) {
      console.error("Error creating color:", error);
    }
  };

  // Handle update color
  const handleUpdateColor = async () => {
    if (!editingColorId || !editForm.name?.trim()) {
      toast.error("Color name is required");
      return;
    }

    try {
      await updateColor(editingColorId, editForm);
      setEditingColorId(null);
      setEditForm({});
      refreshColors();
    } catch (error) {
      console.error("Error updating color:", error);
    }
  };

  // Handle delete color
  const handleDeleteColor = async (colorId: string) => {
    if (!confirm("Are you sure you want to delete this color?")) {
      return;
    }

    try {
      await deleteColor(colorId);
      refreshColors();
    } catch (error) {
      console.error("Error deleting color:", error);
    }
  };

  // Statistics
  const stats = {
    total: colors.length,
    active: colors.filter((c) => c.isActive).length,
    primary: colors.filter(
      (c) =>
        c.colorFamily === "red" ||
        c.colorFamily === "blue" ||
        c.colorFamily === "green"
    ).length,
    neutral: colors.filter(
      (c) =>
        c.colorFamily === "gray" ||
        c.colorFamily === "black" ||
        c.colorFamily === "white"
    ).length,
  };

  if (error) {
    return (
      <div className="rounded-lg border border-red-200 bg-red-50 p-4">
        <p className="text-red-600">Error loading colors: {error}</p>
        <Button onClick={refreshColors} className="mt-2">
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Statistics */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-pink-100 p-2">
                <Palette className="h-5 w-5 text-pink-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Colors</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-green-100 p-2">
                <TrendingUp className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Active Colors</p>
                <p className="text-2xl font-bold">{stats.active}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-blue-100 p-2">
                <Star className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Primary</p>
                <p className="text-2xl font-bold">{stats.primary}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-gray-100 p-2">
                <Package className="h-5 w-5 text-gray-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Neutral</p>
                <p className="text-2xl font-bold">{stats.neutral}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Controls */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            {/* Search and Filters */}
            <div className="flex flex-1 gap-4">
              <div className="relative max-w-md flex-1">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Search colors..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              <Select value={filterFamily} onValueChange={setFilterFamily}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Families</SelectItem>
                  <SelectItem value="primary">Primary</SelectItem>
                  <SelectItem value="secondary">Secondary</SelectItem>
                  <SelectItem value="neutral">Neutral</SelectItem>
                  <SelectItem value="warm">Warm</SelectItem>
                  <SelectItem value="cool">Cool</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={sortBy}
                onValueChange={(value: any) => setSortBy(value)}
              >
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="created">Created</SelectItem>
                  <SelectItem value="products">Products</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Actions */}
            <div className="flex gap-2">
              <div className="flex items-center gap-2">
                <Switch
                  checked={showInactive}
                  onCheckedChange={setShowInactive}
                />
                <Label className="text-sm">Show inactive</Label>
              </div>

              <div className="flex gap-1">
                <Button
                  variant={viewMode === "grid" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === "list" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setViewMode("list")}
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>

              <Button onClick={() => setShowAddForm(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add Color
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Add Color Form */}
      {showAddForm && (
        <Card>
          <CardHeader>
            <CardTitle>Add New Color</CardTitle>
            <CardDescription>
              Create a new color for your product catalog
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <Label htmlFor="color-name">Name *</Label>
                <Input
                  id="color-name"
                  value={newColor.name || ""}
                  onChange={(e) =>
                    setNewColor({ ...newColor, name: e.target.value })
                  }
                  placeholder="e.g., Ocean Blue"
                />
              </div>
              <div>
                <Label htmlFor="color-hex">Color Selection *</Label>
                <div className="space-y-3">
                  {/* Color Picker */}
                  <div className="flex gap-2">
                    <input
                      type="color"
                      id="color-picker"
                      value={newColor.hexValue || "#000000"}
                      onChange={(e) =>
                        setNewColor({
                          ...newColor,
                          hexValue: e.target.value,
                          colorFamily: getColorFamily(e.target.value) as any,
                        })
                      }
                      className="h-10 w-16 cursor-pointer rounded border"
                    />
                    <Input
                      id="color-hex"
                      value={newColor.hexValue || ""}
                      onChange={(e) =>
                        setNewColor({
                          ...newColor,
                          hexValue: e.target.value,
                          colorFamily: getColorFamily(e.target.value) as any,
                        })
                      }
                      placeholder="#FF0000"
                      className="flex-1"
                    />
                  </div>

                  {/* Preset Colors */}
                  <div>
                    <Label className="text-xs text-gray-500">
                      Quick Colors
                    </Label>
                    <div className="mt-1 flex gap-2">
                      {[
                        "#FF0000",
                        "#00FF00",
                        "#0000FF",
                        "#FFFF00",
                        "#FF00FF",
                        "#00FFFF",
                        "#FFA500",
                        "#800080",
                        "#FFC0CB",
                        "#A52A2A",
                        "#808080",
                        "#000000",
                      ].map((color) => (
                        <button
                          key={color}
                          type="button"
                          className="h-8 w-8 rounded border-2 border-gray-300 transition-colors hover:border-gray-500"
                          style={{ backgroundColor: color }}
                          onClick={() =>
                            setNewColor({
                              ...newColor,
                              hexValue: color,
                              colorFamily: getColorFamily(color) as any,
                            })
                          }
                          title={color}
                        />
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <Label htmlFor="color-family">Family</Label>
                <Select
                  value={newColor.colorFamily}
                  onValueChange={(value: any) =>
                    setNewColor({ ...newColor, colorFamily: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="red">Red</SelectItem>
                    <SelectItem value="orange">Orange</SelectItem>
                    <SelectItem value="yellow">Yellow</SelectItem>
                    <SelectItem value="green">Green</SelectItem>
                    <SelectItem value="blue">Blue</SelectItem>
                    <SelectItem value="purple">Purple</SelectItem>
                    <SelectItem value="pink">Pink</SelectItem>
                    <SelectItem value="brown">Brown</SelectItem>
                    <SelectItem value="gray">Gray</SelectItem>
                    <SelectItem value="black">Black</SelectItem>
                    <SelectItem value="white">White</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="color-description">Description</Label>
              <Textarea
                id="color-description"
                value={newColor.description || ""}
                onChange={(e) =>
                  setNewColor({ ...newColor, description: e.target.value })
                }
                placeholder="Describe the color and its uses..."
                rows={3}
              />
            </div>

            <div className="flex items-center gap-2">
              <Switch
                checked={newColor.isActive}
                onCheckedChange={(checked) =>
                  setNewColor({ ...newColor, isActive: checked })
                }
              />
              <Label>Active</Label>
            </div>

            <div className="flex gap-2 pt-2">
              <Button onClick={handleCreateColor}>
                <Check className="mr-2 h-4 w-4" />
                Add Color
              </Button>
              <Button variant="outline" onClick={() => setShowAddForm(false)}>
                <X className="mr-2 h-4 w-4" />
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Colors Display */}
      {loading ? (
        <div className="space-y-2">
          {[...Array(5)].map((_, i) => (
            <Skeleton key={i} className="h-20 w-full" />
          ))}
        </div>
      ) : filteredColors.length === 0 ? (
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-muted-foreground">No colors found</p>
            {searchTerm && (
              <Button
                variant="link"
                onClick={() => setSearchTerm("")}
                className="mt-2"
              >
                Clear search
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div
          className={
            viewMode === "grid"
              ? "grid gap-4 md:grid-cols-2 lg:grid-cols-3"
              : "space-y-3"
          }
        >
          {filteredColors.map((color) => (
            <Card
              key={color._id}
              className={`transition-shadow hover:shadow-md ${
                !color.isActive ? "opacity-60" : ""
              }`}
            >
              <CardContent className="p-4">
                {editingColorId === color._id ? (
                  // Edit form
                  <div className="space-y-3">
                    <Input
                      value={editForm.name || ""}
                      onChange={(e) =>
                        setEditForm({ ...editForm, name: e.target.value })
                      }
                      placeholder="Color name"
                    />
                    <div className="space-y-2">
                      <div className="flex gap-2">
                        <input
                          type="color"
                          value={editForm.hexValue || "#000000"}
                          onChange={(e) =>
                            setEditForm({
                              ...editForm,
                              hexValue: e.target.value,
                            })
                          }
                          className="h-10 w-16 cursor-pointer rounded border"
                        />
                        <Input
                          value={editForm.hexValue || ""}
                          onChange={(e) =>
                            setEditForm({
                              ...editForm,
                              hexValue: e.target.value,
                            })
                          }
                          placeholder="#FF0000"
                          className="flex-1"
                        />
                      </div>
                      <div className="flex gap-1">
                        {[
                          "#FF0000",
                          "#00FF00",
                          "#0000FF",
                          "#FFFF00",
                          "#FF00FF",
                          "#00FFFF",
                        ].map((color) => (
                          <button
                            key={color}
                            type="button"
                            className="h-6 w-6 rounded border border-gray-300 transition-colors hover:border-gray-500"
                            style={{ backgroundColor: color }}
                            onClick={() =>
                              setEditForm({ ...editForm, hexValue: color })
                            }
                            title={color}
                          />
                        ))}
                      </div>
                    </div>
                    <Textarea
                      value={editForm.description || ""}
                      onChange={(e) =>
                        setEditForm({
                          ...editForm,
                          description: e.target.value,
                        })
                      }
                      placeholder="Description"
                      rows={2}
                    />
                    <div className="flex gap-2">
                      <Button size="sm" onClick={handleUpdateColor}>
                        <Save className="mr-2 h-4 w-4" />
                        Save
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          setEditingColorId(null);
                          setEditForm({});
                        }}
                      >
                        <X className="mr-2 h-4 w-4" />
                        Cancel
                      </Button>
                    </div>
                  </div>
                ) : (
                  // Display mode
                  <div>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <div
                          className="h-12 w-12 rounded-lg border-2 border-gray-200 shadow-sm"
                          style={{ backgroundColor: color.hexValue }}
                        />
                        <div>
                          <h3 className="font-semibold text-gray-900">
                            {color.name}
                          </h3>
                          <p className="text-sm text-gray-600">
                            {color.hexValue}
                          </p>
                          <div className="mt-1 flex items-center gap-2">
                            <Badge variant="outline" className="text-xs">
                              {color.colorFamily}
                            </Badge>
                            {!color.isActive && (
                              <Badge variant="secondary" className="text-xs">
                                Inactive
                              </Badge>
                            )}
                            <span className="text-xs text-gray-500">
                              {color.productCount || 0} products
                            </span>
                          </div>
                        </div>
                      </div>

                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => {
                              setEditingColorId(color._id);
                              setEditForm(color);
                            }}
                          >
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Eye className="mr-2 h-4 w-4" />
                            View Products
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Copy className="mr-2 h-4 w-4" />
                            Duplicate
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => handleDeleteColor(color._id)}
                            className="text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};
