"use client";

import { useState } from "react";

import {
  Archive,
  Award,
  Building2,
  Copy,
  Edit,
  ExternalLink,
  Eye,
  Filter,
  Globe,
  Grid3X3,
  Image,
  List,
  Mail,
  MoreHorizontal,
  Package,
  Pencil,
  Plus,
  Save,
  Search,
  SortAsc,
  SortDesc,
  Star,
  Tag,
  Trash2,
  TrendingUp,
  X,
} from "lucide-react";
import { toast } from "sonner";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";

export type Brand = {
  id: string;
  name: string;
  description: string;
  slug: string;
  logo?: string;
  website?: string;
  email?: string;
  country?: string;
  foundedYear?: number;
  isActive: boolean;
  isPremium: boolean;
  productCount: number;
  rating: number;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
};

// Mock brands with enhanced data
const mockBrands: Brand[] = [
  {
    id: "brand-1",
    name: "Apple",
    description:
      "Innovative technology company known for premium consumer electronics",
    slug: "apple",
    logo: "🍎",
    website: "https://apple.com",
    email: "<EMAIL>",
    country: "United States",
    foundedYear: 1976,
    isActive: true,
    isPremium: true,
    productCount: 89,
    rating: 4.8,
    sortOrder: 1,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-15T10:30:00Z",
  },
  {
    id: "brand-2",
    name: "Samsung",
    description:
      "Global leader in technology, semiconductors, and consumer electronics",
    slug: "samsung",
    logo: "📱",
    website: "https://samsung.com",
    email: "<EMAIL>",
    country: "South Korea",
    foundedYear: 1938,
    isActive: true,
    isPremium: true,
    productCount: 156,
    rating: 4.6,
    sortOrder: 2,
    createdAt: "2024-01-02T00:00:00Z",
    updatedAt: "2024-01-14T16:45:00Z",
  },
  {
    id: "brand-3",
    name: "Nike",
    description: "World's leading supplier of athletic shoes and apparel",
    slug: "nike",
    logo: "✅",
    website: "https://nike.com",
    email: "<EMAIL>",
    country: "United States",
    foundedYear: 1964,
    isActive: true,
    isPremium: true,
    productCount: 234,
    rating: 4.7,
    sortOrder: 3,
    createdAt: "2024-01-03T00:00:00Z",
    updatedAt: "2024-01-13T09:20:00Z",
  },
  {
    id: "brand-4",
    name: "IKEA",
    description:
      "Swedish furniture retailer known for affordable, functional home furnishing",
    slug: "ikea",
    logo: "🏠",
    website: "https://ikea.com",
    email: "<EMAIL>",
    country: "Sweden",
    foundedYear: 1943,
    isActive: true,
    isPremium: false,
    productCount: 445,
    rating: 4.3,
    sortOrder: 4,
    createdAt: "2024-01-04T00:00:00Z",
    updatedAt: "2024-01-12T14:15:00Z",
  },
  {
    id: "brand-5",
    name: "Adidas",
    description:
      "German multinational corporation that designs and manufactures sports equipment",
    slug: "adidas",
    logo: "👟",
    website: "https://adidas.com",
    email: "<EMAIL>",
    country: "Germany",
    foundedYear: 1949,
    isActive: true,
    isPremium: true,
    productCount: 178,
    rating: 4.5,
    sortOrder: 5,
    createdAt: "2024-01-05T00:00:00Z",
    updatedAt: "2024-01-11T11:30:00Z",
  },
  {
    id: "brand-6",
    name: "Sony",
    description:
      "Japanese multinational conglomerate focused on electronics and entertainment",
    slug: "sony",
    logo: "🎮",
    website: "https://sony.com",
    email: "<EMAIL>",
    country: "Japan",
    foundedYear: 1946,
    isActive: false,
    isPremium: true,
    productCount: 67,
    rating: 4.4,
    sortOrder: 6,
    createdAt: "2024-01-06T00:00:00Z",
    updatedAt: "2024-01-10T08:45:00Z",
  },
];

type BrandManagerProps = {
  initialBrands?: Brand[];
  onBrandsChange?: (brands: Brand[]) => void;
};

/**
 * Enhanced component for managing product brands with professional UI
 */
export const BrandManagerEnhanced = ({
  initialBrands = mockBrands,
  onBrandsChange,
}: BrandManagerProps) => {
  const [brands, setBrands] = useState<Brand[]>(initialBrands);
  const [newBrand, setNewBrand] = useState<Partial<Brand>>({
    name: "",
    description: "",
    logo: "",
    website: "",
    email: "",
    country: "",
    foundedYear: undefined,
    isActive: true,
    isPremium: false,
  });
  const [editingBrandId, setEditingBrandId] = useState<string | null>(null);
  const [editForm, setEditForm] = useState<Partial<Brand>>({});
  const [searchQuery, setSearchQuery] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [filterType, setFilterType] = useState("all");
  const [sortBy, setSortBy] = useState("name");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [showAddForm, setShowAddForm] = useState(false);

  // Generate a slug from the brand name
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/^-|-$/g, "");
  };

  // Filter and sort brands
  const filteredAndSortedBrands = brands
    .filter((brand) => {
      const matchesSearch =
        brand.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        brand.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        brand.country?.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesStatus =
        filterStatus === "all" ||
        (filterStatus === "active" && brand.isActive) ||
        (filterStatus === "inactive" && !brand.isActive);

      const matchesType =
        filterType === "all" ||
        (filterType === "premium" && brand.isPremium) ||
        (filterType === "standard" && !brand.isPremium);

      return matchesSearch && matchesStatus && matchesType;
    })
    .sort((a, b) => {
      let comparison = 0;
      switch (sortBy) {
        case "name":
          comparison = a.name.localeCompare(b.name);
          break;
        case "products":
          comparison = a.productCount - b.productCount;
          break;
        case "rating":
          comparison = a.rating - b.rating;
          break;
        case "founded":
          comparison = (a.foundedYear || 0) - (b.foundedYear || 0);
          break;
        case "created":
          comparison =
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
          break;
        default:
          comparison = a.sortOrder - b.sortOrder;
      }
      return sortOrder === "asc" ? comparison : -comparison;
    });

  // Add a new brand
  const handleAddBrand = () => {
    if (!newBrand.name) {
      toast.error("Brand name is required");
      return;
    }

    const slug = generateSlug(newBrand.name);

    // Check if slug already exists
    if (brands.some((brand) => brand.slug === slug)) {
      toast.error("A brand with this name already exists");
      return;
    }

    const newBrandWithId: Brand = {
      id: `brand-${Date.now()}`,
      name: newBrand.name,
      description: newBrand.description || "",
      slug,
      logo: newBrand.logo || "🏢",
      website: newBrand.website || "",
      email: newBrand.email || "",
      country: newBrand.country || "",
      foundedYear: newBrand.foundedYear,
      isActive: newBrand.isActive ?? true,
      isPremium: newBrand.isPremium ?? false,
      productCount: 0,
      rating: 0,
      sortOrder: brands.length + 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    const updatedBrands = [...brands, newBrandWithId];
    setBrands(updatedBrands);
    setNewBrand({
      name: "",
      description: "",
      logo: "",
      website: "",
      email: "",
      country: "",
      foundedYear: undefined,
      isActive: true,
      isPremium: false,
    });
    setShowAddForm(false);

    // Notify parent component
    if (onBrandsChange) {
      onBrandsChange(updatedBrands);
    }

    toast.success("Brand added successfully");
  };

  // Start editing a brand
  const handleEditStart = (brand: Brand) => {
    setEditingBrandId(brand.id);
    setEditForm({ ...brand });
  };

  // Cancel editing
  const handleEditCancel = () => {
    setEditingBrandId(null);
    setEditForm({});
  };

  // Save edited brand
  const handleEditSave = () => {
    if (!editForm.name) {
      toast.error("Brand name is required");
      return;
    }

    const updatedBrands = brands.map((brand) =>
      brand.id === editingBrandId
        ? {
            ...brand,
            name: editForm.name || brand.name,
            description: editForm.description || brand.description,
            logo: editForm.logo || brand.logo,
            website: editForm.website || brand.website,
            email: editForm.email || brand.email,
            country: editForm.country || brand.country,
            foundedYear: editForm.foundedYear || brand.foundedYear,
            isActive: editForm.isActive ?? brand.isActive,
            isPremium: editForm.isPremium ?? brand.isPremium,
            // Only update slug if name changed
            slug:
              brand.name !== editForm.name && editForm.name
                ? generateSlug(editForm.name)
                : brand.slug,
            updatedAt: new Date().toISOString(),
          }
        : brand
    );

    setBrands(updatedBrands);
    setEditingBrandId(null);
    setEditForm({});

    // Notify parent component
    if (onBrandsChange) {
      onBrandsChange(updatedBrands);
    }

    toast.success("Brand updated successfully");
  };

  // Delete a brand
  const handleDeleteBrand = (brandId: string) => {
    const updatedBrands = brands.filter((brand) => brand.id !== brandId);
    setBrands(updatedBrands);

    // Notify parent component
    if (onBrandsChange) {
      onBrandsChange(updatedBrands);
    }

    toast.success("Brand deleted successfully");
  };

  return (
    <div className="space-y-6">
      {/* Header with Statistics */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-green-100 p-2">
                <Tag className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Brands</p>
                <p className="text-2xl font-bold">{brands.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-blue-100 p-2">
                <TrendingUp className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Active Brands</p>
                <p className="text-2xl font-bold">
                  {brands.filter((b) => b.isActive).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-purple-100 p-2">
                <Award className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Premium Brands</p>
                <p className="text-2xl font-bold">
                  {brands.filter((b) => b.isPremium).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-orange-100 p-2">
                <Star className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Avg Rating</p>
                <p className="text-2xl font-bold">
                  {brands.length > 0
                    ? (
                        brands.reduce((sum, b) => sum + b.rating, 0) /
                        brands.length
                      ).toFixed(1)
                    : "0.0"}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Controls */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            {/* Search and Filters */}
            <div className="flex flex-1 gap-4">
              <div className="relative max-w-md flex-1">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Search brands..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>

              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>

              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="premium">Premium</SelectItem>
                  <SelectItem value="standard">Standard</SelectItem>
                </SelectContent>
              </Select>

              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="products">Products</SelectItem>
                  <SelectItem value="rating">Rating</SelectItem>
                  <SelectItem value="founded">Founded</SelectItem>
                  <SelectItem value="created">Created</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* View Controls */}
            <div className="flex items-center gap-2">
              <Button
                variant={sortOrder === "asc" ? "default" : "outline"}
                size="sm"
                onClick={() =>
                  setSortOrder(sortOrder === "asc" ? "desc" : "asc")
                }
              >
                {sortOrder === "asc" ? (
                  <SortAsc className="h-4 w-4" />
                ) : (
                  <SortDesc className="h-4 w-4" />
                )}
              </Button>

              <div className="flex rounded-md border">
                <Button
                  variant={viewMode === "grid" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                  className="rounded-r-none"
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === "list" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("list")}
                  className="rounded-l-none"
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>

              <Button onClick={() => setShowAddForm(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add Brand
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Add Brand Form */}
      {showAddForm && (
        <Card className="border-2 border-green-200 bg-green-50/50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Plus className="h-5 w-5 text-green-600" />
              Add New Brand
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <Label htmlFor="new-brand-name">Brand Name *</Label>
                <Input
                  id="new-brand-name"
                  value={newBrand.name}
                  onChange={(e) =>
                    setNewBrand({ ...newBrand, name: e.target.value })
                  }
                  placeholder="e.g. Apple"
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="new-brand-logo">Logo (Emoji)</Label>
                <Input
                  id="new-brand-logo"
                  value={newBrand.logo}
                  onChange={(e) =>
                    setNewBrand({ ...newBrand, logo: e.target.value })
                  }
                  placeholder="🍎"
                  className="mt-1"
                />
              </div>

              <div className="md:col-span-2">
                <Label htmlFor="new-brand-description">Description</Label>
                <Textarea
                  id="new-brand-description"
                  value={newBrand.description}
                  onChange={(e) =>
                    setNewBrand({ ...newBrand, description: e.target.value })
                  }
                  placeholder="Describe the brand, its values, and what makes it unique..."
                  className="mt-1"
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="new-brand-website">Website</Label>
                <Input
                  id="new-brand-website"
                  type="url"
                  value={newBrand.website}
                  onChange={(e) =>
                    setNewBrand({ ...newBrand, website: e.target.value })
                  }
                  placeholder="https://example.com"
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="new-brand-email">Contact Email</Label>
                <Input
                  id="new-brand-email"
                  type="email"
                  value={newBrand.email}
                  onChange={(e) =>
                    setNewBrand({ ...newBrand, email: e.target.value })
                  }
                  placeholder="<EMAIL>"
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="new-brand-country">Country</Label>
                <Input
                  id="new-brand-country"
                  value={newBrand.country}
                  onChange={(e) =>
                    setNewBrand({ ...newBrand, country: e.target.value })
                  }
                  placeholder="United States"
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="new-brand-founded">Founded Year</Label>
                <Input
                  id="new-brand-founded"
                  type="number"
                  min="1800"
                  max={new Date().getFullYear()}
                  value={newBrand.foundedYear || ""}
                  onChange={(e) =>
                    setNewBrand({
                      ...newBrand,
                      foundedYear: parseInt(e.target.value) || undefined,
                    })
                  }
                  placeholder="1976"
                  className="mt-1"
                />
              </div>

              <div className="flex items-center space-x-2 pt-6">
                <Switch
                  id="new-brand-active"
                  checked={newBrand.isActive}
                  onCheckedChange={(checked) =>
                    setNewBrand({ ...newBrand, isActive: checked })
                  }
                />
                <Label htmlFor="new-brand-active">Active brand</Label>
              </div>

              <div className="flex items-center space-x-2 pt-6">
                <Switch
                  id="new-brand-premium"
                  checked={newBrand.isPremium}
                  onCheckedChange={(checked) =>
                    setNewBrand({ ...newBrand, isPremium: checked })
                  }
                />
                <Label htmlFor="new-brand-premium">Premium brand</Label>
              </div>
            </div>

            <div className="mt-6 flex gap-2">
              <Button onClick={handleAddBrand}>
                <Save className="mr-2 h-4 w-4" />
                Add Brand
              </Button>
              <Button variant="outline" onClick={() => setShowAddForm(false)}>
                <X className="mr-2 h-4 w-4" />
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Brands Display */}
      {viewMode === "grid" ? (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {filteredAndSortedBrands.map((brand) => (
            <Card key={brand.id} className="transition-shadow hover:shadow-md">
              <CardContent className="p-4">
                {editingBrandId === brand.id ? (
                  // Edit form
                  <div className="space-y-3">
                    <Input
                      value={editForm.name || ""}
                      onChange={(e) =>
                        setEditForm({ ...editForm, name: e.target.value })
                      }
                      placeholder="Brand name"
                    />
                    <Textarea
                      value={editForm.description || ""}
                      onChange={(e) =>
                        setEditForm({
                          ...editForm,
                          description: e.target.value,
                        })
                      }
                      placeholder="Description"
                      rows={2}
                    />
                    <div className="flex gap-2">
                      <Button size="sm" onClick={handleEditSave}>
                        <Save className="mr-1 h-3 w-3" />
                        Save
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={handleEditCancel}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ) : (
                  // Display mode
                  <div className="space-y-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <span className="text-3xl">{brand.logo}</span>
                        <div>
                          <h3 className="font-semibold">{brand.name}</h3>
                          <div className="flex gap-1">
                            <Badge
                              variant={brand.isActive ? "default" : "secondary"}
                              className="text-xs"
                            >
                              {brand.isActive ? "Active" : "Inactive"}
                            </Badge>
                            {brand.isPremium && (
                              <Badge
                                variant="outline"
                                className="border-purple-200 text-xs text-purple-600"
                              >
                                Premium
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>

                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => handleEditStart(brand)}
                          >
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Eye className="mr-2 h-4 w-4" />
                            View Products
                          </DropdownMenuItem>
                          {brand.website && (
                            <DropdownMenuItem asChild>
                              <a
                                href={brand.website}
                                target="_blank"
                                rel="noopener noreferrer"
                              >
                                <ExternalLink className="mr-2 h-4 w-4" />
                                Visit Website
                              </a>
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuItem>
                            <Copy className="mr-2 h-4 w-4" />
                            Duplicate
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem>
                            <Archive className="mr-2 h-4 w-4" />
                            Archive
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleDeleteBrand(brand.id)}
                            className="text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>

                    <p className="line-clamp-2 text-sm text-gray-600">
                      {brand.description || "No description provided"}
                    </p>

                    <div className="space-y-2 text-xs text-gray-500">
                      <div className="flex items-center justify-between">
                        <span>{brand.productCount} products</span>
                        <div className="flex items-center gap-1">
                          <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                          <span>{brand.rating.toFixed(1)}</span>
                        </div>
                      </div>

                      {brand.country && (
                        <div className="flex items-center gap-1">
                          <Globe className="h-3 w-3" />
                          <span>{brand.country}</span>
                          {brand.foundedYear && (
                            <span>• Est. {brand.foundedYear}</span>
                          )}
                        </div>
                      )}

                      {brand.website && (
                        <div className="flex items-center gap-1">
                          <ExternalLink className="h-3 w-3" />
                          <a
                            href={brand.website}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="truncate text-blue-600 hover:underline"
                          >
                            {brand.website.replace(/^https?:\/\//, "")}
                          </a>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        // List view - simplified for now
        <Card>
          <CardContent className="p-0">
            <div className="divide-y">
              {filteredAndSortedBrands.map((brand) => (
                <div key={brand.id} className="p-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <span className="text-2xl">{brand.logo}</span>
                      <div>
                        <div className="flex items-center gap-2">
                          <h3 className="font-semibold">{brand.name}</h3>
                          <Badge
                            variant={brand.isActive ? "default" : "secondary"}
                            className="text-xs"
                          >
                            {brand.isActive ? "Active" : "Inactive"}
                          </Badge>
                          {brand.isPremium && (
                            <Badge
                              variant="outline"
                              className="border-purple-200 text-xs text-purple-600"
                            >
                              Premium
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-600">
                          {brand.description || "No description"}
                        </p>
                        <div className="mt-1 flex items-center gap-4 text-xs text-gray-500">
                          <span>{brand.productCount} products</span>
                          <span>Rating: {brand.rating.toFixed(1)}</span>
                          {brand.country && <span>{brand.country}</span>}
                          {brand.foundedYear && (
                            <span>Est. {brand.foundedYear}</span>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleEditStart(brand)}
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDeleteBrand(brand.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {filteredAndSortedBrands.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <Building2 className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-lg font-medium text-gray-900">
              No brands found
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchQuery || filterStatus !== "all" || filterType !== "all"
                ? "Try adjusting your search or filter criteria."
                : "Get started by adding your first brand."}
            </p>
            {!searchQuery && filterStatus === "all" && filterType === "all" && (
              <Button className="mt-4" onClick={() => setShowAddForm(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add Your First Brand
              </Button>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};
