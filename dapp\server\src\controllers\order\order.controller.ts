import { Request, Response } from "express";
import { OrderService } from "../../services/order.service";
import { CreateOrderDto, UpdateOrderDto, OrderFilters } from "../../types/order.types";

/**
 * Controller class for handling order-related HTTP requests
 */
export class OrderController {
  private orderService: OrderService;

  constructor() {
    this.orderService = new OrderService();
  }

  /**
   * GET /api/orders - Get all orders with optional filtering and pagination
   */
  getOrders = async (req: Request, res: Response): Promise<void> => {
    try {
      const {
        page = 1,
        limit = 20,
        sortBy = "placedAt",
        sortOrder = "desc",
        status,
        paymentStatus,
        shippingStatus,
        customerId,
        customerEmail,
        orderNumber,
        dateFrom,
        dateTo,
        minAmount,
        maxAmount,
        search,
      } = req.query;

      const filters: OrderFilters = {};

      if (status) {
        filters.status = Array.isArray(status) ? status as any : status as any;
      }
      if (paymentStatus) {
        filters.paymentStatus = Array.isArray(paymentStatus) ? paymentStatus as any : paymentStatus as any;
      }
      if (shippingStatus) {
        filters.shippingStatus = Array.isArray(shippingStatus) ? shippingStatus as any : shippingStatus as any;
      }
      if (customerId) filters.customerId = customerId as string;
      if (customerEmail) filters.customerEmail = customerEmail as string;
      if (orderNumber) filters.orderNumber = orderNumber as string;
      if (dateFrom) filters.dateFrom = new Date(dateFrom as string);
      if (dateTo) filters.dateTo = new Date(dateTo as string);
      if (minAmount) filters.minAmount = parseFloat(minAmount as string);
      if (maxAmount) filters.maxAmount = parseFloat(maxAmount as string);
      if (search) filters.search = search as string;

      const result = await this.orderService.getOrders(
        filters,
        parseInt(page as string),
        parseInt(limit as string),
        sortBy as string,
        sortOrder as "asc" | "desc"
      );

      res.status(200).json({
        success: true,
        data: result,
      });
    } catch (error) {
      console.error("Error getting orders:", error);
      res.status(500).json({
        success: false,
        message: "Failed to get orders",
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  };

  /**
   * GET /api/orders/:id - Get order by ID
   */
  getOrderById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const order = await this.orderService.getOrderById(id);

      if (!order) {
        res.status(404).json({
          success: false,
          message: "Order not found",
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: order,
      });
    } catch (error) {
      console.error("Error getting order:", error);
      res.status(500).json({
        success: false,
        message: "Failed to get order",
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  };

  /**
   * GET /api/orders/number/:orderNumber - Get order by order number
   */
  getOrderByNumber = async (req: Request, res: Response): Promise<void> => {
    try {
      const { orderNumber } = req.params;
      const order = await this.orderService.getOrderByNumber(orderNumber);

      if (!order) {
        res.status(404).json({
          success: false,
          message: "Order not found",
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: order,
      });
    } catch (error) {
      console.error("Error getting order:", error);
      res.status(500).json({
        success: false,
        message: "Failed to get order",
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  };

  /**
   * POST /api/orders - Create a new order
   */
  createOrder = async (req: Request, res: Response): Promise<void> => {
    try {
      const orderData: CreateOrderDto = req.body;

      // Basic validation
      if (!orderData.customer || !orderData.items || orderData.items.length === 0) {
        res.status(400).json({
          success: false,
          message: "Customer and items are required",
        });
        return;
      }

      if (!orderData.shippingAddress) {
        res.status(400).json({
          success: false,
          message: "Shipping address is required",
        });
        return;
      }

      const order = await this.orderService.createOrder(orderData);

      res.status(201).json({
        success: true,
        data: order,
        message: "Order created successfully",
      });
    } catch (error) {
      console.error("Error creating order:", error);
      res.status(500).json({
        success: false,
        message: "Failed to create order",
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  };

  /**
   * PUT /api/orders/:id - Update an order
   */
  updateOrder = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const updateData: UpdateOrderDto = req.body;

      const order = await this.orderService.updateOrder(id, updateData);

      if (!order) {
        res.status(404).json({
          success: false,
          message: "Order not found",
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: order,
        message: "Order updated successfully",
      });
    } catch (error) {
      console.error("Error updating order:", error);
      res.status(500).json({
        success: false,
        message: "Failed to update order",
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  };

  /**
   * PUT /api/orders/:id/status - Update order status
   */
  updateOrderStatus = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const { status, paymentStatus, shippingStatus } = req.body;

      if (!status) {
        res.status(400).json({
          success: false,
          message: "Status is required",
        });
        return;
      }

      const order = await this.orderService.updateOrderStatus(
        id,
        status,
        paymentStatus,
        shippingStatus
      );

      if (!order) {
        res.status(404).json({
          success: false,
          message: "Order not found",
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: order,
        message: "Order status updated successfully",
      });
    } catch (error) {
      console.error("Error updating order status:", error);
      res.status(500).json({
        success: false,
        message: "Failed to update order status",
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  };

  /**
   * DELETE /api/orders/:id - Delete an order (soft delete)
   */
  deleteOrder = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const success = await this.orderService.deleteOrder(id);

      if (!success) {
        res.status(404).json({
          success: false,
          message: "Order not found or cannot be deleted",
        });
        return;
      }

      res.status(200).json({
        success: true,
        message: "Order cancelled successfully",
      });
    } catch (error) {
      console.error("Error deleting order:", error);
      res.status(500).json({
        success: false,
        message: "Failed to delete order",
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  };

  /**
   * GET /api/orders/stats - Get order statistics
   */
  getOrderStats = async (req: Request, res: Response): Promise<void> => {
    try {
      const { dateFrom, dateTo } = req.query;

      const filters: { dateFrom?: Date; dateTo?: Date } = {};
      if (dateFrom) filters.dateFrom = new Date(dateFrom as string);
      if (dateTo) filters.dateTo = new Date(dateTo as string);

      const stats = await this.orderService.getOrderStats(
        filters.dateFrom,
        filters.dateTo
      );

      res.status(200).json({
        success: true,
        data: stats,
      });
    } catch (error) {
      console.error("Error getting order statistics:", error);
      res.status(500).json({
        success: false,
        message: "Failed to get order statistics",
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  };
}
