"use client";

import { useState } from "react";

import { Pencil, Plus, Save, Trash2, X } from "lucide-react";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Brand } from "@/types/brand";

// Transform backend brand to frontend format
type FrontendBrand = Omit<Brand, "_id"> & { id: string };

type BrandManagerProps = {
  initialBrands?: Brand[];
  onBrandsChange?: (brands: Brand[]) => void;
};

/**
 * Component for managing product brands
 */
export const BrandManager = ({
  initialBrands = [],
  onBrandsChange,
}: BrandManagerProps) => {
  const [brands, setBrands] = useState<Brand[]>(initialBrands);
  const [newBrand, setNewBrand] = useState<Partial<Brand>>({
    name: "",
    website: "",
  });
  const [editingBrandId, setEditingBrandId] = useState<string | null>(null);
  const [editForm, setEditForm] = useState<Partial<Brand>>({});

  // Generate a slug from the brand name
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/^-|-$/g, "");
  };

  // Add a new brand
  const handleAddBrand = () => {
    if (!newBrand.name) {
      toast.error("Brand name is required");
      return;
    }

    const slug = generateSlug(newBrand.name);

    // Check if slug already exists
    if (brands.some((brand) => brand.slug === slug)) {
      toast.error("A brand with this name already exists");
      return;
    }

    const newBrandWithId: Brand = {
      _id: `brand-${Date.now()}`,
      name: newBrand.name || "",
      description: "",
      website: newBrand.website,
      logo: newBrand.logo,
      slug,
      color: "",
      isActive: true,
      productCount: 0,
      sortOrder: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    const updatedBrands = [...brands, newBrandWithId];
    setBrands(updatedBrands);
    setNewBrand({ name: "", website: "" });

    // Notify parent component
    if (onBrandsChange) {
      onBrandsChange(updatedBrands);
    }

    toast.success("Brand added successfully");
  };

  // Start editing a brand
  const handleEditStart = (brand: Brand) => {
    setEditingBrandId(brand._id);
    setEditForm({ ...brand });
  };

  // Cancel editing
  const handleEditCancel = () => {
    setEditingBrandId(null);
    setEditForm({});
  };

  // Save edited brand
  const handleEditSave = () => {
    if (!editForm.name) {
      toast.error("Brand name is required");
      return;
    }

    const updatedBrands = brands.map((brand) =>
      brand._id === editingBrandId
        ? {
            ...brand,
            name: editForm.name || brand.name,
            website: editForm.website,
            // Only update slug if name changed
            slug:
              brand.name !== editForm.name
                ? generateSlug(editForm.name)
                : brand.slug,
          }
        : brand
    );

    setBrands(updatedBrands);
    setEditingBrandId(null);
    setEditForm({});

    // Notify parent component
    if (onBrandsChange) {
      onBrandsChange(updatedBrands);
    }

    toast.success("Brand updated successfully");
  };

  // Delete a brand
  const handleDelete = (brandId: string) => {
    const updatedBrands = brands.filter((brand) => brand._id !== brandId);
    setBrands(updatedBrands);

    // Notify parent component
    if (onBrandsChange) {
      onBrandsChange(updatedBrands);
    }

    toast.success("Brand deleted successfully");
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Manage Brands</CardTitle>
      </CardHeader>
      <CardContent>
        {/* Add new brand form */}
        <div className="mb-6 space-y-4 rounded-md border p-4">
          <h3 className="text-lg font-medium">Add New Brand</h3>
          <div className="grid gap-4 sm:grid-cols-2">
            <div>
              <Label htmlFor="new-brand-name">Brand Name</Label>
              <Input
                id="new-brand-name"
                value={newBrand.name}
                onChange={(e) =>
                  setNewBrand({ ...newBrand, name: e.target.value })
                }
                placeholder="e.g. Apple"
              />
            </div>
            <div>
              <Label htmlFor="new-brand-website">Website (optional)</Label>
              <Input
                id="new-brand-website"
                value={newBrand.website}
                onChange={(e) =>
                  setNewBrand({ ...newBrand, website: e.target.value })
                }
                placeholder="e.g. https://apple.com"
              />
            </div>
          </div>
          <Button onClick={handleAddBrand} className="mt-2">
            <Plus className="mr-2 h-4 w-4" />
            Add Brand
          </Button>
        </div>

        {/* Brands list */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Existing Brands</h3>
          {brands.length === 0 ? (
            <p className="text-sm text-muted-foreground">
              No brands yet. Add your first brand above.
            </p>
          ) : (
            <div className="space-y-2">
              {brands.map((brand) => (
                <div
                  key={brand._id}
                  className="flex items-center justify-between rounded-md border p-3"
                >
                  {editingBrandId === brand._id ? (
                    // Edit mode
                    <div className="flex w-full flex-col space-y-2">
                      <div className="grid gap-2 sm:grid-cols-2">
                        <Input
                          value={editForm.name || ""}
                          onChange={(e) =>
                            setEditForm({ ...editForm, name: e.target.value })
                          }
                          placeholder="Brand name"
                        />
                        <Input
                          value={editForm.website || ""}
                          onChange={(e) =>
                            setEditForm({
                              ...editForm,
                              website: e.target.value,
                            })
                          }
                          placeholder="Website (optional)"
                        />
                      </div>
                      <div className="flex space-x-2">
                        <Button size="sm" onClick={handleEditSave}>
                          <Save className="mr-2 h-4 w-4" />
                          Save
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={handleEditCancel}
                        >
                          <X className="mr-2 h-4 w-4" />
                          Cancel
                        </Button>
                      </div>
                    </div>
                  ) : (
                    // View mode
                    <>
                      <div>
                        <h4 className="font-medium">{brand.name}</h4>
                        {brand.website && (
                          <p className="text-sm text-muted-foreground">
                            <a
                              href={brand.website}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-500 hover:underline"
                            >
                              {brand.website}
                            </a>
                          </p>
                        )}
                      </div>
                      <div className="flex space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleEditStart(brand)}
                        >
                          <Pencil className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-destructive hover:bg-destructive hover:text-destructive-foreground"
                          onClick={() => handleDelete(brand._id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
