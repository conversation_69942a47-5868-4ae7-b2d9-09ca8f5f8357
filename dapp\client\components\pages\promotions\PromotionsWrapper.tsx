"use client";

import { useState } from "react";

import { BarChart3, Flame, Package, Settings } from "lucide-react";

import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

import { ProductPromotions } from "./ProductPromotions";
import { PromotionsStats } from "./PromotionsStats";
import { PromotionsTable } from "./PromotionsTable";

export const PromotionsWrapper = () => {
  const [activeTab, setActiveTab] = useState("campaigns");

  return (
    <div className="space-y-6">
      {/* Statistics Overview */}
      <PromotionsStats />

      {/* Main Content with Tabs */}
      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-6"
      >
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="campaigns" className="flex items-center gap-2">
            <Flame className="h-4 w-4" />
            <span className="hidden sm:inline">Campaigns</span>
          </TabsTrigger>
          <TabsTrigger value="products" className="flex items-center gap-2">
            <Package className="h-4 w-4" />
            <span className="hidden sm:inline">Product Sales</span>
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            <span className="hidden sm:inline">Analytics</span>
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            <span className="hidden sm:inline">Settings</span>
          </TabsTrigger>
        </TabsList>

        {/* Campaigns Tab */}
        <TabsContent value="campaigns" className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                Promotional Campaigns
              </h3>
              <p className="text-sm text-gray-600">
                Manage your promotional campaigns, discounts, and special offers
              </p>
            </div>
          </div>

          <Card>
            <CardContent className="p-6">
              <PromotionsTable />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Product Sales Tab */}
        <TabsContent value="products" className="space-y-6">
          <ProductPromotions />
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          <div className="grid gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="py-12 text-center">
                  <BarChart3 className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-lg font-medium text-gray-900">
                    Promotion Analytics
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Detailed analytics and performance metrics for your
                    promotions will be displayed here.
                  </p>
                  <div className="mt-6 grid gap-4 md:grid-cols-3">
                    <div className="rounded-lg border p-4">
                      <div className="text-2xl font-bold text-blue-600">
                        €12,450
                      </div>
                      <div className="text-sm text-gray-600">
                        Revenue from promotions
                      </div>
                    </div>
                    <div className="rounded-lg border p-4">
                      <div className="text-2xl font-bold text-green-600">
                        24.5%
                      </div>
                      <div className="text-sm text-gray-600">
                        Conversion rate increase
                      </div>
                    </div>
                    <div className="rounded-lg border p-4">
                      <div className="text-2xl font-bold text-orange-600">
                        1,247
                      </div>
                      <div className="text-sm text-gray-600">
                        Customers reached
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Settings Tab */}
        <TabsContent value="settings" className="space-y-6">
          <div className="grid gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="py-12 text-center">
                  <Settings className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-lg font-medium text-gray-900">
                    Promotion Settings
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Configure global promotion settings, approval workflows, and
                    automation rules.
                  </p>
                  <div className="mx-auto mt-6 max-w-md space-y-4">
                    <div className="flex items-center justify-between rounded-lg border p-4">
                      <div className="text-left">
                        <div className="font-medium">
                          Auto-apply best discount
                        </div>
                        <div className="text-sm text-gray-500">
                          Automatically apply the best available discount
                        </div>
                      </div>
                      <div className="h-6 w-10 rounded-full bg-blue-600"></div>
                    </div>
                    <div className="flex items-center justify-between rounded-lg border p-4">
                      <div className="text-left">
                        <div className="font-medium">
                          Require approval for discounts {">"}50%
                        </div>
                        <div className="text-sm text-gray-500">
                          Large discounts need manager approval
                        </div>
                      </div>
                      <div className="h-6 w-10 rounded-full bg-gray-300"></div>
                    </div>
                    <div className="flex items-center justify-between rounded-lg border p-4">
                      <div className="text-left">
                        <div className="font-medium">Email notifications</div>
                        <div className="text-sm text-gray-500">
                          Notify when promotions start/end
                        </div>
                      </div>
                      <div className="h-6 w-10 rounded-full bg-blue-600"></div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
