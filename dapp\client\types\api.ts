export type ApiResponse<T> = {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
  count?: number;
};

export type PaginatedResponse<T> = {
  data: T[];
  total: number;
  page: number;
  totalPages: number;
  limit: number;
};

export type ApiError = {
  success: false;
  error: string;
  message?: string;
  statusCode?: number;
};

export type ApiSuccess<T> = {
  success: true;
  data: T;
  message?: string;
};
