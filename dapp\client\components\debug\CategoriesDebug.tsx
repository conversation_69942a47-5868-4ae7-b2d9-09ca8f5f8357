"use client";

import { useEffect, useState } from "react";

import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { useCategories } from "@/hooks/useCategories";
import { CategoryApiService } from "@/lib/api/categoryApi";

export const CategoriesDebug = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const { categories, loading, error } = useCategories();

  const addLog = (message: string) => {
    setTestResults((prev) => [
      ...prev,
      `${new Date().toLocaleTimeString()}: ${message}`,
    ]);
  };

  const testDirectAPI = async () => {
    addLog("🧪 Testing direct API calls...");

    try {
      // Test server connection
      addLog("Testing server connection...");
      const response = await fetch("http://localhost:3010/api");
      if (response.ok) {
        const data = await response.json();
        addLog(`✅ Server connected: ${JSON.stringify(data)}`);
      } else {
        addLog(`❌ Server error: ${response.status}`);
      }

      // Test categories endpoint
      addLog("Testing categories endpoint...");
      const categoriesResponse = await fetch(
        "http://localhost:3010/api/categories"
      );
      if (categoriesResponse.ok) {
        const categoriesData = await categoriesResponse.json();
        addLog(`✅ Categories endpoint: ${JSON.stringify(categoriesData)}`);
      } else {
        addLog(`❌ Categories endpoint error: ${categoriesResponse.status}`);
      }

      // Test CategoryApiService
      addLog("Testing CategoryApiService...");
      const categories = await CategoryApiService.getCategories();
      addLog(`✅ CategoryApiService: ${categories.length} categories`);
      categories.forEach((cat, index) => {
        addLog(`  Category ${index + 1}: ID=${cat._id}, Name=${cat.name}`);
      });
    } catch (error) {
      addLog(
        `❌ Error: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  };

  const clearLogs = () => {
    setTestResults([]);
  };

  useEffect(() => {
    addLog("🔄 useCategories hook initialized");
    addLog(
      `Loading: ${loading}, Error: ${error}, Categories: ${categories.length}`
    );
  }, [loading, error, categories.length]);

  return (
    <Card className="mx-auto w-full max-w-4xl">
      <CardHeader>
        <CardTitle>Categories API Debug Panel</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Hook Status */}
        <div className="grid grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="text-sm font-medium">Loading</div>
              <div
                className={`text-lg ${loading ? "text-yellow-600" : "text-green-600"}`}
              >
                {loading ? "Yes" : "No"}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-sm font-medium">Error</div>
              <div
                className={`text-lg ${error ? "text-red-600" : "text-green-600"}`}
              >
                {error || "None"}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-sm font-medium">Categories</div>
              <div className="text-lg text-blue-600">{categories.length}</div>
            </CardContent>
          </Card>
        </div>

        {/* Categories List */}
        {categories.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Categories from Hook</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {categories.map((category, index) => (
                  <div
                    key={category._id || index}
                    className="rounded border p-2"
                  >
                    <div className="font-medium">
                      ID: {category._id || "UNDEFINED"}
                    </div>
                    <div>Name: {category.name}</div>
                    <div>Active: {category.isActive ? "Yes" : "No"}</div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Test Controls */}
        <div className="flex gap-2">
          <Button onClick={testDirectAPI}>Test Direct API</Button>
          <Button variant="outline" onClick={clearLogs}>
            Clear Logs
          </Button>
        </div>

        {/* Test Results */}
        {testResults.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Test Results</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="max-h-96 overflow-y-auto rounded bg-gray-100 p-4">
                <pre className="whitespace-pre-wrap text-sm">
                  {testResults.join("\n")}
                </pre>
              </div>
            </CardContent>
          </Card>
        )}
      </CardContent>
    </Card>
  );
};
