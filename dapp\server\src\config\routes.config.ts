import type {
  ErrorRequestHandler,
  Express,
  NextFunction,
  Request,
  Response,
} from "express";

import { registerRoutes } from "../routes";

/**
 * Configure API routes for the Express application
 * @param app - Express application
 */
export const configRoutes = (app: Express): void => {
  try {
    // Register API routes
    registerRoutes(app);
    console.info("API routes registered successfully");

    // API error handler - properly typed as <PERSON>rrorRequestHandler
    const apiErrorHandler: ErrorRequestHandler = (
      err: Error,
      req: Request,
      res: Response,
      _next: NextFunction
    ) => {
      console.error(`API Error: ${err.message}`);
      return res.status(500).json({
        success: false,
        message: "Internal server error",
        error: process.env.NODE_ENV === "development" ? err.message : undefined,
      });
    };

    app.use("/api", apiErrorHandler);

    // Handle 404 for any unmatched API routes
    app.use("/api/*", (req: Request, res: Response) => {
      console.warn(`404 Not Found: ${req.method} ${req.url}`);
      return res.status(404).json({
        success: false,
        message: "Endpoint does not exist",
      });
    });

    console.info("Route configuration completed");
  } catch (error) {
    console.error("Failed to configure routes:", error);
    throw error;
  }
};
