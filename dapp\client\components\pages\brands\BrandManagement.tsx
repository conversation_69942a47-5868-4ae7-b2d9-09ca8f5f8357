"use client";

import React, { useEffect, useState } from "react";

import {
  Archive,
  Check,
  Copy,
  Edit,
  ExternalLink,
  Eye,
  Filter,
  Grid3X3,
  List,
  MoreHorizontal,
  Plus,
  RefreshCw,
  Search,
  Trash2,
  X,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { Switch } from "@/components/ui/switch";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Textarea } from "@/components/ui/textarea";
import { useBrandMutations, useBrands } from "@/hooks/useBrands";
import { Brand, CreateBrandDto } from "@/lib/api/brands";

interface BrandManagementProps {
  initialBrands?: Brand[];
  onBrandsChange?: (brands: Brand[]) => void;
}

export const BrandManagement: React.FC<BrandManagementProps> = ({
  initialBrands = [],
  onBrandsChange,
}) => {
  const router = useRouter();

  // API hooks
  const { brands: apiBrands, loading, error, refetch } = useBrands();
  const {
    createBrand,
    updateBrand,
    deleteBrand,
    loading: mutationLoading,
  } = useBrandMutations();

  // Local state
  const [brands, setBrands] = useState<Brand[]>(initialBrands);
  const [newBrand, setNewBrand] = useState<Partial<CreateBrandDto>>({
    name: "",
    description: "",
    logo: "",
    website: "",
    color: "#3B82F6",
    isActive: true,
  });
  const [editingBrandId, setEditingBrandId] = useState<string | null>(null);
  const [editForm, setEditForm] = useState<Partial<Brand>>({});
  const [showAddForm, setShowAddForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [showInactive, setShowInactive] = useState(false);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [sortBy, setSortBy] = useState<"name" | "created" | "products">("name");

  // Sync with API data
  useEffect(() => {
    if (apiBrands.length > 0) {
      setBrands(apiBrands);
      onBrandsChange?.(apiBrands);
    }
  }, [apiBrands, onBrandsChange]);

  // Filter brands based on search term and active status
  const filteredBrands = brands.filter((brand) => {
    const matchesSearch =
      brand.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      brand.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = showInactive || brand.isActive;
    return matchesSearch && matchesStatus;
  });

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    // updateFilters({
    //   search: value || undefined,
    //   isActive: showInactive ? undefined : true,
    // });
  };

  const handleToggleInactive = () => {
    const newShowInactive = !showInactive;
    setShowInactive(newShowInactive);
    // updateFilters({
    //   search: searchTerm || undefined,
    //   isActive: newShowInactive ? undefined : true,
    // });
  };

  const handleDeleteBrand = async (brand: Brand) => {
    if (brand.productCount > 0) {
      alert(
        `Cannot delete "${brand.name}" because it has ${brand.productCount} products. Please remove or reassign the products first.`
      );
      return;
    }

    if (
      confirm(
        `Are you sure you want to delete "${brand.name}"? This action cannot be undone.`
      )
    ) {
      await deleteBrand(brand._id);
    }
  };

  const handleRecalculateCounts = async () => {
    // await recalculateProductCounts();
  };

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <p>Error loading brands: {error}</p>
            <Button onClick={() => {}} className="mt-2">
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
        <div>
          <h1 className="text-2xl font-bold">Brand Management</h1>
          <p className="text-muted-foreground">
            Manage your product brands and their information
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRecalculateCounts}
            disabled={loading}
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Recalculate Counts
          </Button>
          <Button onClick={() => createBrand}>
            <Plus className="mr-2 h-4 w-4" />
            Add Brand
          </Button>
        </div>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Brands</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{brands.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Active Brands</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {brands.filter((b) => b.isActive).length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Brands with Products
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {brands.filter((b) => b.productCount > 0).length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Total Products
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {brands.reduce((sum, b) => sum + b.productCount, 0)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4 sm:flex-row">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                <Input
                  placeholder="Search brands..."
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Button
              variant={showInactive ? "default" : "outline"}
              onClick={handleToggleInactive}
            >
              <Filter className="mr-2 h-4 w-4" />
              {showInactive ? "Show All" : "Show Inactive"}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Brands Table */}
      <Card>
        <CardHeader>
          <CardTitle>Brands ({filteredBrands.length})</CardTitle>
          <CardDescription>
            Manage your brand catalog and view product associations
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-2">
              {[...Array(5)].map((_, i) => (
                <Skeleton key={i} className="h-12 w-full" />
              ))}
            </div>
          ) : filteredBrands.length === 0 ? (
            <div className="py-8 text-center">
              <p className="text-muted-foreground">No brands found</p>
              {searchTerm && (
                <Button
                  variant="link"
                  onClick={() => handleSearch("")}
                  className="mt-2"
                >
                  Clear search
                </Button>
              )}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Brand</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Products</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredBrands.map((brand) => (
                  <TableRow key={brand._id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <div
                          className="h-4 w-4 rounded-full border"
                          style={{ backgroundColor: brand.color }}
                        />
                        <div>
                          <div className="font-medium">{brand.name}</div>
                          {brand.website && (
                            <div className="text-sm text-muted-foreground">
                              {brand.website}
                            </div>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="max-w-xs truncate">
                        {brand.description || "No description"}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary">
                        {brand.productCount} products
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant={brand.isActive ? "default" : "secondary"}>
                        {brand.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {new Date(brand.createdAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => {}}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => {}}>
                            <Eye className="mr-2 h-4 w-4" />
                            View Products ({brand.productCount})
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => handleDeleteBrand(brand)}
                            className="text-red-600"
                            disabled={brand.productCount > 0}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
