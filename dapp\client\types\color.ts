import { RgbValue, HslValue, ColorFamily } from "./common";

export type Color = {
  _id: string;
  name: string;
  description: string;
  slug: string;
  hexValue: string;
  rgbValue: RgbValue;
  hslValue: HslValue;
  colorFamily: ColorFamily;
  isActive: boolean;
  productCount: number;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
};

export type CreateColorDto = {
  name: string;
  description?: string;
  hexValue: string;
  rgbValue?: RgbValue;
  hslValue?: HslValue;
  colorFamily: ColorFamily;
  isActive?: boolean;
  sortOrder?: number;
};

export type UpdateColorDto = {
  name?: string;
  description?: string;
  slug?: string;
  hexValue?: string;
  rgbValue?: RgbValue;
  hslValue?: HslValue;
  colorFamily?: ColorFamily;
  isActive?: boolean;
  sortOrder?: number;
};

export type ColorFilters = {
  isActive?: boolean;
  search?: string;
  colorFamily?: ColorFamily;
  hexValue?: string;
};

export type ColorsResponse = {
  colors: Color[];
  total: number;
  page: number;
  totalPages: number;
};
