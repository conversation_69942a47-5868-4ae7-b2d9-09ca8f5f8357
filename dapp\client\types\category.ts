export type Category = {
  _id: string;
  name: string;
  description: string;
  slug: string;
  icon?: string;
  color?: string;
  isActive: boolean;
  productCount: number;
  parentId?: string;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
};

export type CreateCategoryDto = {
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  isActive?: boolean;
  parentId?: string;
  sortOrder?: number;
};

export type UpdateCategoryDto = {
  name?: string;
  description?: string;
  slug?: string;
  icon?: string;
  color?: string;
  isActive?: boolean;
  parentId?: string;
  sortOrder?: number;
};

export type CategoryFilters = {
  isActive?: boolean;
  parentId?: string;
  search?: string;
};

export type CategoriesResponse = {
  categories: Category[];
  total: number;
  page: number;
  totalPages: number;
};
