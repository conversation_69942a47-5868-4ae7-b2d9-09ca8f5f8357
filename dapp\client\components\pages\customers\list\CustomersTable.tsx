"use client";

import React from "react";

import { mockCustomers } from "@/constants/products";

import { CustomerRow } from "./CustomerRow";

export const CustomersTable = () => {
  return (
    <div className="rounded-lg border border-gray-200 bg-white">
      <div className="overflow-x-auto">
        <table className="w-full text-sm">
          <thead>
            <tr className="border-b bg-gray-50/50 text-gray-500">
              <th className="p-4 text-left font-medium">Customer</th>
              <th className="p-4 text-left font-medium">Contact</th>
              <th className="p-4 text-center font-medium">Status</th>
              <th className="p-4 text-center font-medium">Type</th>
              <th className="p-4 text-center font-medium">Orders</th>
              <th className="p-4 text-right font-medium">Total Spent</th>
              <th className="p-4 text-center font-medium">Last Order</th>
              <th className="p-4 text-center font-medium">Actions</th>
            </tr>
          </thead>
          <tbody>
            {mockCustomers.map((customer) => (
              <CustomerRow key={customer.id} customer={customer as any} />
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};
