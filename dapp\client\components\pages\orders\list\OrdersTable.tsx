"use client";

import React from "react";

import { Eye } from "lucide-react";

import { Skeleton } from "@/components/ui/skeleton";
import { useOrders } from "@/hooks/useOrders";

import { OrderRow } from "./OrderRow";

export const OrdersTable = () => {
  const { orders, loading, error } = useOrders();

  if (error) {
    return (
      <div className="rounded-lg border border-gray-200 bg-white p-8 text-center">
        <div className="text-red-600">
          <p>Error loading orders: {error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="rounded-lg border border-gray-200 bg-white">
      <div className="overflow-x-auto">
        <table className="w-full text-sm">
          <thead>
            <tr className="border-b bg-gray-50/50 text-gray-500">
              <th className="p-4 text-left font-medium">Order</th>
              <th className="p-4 text-left font-medium">Customer</th>
              <th className="p-4 text-left font-medium">Products</th>
              <th className="p-4 text-center font-medium">Status</th>
              <th className="p-4 text-center font-medium">Shipping</th>
              <th className="p-4 text-right font-medium">Total</th>
              <th className="p-4 text-center font-medium">Date</th>
              <th className="p-4 text-center font-medium">Actions</th>
            </tr>
          </thead>

          <tbody>
            {loading
              ? // Loading skeleton
                Array.from({ length: 5 }).map((_, index) => (
                  <tr key={index} className="border-t">
                    <td className="p-4">
                      <Skeleton className="h-4 w-20" />
                    </td>
                    <td className="p-4">
                      <div className="flex items-center gap-3">
                        <Skeleton className="h-8 w-8 rounded-full" />
                        <div className="space-y-1">
                          <Skeleton className="h-4 w-24" />
                          <Skeleton className="h-3 w-32" />
                        </div>
                      </div>
                    </td>
                    <td className="p-4">
                      <Skeleton className="h-4 w-16" />
                    </td>
                    <td className="p-4 text-center">
                      <Skeleton className="mx-auto h-6 w-16" />
                    </td>
                    <td className="p-4 text-center">
                      <Skeleton className="mx-auto h-6 w-16" />
                    </td>
                    <td className="p-4 text-right">
                      <Skeleton className="ml-auto h-4 w-16" />
                    </td>
                    <td className="p-4 text-center">
                      <Skeleton className="mx-auto h-4 w-20" />
                    </td>
                    <td className="p-4 text-center">
                      <Skeleton className="mx-auto h-8 w-8" />
                    </td>
                  </tr>
                ))
              : orders.map((order) => (
                  <OrderRow key={order._id} order={order} />
                ))}
          </tbody>
        </table>
      </div>

      {/* Empty state - show when no orders */}
      {!loading && orders.length === 0 && (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <div className="mb-4 rounded-full bg-gray-100 p-3">
            <Eye className="h-6 w-6 text-gray-400" />
          </div>
          <h3 className="mb-2 text-lg font-medium text-gray-900">
            No orders found
          </h3>
          <p className="text-gray-500">
            No orders match your current filters. Try adjusting your search
            criteria.
          </p>
        </div>
      )}
    </div>
  );
};
