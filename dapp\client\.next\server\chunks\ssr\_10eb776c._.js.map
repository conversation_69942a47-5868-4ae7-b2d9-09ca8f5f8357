{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border px-4 py-3 text-sm [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-background text-foreground\",\r\n        destructive:\r\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Alert = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\r\n>(({ className, variant, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    role=\"alert\"\r\n    className={cn(alertVariants({ variant }), className)}\r\n    {...props}\r\n  />\r\n))\r\nAlert.displayName = \"Alert\"\r\n\r\nconst AlertTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h5\r\n    ref={ref}\r\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertTitle.displayName = \"AlertTitle\"\r\n\r\nconst AlertDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDescription.displayName = \"AlertDescription\"\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB,2KACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,6WAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) {\r\n  return (\r\n    <div\r\n      className={cn(\"animate-pulse rounded-md bg-primary/10\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,6WAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/lib/api/products.ts"], "sourcesContent": ["/**\r\n * API functions for product operations\r\n * Handles all HTTP requests to the backend product endpoints\r\n */\r\n\r\n// Types for API responses\r\nexport interface ApiResponse<T> {\r\n  success: boolean;\r\n  data: T;\r\n  message?: string;\r\n  meta?: {\r\n    total: number;\r\n    page: number;\r\n    limit: number;\r\n    pages: number;\r\n  };\r\n}\r\n\r\nexport interface ProductFilters {\r\n  search?: string;\r\n  category?: string;\r\n  brand?: string;\r\n  minPrice?: number;\r\n  maxPrice?: number;\r\n  condition?: string;\r\n  inStock?: boolean;\r\n  tags?: string[];\r\n  status?: string;\r\n  sortBy?: \"price\" | \"createdAt\" | \"name\";\r\n  sortOrder?: \"asc\" | \"desc\";\r\n  page?: number;\r\n  limit?: number;\r\n}\r\n\r\n// Get API base URL from environment or default to localhost\r\nconst getApiBaseUrl = (): string => {\r\n  if (typeof window !== \"undefined\") {\r\n    // Client-side: use current origin or environment variable\r\n    return (\r\n      process.env.NEXT_PUBLIC_API_URL ||\r\n      `${window.location.protocol}//${window.location.hostname}:3001`\r\n    );\r\n  }\r\n  // Server-side: use environment variable or default\r\n  return process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3001\";\r\n};\r\n\r\nconst API_BASE_URL = getApiBaseUrl();\r\n\r\n/**\r\n * Generic fetch wrapper with error handling\r\n */\r\nasync function apiRequest<T>(\r\n  endpoint: string,\r\n  options: RequestInit = {}\r\n): Promise<ApiResponse<T>> {\r\n  const url = `${API_BASE_URL}/api${endpoint}`;\r\n\r\n  const defaultOptions: RequestInit = {\r\n    headers: {\r\n      \"Content-Type\": \"application/json\",\r\n      ...options.headers,\r\n    },\r\n    ...options,\r\n  };\r\n\r\n  console.log(`🌐 Making API request to: ${url}`);\r\n  console.log(`🌐 Request options:`, defaultOptions);\r\n\r\n  try {\r\n    const response = await fetch(url, defaultOptions);\r\n\r\n    console.log(`🌐 Response status: ${response.status}`);\r\n    console.log(`🌐 Response ok: ${response.ok}`);\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response.text();\r\n      console.error(`🌐 Response error text:`, errorText);\r\n      throw new Error(\r\n        `HTTP error! status: ${response.status}, message: ${errorText}`\r\n      );\r\n    }\r\n\r\n    const data = await response.json();\r\n    console.log(`🌐 Response data:`, data);\r\n    return data;\r\n  } catch (error) {\r\n    console.error(`🌐 API request failed for ${endpoint}:`, error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Get all products with optional filtering\r\n */\r\nexport async function getProducts(\r\n  filters: ProductFilters = {}\r\n): Promise<ApiResponse<any[]>> {\r\n  const searchParams = new URLSearchParams();\r\n\r\n  // Add filters to search params\r\n  Object.entries(filters).forEach(([key, value]) => {\r\n    if (value !== undefined && value !== null && value !== \"\") {\r\n      if (Array.isArray(value)) {\r\n        value.forEach((item) => searchParams.append(key, item.toString()));\r\n      } else {\r\n        searchParams.append(key, value.toString());\r\n      }\r\n    }\r\n  });\r\n\r\n  const queryString = searchParams.toString();\r\n  const endpoint = queryString ? `/products?${queryString}` : \"/products\";\r\n\r\n  return apiRequest<any[]>(endpoint);\r\n}\r\n\r\n/**\r\n * Get a single product by ID\r\n */\r\nexport async function getProductById(id: string): Promise<ApiResponse<any>> {\r\n  return apiRequest<any>(`/products/${id}`);\r\n}\r\n\r\n/**\r\n * Get a product for editing (with safe field list)\r\n */\r\nexport async function getProductForEdit(id: string): Promise<ApiResponse<any>> {\r\n  return apiRequest<any>(`/products/${id}/edit`);\r\n}\r\n\r\n/**\r\n * Create a new product\r\n */\r\nexport async function createProduct(\r\n  productData: any\r\n): Promise<ApiResponse<any>> {\r\n  console.log(\"🌐 API createProduct called with data:\", productData);\r\n  console.log(\"🌐 API Base URL:\", API_BASE_URL);\r\n\r\n  try {\r\n    const result = await apiRequest<any>(\"/products\", {\r\n      method: \"POST\",\r\n      body: JSON.stringify(productData),\r\n    });\r\n    console.log(\"🌐 API createProduct response:\", result);\r\n    return result;\r\n  } catch (error) {\r\n    console.error(\"🌐 API createProduct error:\", error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Update an existing product\r\n */\r\nexport async function updateProduct(\r\n  id: string,\r\n  productData: any\r\n): Promise<ApiResponse<any>> {\r\n  return apiRequest<any>(`/products/${id}`, {\r\n    method: \"PUT\",\r\n    body: JSON.stringify(productData),\r\n  });\r\n}\r\n\r\n/**\r\n * Update product with safe fields (PATCH)\r\n */\r\nexport async function updateProductSafe(\r\n  id: string,\r\n  productData: any\r\n): Promise<ApiResponse<any>> {\r\n  return apiRequest<any>(`/products/${id}/edit`, {\r\n    method: \"PATCH\",\r\n    body: JSON.stringify(productData),\r\n  });\r\n}\r\n\r\n/**\r\n * Delete a product\r\n */\r\nexport async function deleteProduct(id: string): Promise<ApiResponse<boolean>> {\r\n  return apiRequest<boolean>(`/products/${id}`, {\r\n    method: \"DELETE\",\r\n  });\r\n}\r\n\r\n/**\r\n * Health check for API connection\r\n */\r\nexport async function checkApiHealth(): Promise<boolean> {\r\n  try {\r\n    const response = await fetch(`${API_BASE_URL}/api`);\r\n    return response.ok;\r\n  } catch (error) {\r\n    console.error(\"API health check failed:\", error);\r\n    return false;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,0BAA0B;;;;;;;;;;;AA6B1B,4DAA4D;AAC5D,MAAM,gBAAgB;IACpB,uCAAmC;;IAMnC;IACA,mDAAmD;IACnD,OAAO,6DAAmC;AAC5C;AAEA,MAAM,eAAe;AAErB;;CAEC,GACD,eAAe,WACb,QAAgB,EAChB,UAAuB,CAAC,CAAC;IAEzB,MAAM,MAAM,GAAG,aAAa,IAAI,EAAE,UAAU;IAE5C,MAAM,iBAA8B;QAClC,SAAS;YACP,gBAAgB;YAChB,GAAG,QAAQ,OAAO;QACpB;QACA,GAAG,OAAO;IACZ;IAEA,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,KAAK;IAC9C,QAAQ,GAAG,CAAC,CAAC,mBAAmB,CAAC,EAAE;IAEnC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,KAAK;QAElC,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QACpD,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,SAAS,EAAE,EAAE;QAE5C,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,CAAC,uBAAuB,CAAC,EAAE;YACzC,MAAM,IAAI,MACR,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,WAAW;QAEnE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,CAAC,iBAAiB,CAAC,EAAE;QACjC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,0BAA0B,EAAE,SAAS,CAAC,CAAC,EAAE;QACxD,MAAM;IACR;AACF;AAKO,eAAe,YACpB,UAA0B,CAAC,CAAC;IAE5B,MAAM,eAAe,IAAI;IAEzB,+BAA+B;IAC/B,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QAC3C,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;YACzD,IAAI,MAAM,OAAO,CAAC,QAAQ;gBACxB,MAAM,OAAO,CAAC,CAAC,OAAS,aAAa,MAAM,CAAC,KAAK,KAAK,QAAQ;YAChE,OAAO;gBACL,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;YACzC;QACF;IACF;IAEA,MAAM,cAAc,aAAa,QAAQ;IACzC,MAAM,WAAW,cAAc,CAAC,UAAU,EAAE,aAAa,GAAG;IAE5D,OAAO,WAAkB;AAC3B;AAKO,eAAe,eAAe,EAAU;IAC7C,OAAO,WAAgB,CAAC,UAAU,EAAE,IAAI;AAC1C;AAKO,eAAe,kBAAkB,EAAU;IAChD,OAAO,WAAgB,CAAC,UAAU,EAAE,GAAG,KAAK,CAAC;AAC/C;AAKO,eAAe,cACpB,WAAgB;IAEhB,QAAQ,GAAG,CAAC,0CAA0C;IACtD,QAAQ,GAAG,CAAC,oBAAoB;IAEhC,IAAI;QACF,MAAM,SAAS,MAAM,WAAgB,aAAa;YAChD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,QAAQ,GAAG,CAAC,kCAAkC;QAC9C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AAKO,eAAe,cACpB,EAAU,EACV,WAAgB;IAEhB,OAAO,WAAgB,CAAC,UAAU,EAAE,IAAI,EAAE;QACxC,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAKO,eAAe,kBACpB,EAAU,EACV,WAAgB;IAEhB,OAAO,WAAgB,CAAC,UAAU,EAAE,GAAG,KAAK,CAAC,EAAE;QAC7C,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAKO,eAAe,cAAc,EAAU;IAC5C,OAAO,WAAoB,CAAC,UAAU,EAAE,IAAI,EAAE;QAC5C,QAAQ;IACV;AACF;AAKO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,IAAI,CAAC;QAClD,OAAO,SAAS,EAAE;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 217, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/hooks/useProducts.ts"], "sourcesContent": ["/**\r\n * React hooks for product data management\r\n * Provides easy-to-use hooks for CRUD operations on products\r\n */\r\nimport { useCallback, useEffect, useState } from \"react\";\r\n\r\nimport { toast } from \"sonner\";\r\n\r\nimport {\r\n  type ApiResponse,\r\n  type ProductFilters,\r\n  createProduct,\r\n  deleteProduct,\r\n  getProductById,\r\n  getProductForEdit,\r\n  getProducts,\r\n  updateProduct,\r\n  updateProductSafe,\r\n} from \"@/lib/api/products\";\r\nimport { Product } from \"@/types/product\";\r\n\r\n// Hook state types\r\ntype UseProductsState = {\r\n  products: Product[];\r\n  loading: boolean;\r\n  error: string | null;\r\n  meta?: {\r\n    total: number;\r\n    page: number;\r\n    limit: number;\r\n    pages: number;\r\n  };\r\n};\r\n\r\ntype UseProductState = {\r\n  product: Product | null;\r\n  loading: boolean;\r\n  error: string | null;\r\n};\r\n\r\n/**\r\n * Hook for fetching and managing multiple products\r\n */\r\nexport function useProducts(initialFilters: ProductFilters = {}) {\r\n  const [state, setState] = useState<UseProductsState>({\r\n    products: [],\r\n    loading: true,\r\n    error: null,\r\n  });\r\n\r\n  const [filters, setFilters] = useState<ProductFilters>(initialFilters);\r\n\r\n  const fetchProducts = useCallback(\r\n    async (newFilters?: ProductFilters) => {\r\n      setState((prev) => ({ ...prev, loading: true, error: null }));\r\n\r\n      try {\r\n        const filtersToUse = newFilters || filters;\r\n        const response = await getProducts(filtersToUse);\r\n\r\n        setState({\r\n          products: response.data,\r\n          loading: false,\r\n          error: null,\r\n          meta: response.meta,\r\n        });\r\n      } catch (error) {\r\n        const errorMessage =\r\n          error instanceof Error ? error.message : \"Failed to fetch products\";\r\n        setState((prev) => ({\r\n          ...prev,\r\n          loading: false,\r\n          error: errorMessage,\r\n        }));\r\n        toast.error(\"Failed to load products\");\r\n      }\r\n    },\r\n    [filters]\r\n  );\r\n\r\n  const updateFilters = useCallback(\r\n    (newFilters: ProductFilters) => {\r\n      setFilters(newFilters);\r\n      fetchProducts(newFilters);\r\n    },\r\n    [fetchProducts]\r\n  );\r\n\r\n  const refreshProducts = useCallback(() => {\r\n    fetchProducts();\r\n  }, [fetchProducts]);\r\n\r\n  // Initial fetch\r\n  useEffect(() => {\r\n    fetchProducts();\r\n  }, [fetchProducts]);\r\n\r\n  return {\r\n    ...state,\r\n    filters,\r\n    updateFilters,\r\n    refreshProducts,\r\n    refetch: fetchProducts,\r\n  };\r\n}\r\n\r\n/**\r\n * Hook for fetching and managing a single product\r\n */\r\nexport function useProduct(id: string | null) {\r\n  const [state, setState] = useState<UseProductState>({\r\n    product: null,\r\n    loading: true,\r\n    error: null,\r\n  });\r\n\r\n  const fetchProduct = useCallback(async () => {\r\n    if (!id) {\r\n      setState({ product: null, loading: false, error: null });\r\n      return;\r\n    }\r\n\r\n    setState((prev) => ({ ...prev, loading: true, error: null }));\r\n\r\n    try {\r\n      const response = await getProductById(id);\r\n      setState({\r\n        product: response.data,\r\n        loading: false,\r\n        error: null,\r\n      });\r\n    } catch (error) {\r\n      const errorMessage =\r\n        error instanceof Error ? error.message : \"Failed to fetch product\";\r\n      setState((prev) => ({\r\n        ...prev,\r\n        loading: false,\r\n        error: errorMessage,\r\n      }));\r\n      toast.error(\"Failed to load product\");\r\n    }\r\n  }, [id]);\r\n\r\n  useEffect(() => {\r\n    fetchProduct();\r\n  }, [fetchProduct]);\r\n\r\n  return {\r\n    ...state,\r\n    refetch: fetchProduct,\r\n  };\r\n}\r\n\r\n/**\r\n * Hook for product CRUD operations\r\n */\r\nexport function useProductMutations() {\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  const createProductMutation = useCallback(\r\n    async (productData: Partial<Product>) => {\r\n      setLoading(true);\r\n      try {\r\n        const response = await createProduct(productData);\r\n        toast.success(\"Product created successfully\");\r\n        return response.data;\r\n      } catch (error) {\r\n        const errorMessage =\r\n          error instanceof Error ? error.message : \"Failed to create product\";\r\n        toast.error(errorMessage);\r\n        throw error;\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    },\r\n    []\r\n  );\r\n\r\n  const updateProductMutation = useCallback(\r\n    async (id: string, productData: Partial<Product>) => {\r\n      setLoading(true);\r\n      try {\r\n        const response = await updateProduct(id, productData);\r\n        toast.success(\"Product updated successfully\");\r\n        return response.data;\r\n      } catch (error) {\r\n        const errorMessage =\r\n          error instanceof Error ? error.message : \"Failed to update product\";\r\n        toast.error(errorMessage);\r\n        throw error;\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    },\r\n    []\r\n  );\r\n\r\n  const updateProductSafeMutation = useCallback(\r\n    async (id: string, productData: Partial<Product>) => {\r\n      setLoading(true);\r\n      try {\r\n        const response = await updateProductSafe(id, productData);\r\n        toast.success(\"Product updated successfully\");\r\n        return response.data;\r\n      } catch (error) {\r\n        const errorMessage =\r\n          error instanceof Error ? error.message : \"Failed to update product\";\r\n        toast.error(errorMessage);\r\n        throw error;\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    },\r\n    []\r\n  );\r\n\r\n  const deleteProductMutation = useCallback(async (id: string) => {\r\n    setLoading(true);\r\n    try {\r\n      await deleteProduct(id);\r\n      toast.success(\"Product deleted successfully\");\r\n      return true;\r\n    } catch (error) {\r\n      const errorMessage =\r\n        error instanceof Error ? error.message : \"Failed to delete product\";\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  return {\r\n    loading,\r\n    createProduct: createProductMutation,\r\n    updateProduct: updateProductMutation,\r\n    updateProductSafe: updateProductSafeMutation,\r\n    deleteProduct: deleteProductMutation,\r\n  };\r\n}\r\n\r\n/**\r\n * Hook for fetching product for editing\r\n */\r\nexport function useProductForEdit(id: string | null) {\r\n  const [state, setState] = useState<\r\n    UseProductState & { editableFields?: string[] }\r\n  >({\r\n    product: null,\r\n    loading: true,\r\n    error: null,\r\n    editableFields: [],\r\n  });\r\n\r\n  const fetchProductForEdit = useCallback(async () => {\r\n    if (!id) {\r\n      setState({\r\n        product: null,\r\n        loading: false,\r\n        error: null,\r\n        editableFields: [],\r\n      });\r\n      return;\r\n    }\r\n\r\n    setState((prev) => ({ ...prev, loading: true, error: null }));\r\n\r\n    try {\r\n      const response = await getProductForEdit(id);\r\n      setState({\r\n        product: response.data,\r\n        loading: false,\r\n        error: null,\r\n        editableFields: response.editableFields || [],\r\n      });\r\n    } catch (error) {\r\n      const errorMessage =\r\n        error instanceof Error\r\n          ? error.message\r\n          : \"Failed to fetch product for editing\";\r\n      setState((prev) => ({\r\n        ...prev,\r\n        loading: false,\r\n        error: errorMessage,\r\n      }));\r\n      toast.error(\"Failed to load product for editing\");\r\n    }\r\n  }, [id]);\r\n\r\n  useEffect(() => {\r\n    fetchProductForEdit();\r\n  }, [fetchProductForEdit]);\r\n\r\n  return {\r\n    ...state,\r\n    refetch: fetchProductForEdit,\r\n  };\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AACD;AAEA;AAEA;;;;AAmCO,SAAS,YAAY,iBAAiC,CAAC,CAAC;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAoB;QACnD,UAAU,EAAE;QACZ,SAAS;QACT,OAAO;IACT;IAEA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAkB;IAEvD,MAAM,gBAAgB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAC9B,OAAO;QACL,SAAS,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK,CAAC;QAE3D,IAAI;YACF,MAAM,eAAe,cAAc;YACnC,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;YAEnC,SAAS;gBACP,UAAU,SAAS,IAAI;gBACvB,SAAS;gBACT,OAAO;gBACP,MAAM,SAAS,IAAI;YACrB;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC3C,SAAS,CAAC,OAAS,CAAC;oBAClB,GAAG,IAAI;oBACP,SAAS;oBACT,OAAO;gBACT,CAAC;YACD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF,GACA;QAAC;KAAQ;IAGX,MAAM,gBAAgB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAC9B,CAAC;QACC,WAAW;QACX,cAAc;IAChB,GACA;QAAC;KAAc;IAGjB,MAAM,kBAAkB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE;QAClC;IACF,GAAG;QAAC;KAAc;IAElB,gBAAgB;IAChB,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAc;IAElB,OAAO;QACL,GAAG,KAAK;QACR;QACA;QACA;QACA,SAAS;IACX;AACF;AAKO,SAAS,WAAW,EAAiB;IAC1C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAmB;QAClD,SAAS;QACT,SAAS;QACT,OAAO;IACT;IAEA,MAAM,eAAe,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI,CAAC,IAAI;YACP,SAAS;gBAAE,SAAS;gBAAM,SAAS;gBAAO,OAAO;YAAK;YACtD;QACF;QAEA,SAAS,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK,CAAC;QAE3D,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;YACtC,SAAS;gBACP,SAAS,SAAS,IAAI;gBACtB,SAAS;gBACT,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC3C,SAAS,CAAC,OAAS,CAAC;oBAClB,GAAG,IAAI;oBACP,SAAS;oBACT,OAAO;gBACT,CAAC;YACD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF,GAAG;QAAC;KAAG;IAEP,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAa;IAEjB,OAAO;QACL,GAAG,KAAK;QACR,SAAS;IACX;AACF;AAKO,SAAS;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,wBAAwB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EACtC,OAAO;QACL,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE;YACrC,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC3C,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF,GACA,EAAE;IAGJ,MAAM,wBAAwB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EACtC,OAAO,IAAY;QACjB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,IAAI;YACzC,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC3C,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF,GACA,EAAE;IAGJ,MAAM,4BAA4B,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAC1C,OAAO,IAAY;QACjB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,IAAI;YAC7C,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC3C,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF,GACA,EAAE;IAGJ,MAAM,wBAAwB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC/C,WAAW;QACX,IAAI;YACF,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE;YACpB,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC3C,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF,GAAG,EAAE;IAEL,OAAO;QACL;QACA,eAAe;QACf,eAAe;QACf,mBAAmB;QACnB,eAAe;IACjB;AACF;AAKO,SAAS,kBAAkB,EAAiB;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAE/B;QACA,SAAS;QACT,SAAS;QACT,OAAO;QACP,gBAAgB,EAAE;IACpB;IAEA,MAAM,sBAAsB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE;QACtC,IAAI,CAAC,IAAI;YACP,SAAS;gBACP,SAAS;gBACT,SAAS;gBACT,OAAO;gBACP,gBAAgB,EAAE;YACpB;YACA;QACF;QAEA,SAAS,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK,CAAC;QAE3D,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE;YACzC,SAAS;gBACP,SAAS,SAAS,IAAI;gBACtB,SAAS;gBACT,OAAO;gBACP,gBAAgB,SAAS,cAAc,IAAI,EAAE;YAC/C;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eACJ,iBAAiB,QACb,MAAM,OAAO,GACb;YACN,SAAS,CAAC,OAAS,CAAC;oBAClB,GAAG,IAAI;oBACP,SAAS;oBACT,OAAO;gBACT,CAAC;YACD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF,GAAG;QAAC;KAAG;IAEP,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAoB;IAExB,OAAO;QACL,GAAG,KAAK;QACR,SAAS;IACX;AACF", "debugId": null}}, {"offset": {"line": 464, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/products/details/ProductDetailsActions.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from \"react\";\r\n\r\nimport {\r\n  <PERSON><PERSON>eft,\r\n  <PERSON><PERSON>,\r\n  Edit,\r\n  Eye,\r\n  MoreHorizontal,\r\n  Save,\r\n  Trash2,\r\n  X,\r\n} from \"lucide-react\";\r\nimport Link from \"next/link\";\r\n\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\n\r\ntype ProductDetailsActionsProps = {\r\n  productId: string;\r\n  isEditing?: boolean;\r\n  onEditToggle?: () => void;\r\n  onSave?: () => void;\r\n  onCancel?: () => void;\r\n};\r\n\r\nexport const ProductDetailsActions = ({\r\n  productId,\r\n  isEditing = false,\r\n  onEditToggle,\r\n  onSave,\r\n  onCancel,\r\n}: ProductDetailsActionsProps) => {\r\n  const handleDuplicate = () => {\r\n    console.log(\"Duplicate product:\", productId);\r\n    // TODO: Implement duplicate functionality\r\n  };\r\n\r\n  const handleDelete = () => {\r\n    console.log(\"Delete product:\", productId);\r\n    // TODO: Implement delete functionality\r\n  };\r\n\r\n  const handleViewInStore = () => {\r\n    console.log(\"View in store:\", productId);\r\n    // TODO: Implement view in store functionality\r\n  };\r\n\r\n  if (isEditing) {\r\n    return (\r\n      <div className=\"flex gap-2\">\r\n        <Button size=\"sm\" onClick={onSave}>\r\n          <Save className=\"mr-2 h-4 w-4\" />\r\n          Save Changes\r\n        </Button>\r\n        <Button variant=\"outline\" size=\"sm\" onClick={onCancel}>\r\n          <X className=\"mr-2 h-4 w-4\" />\r\n          Cancel\r\n        </Button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex gap-2\">\r\n      <Button variant=\"outline\" size=\"sm\" asChild>\r\n        <Link href=\"/products/list\">\r\n          <ArrowLeft className=\"mr-2 h-4 w-4\" />\r\n          Back to Products\r\n        </Link>\r\n      </Button>\r\n\r\n      <Button variant=\"outline\" size=\"sm\" onClick={onEditToggle}>\r\n        <Edit className=\"mr-2 h-4 w-4\" />\r\n        Edit Product\r\n      </Button>\r\n\r\n      <DropdownMenu>\r\n        <DropdownMenuTrigger asChild>\r\n          <Button variant=\"outline\" size=\"sm\">\r\n            <MoreHorizontal className=\"h-4 w-4\" />\r\n          </Button>\r\n        </DropdownMenuTrigger>\r\n        <DropdownMenuContent align=\"end\">\r\n          <DropdownMenuItem onClick={handleViewInStore}>\r\n            <Eye className=\"mr-2 h-4 w-4\" />\r\n            View in Store\r\n          </DropdownMenuItem>\r\n          <DropdownMenuItem onClick={handleDuplicate}>\r\n            <Copy className=\"mr-2 h-4 w-4\" />\r\n            Duplicate Product\r\n          </DropdownMenuItem>\r\n          <DropdownMenuSeparator />\r\n          <DropdownMenuItem className=\"text-red-600\" onClick={handleDelete}>\r\n            <Trash2 className=\"mr-2 h-4 w-4\" />\r\n            Delete Product\r\n          </DropdownMenuItem>\r\n        </DropdownMenuContent>\r\n      </DropdownMenu>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAEA;AACA;AAjBA;;;;;;AAiCO,MAAM,wBAAwB,CAAC,EACpC,SAAS,EACT,YAAY,KAAK,EACjB,YAAY,EACZ,MAAM,EACN,QAAQ,EACmB;IAC3B,MAAM,kBAAkB;QACtB,QAAQ,GAAG,CAAC,sBAAsB;IAClC,0CAA0C;IAC5C;IAEA,MAAM,eAAe;QACnB,QAAQ,GAAG,CAAC,mBAAmB;IAC/B,uCAAuC;IACzC;IAEA,MAAM,oBAAoB;QACxB,QAAQ,GAAG,CAAC,kBAAkB;IAC9B,8CAA8C;IAChD;IAEA,IAAI,WAAW;QACb,qBACE,6WAAC;YAAI,WAAU;;8BACb,6WAAC,2HAAA,CAAA,SAAM;oBAAC,MAAK;oBAAK,SAAS;;sCACzB,6WAAC,sRAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;8BAGnC,6WAAC,2HAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;oBAAK,SAAS;;sCAC3C,6WAAC,gRAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;;IAKtC;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BACb,6WAAC,2HAAA,CAAA,SAAM;gBAAC,SAAQ;gBAAU,MAAK;gBAAK,OAAO;0BACzC,cAAA,6WAAC,2RAAA,CAAA,UAAI;oBAAC,MAAK;;sCACT,6WAAC,oSAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;0BAK1C,6WAAC,2HAAA,CAAA,SAAM;gBAAC,SAAQ;gBAAU,MAAK;gBAAK,SAAS;;kCAC3C,6WAAC,+RAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;0BAInC,6WAAC,qIAAA,CAAA,eAAY;;kCACX,6WAAC,qIAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,6WAAC,2HAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,MAAK;sCAC7B,cAAA,6WAAC,oSAAA,CAAA,iBAAc;gCAAC,WAAU;;;;;;;;;;;;;;;;kCAG9B,6WAAC,qIAAA,CAAA,sBAAmB;wBAAC,OAAM;;0CACzB,6WAAC,qIAAA,CAAA,mBAAgB;gCAAC,SAAS;;kDACzB,6WAAC,oRAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGlC,6WAAC,qIAAA,CAAA,mBAAgB;gCAAC,SAAS;;kDACzB,6WAAC,sRAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGnC,6WAAC,qIAAA,CAAA,wBAAqB;;;;;0CACtB,6WAAC,qIAAA,CAAA,mBAAgB;gCAAC,WAAU;gCAAe,SAAS;;kDAClD,6WAAC,8RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}, {"offset": {"line": 703, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-xl border bg-card text-card-foreground shadow\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 784, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6WAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2WACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 813, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n)\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,8QAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,8QAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 845, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/products/details/ProductDetailsSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from \"react\";\r\n\r\nimport { Info, Ruler, Tag, Weight } from \"lucide-react\";\r\n\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { Product } from \"@/types/product\";\r\n\r\ntype ProductSectionProps = {\r\n  product: Product;\r\n  isEditing: boolean;\r\n  onProductUpdate: (product: Product) => void;\r\n};\r\n\r\nexport const ProductDetailsSection = ({\r\n  product,\r\n  isEditing,\r\n  onProductUpdate,\r\n}: ProductSectionProps) => {\r\n  const [editedProduct, setEditedProduct] = useState(product);\r\n  const [tags, setTags] = useState(product.tags || []);\r\n\r\n  const handleInputChange = (field: string, value: string) => {\r\n    setEditedProduct((prev) => ({\r\n      ...prev,\r\n      [field]: value,\r\n    }));\r\n  };\r\n\r\n  const handleTagRemove = (tagToRemove: string) => {\r\n    setTags(tags.filter((tag) => tag !== tagToRemove));\r\n  };\r\n\r\n  const handleTagAdd = (newTag: string) => {\r\n    if (newTag && !tags.includes(newTag)) {\r\n      setTags([...tags, newTag]);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle className=\"flex items-center gap-2\">\r\n          <Info className=\"h-5 w-5\" />\r\n          Additional Details\r\n        </CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        {/* Product Tags */}\r\n        <div>\r\n          <Label>Product Tags</Label>\r\n          {isEditing ? (\r\n            <div className=\"mt-1 space-y-2\">\r\n              <div className=\"flex flex-wrap gap-1\">\r\n                {tags.map((tag, index) => (\r\n                  <Badge\r\n                    key={index}\r\n                    variant=\"secondary\"\r\n                    className=\"cursor-pointer hover:bg-red-100\"\r\n                    onClick={() => handleTagRemove(tag)}\r\n                  >\r\n                    {tag} ×\r\n                  </Badge>\r\n                ))}\r\n              </div>\r\n              <Input\r\n                placeholder=\"Add a tag and press Enter\"\r\n                onKeyPress={(e) => {\r\n                  if (e.key === \"Enter\") {\r\n                    handleTagAdd(e.currentTarget.value);\r\n                    e.currentTarget.value = \"\";\r\n                  }\r\n                }}\r\n              />\r\n            </div>\r\n          ) : (\r\n            <div className=\"mt-1 flex flex-wrap gap-1\">\r\n              {tags.map((tag, index) => (\r\n                <Badge key={index} variant=\"secondary\">\r\n                  <Tag className=\"mr-1 h-3 w-3\" />\r\n                  {tag}\r\n                </Badge>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Dimensions */}\r\n        <div>\r\n          <Label>Dimensions</Label>\r\n          {isEditing ? (\r\n            <div className=\"mt-1 grid grid-cols-3 gap-2\">\r\n              <Input placeholder=\"Length\" />\r\n              <Input placeholder=\"Width\" />\r\n              <Input placeholder=\"Height\" />\r\n            </div>\r\n          ) : (\r\n            <div className=\"mt-1 flex items-center gap-1 text-gray-600\">\r\n              <Ruler className=\"h-4 w-4\" />\r\n              <span>\r\n                {product.dimensions\r\n                  ? `${product.dimensions.width} × ${product.dimensions.height} × ${product.dimensions.depth} ${product.dimensions.unit}`\r\n                  : \"Not specified\"}\r\n              </span>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Weight */}\r\n        <div>\r\n          <Label htmlFor=\"weight\">Weight</Label>\r\n          {isEditing ? (\r\n            <Input id=\"weight\" placeholder=\"Weight in kg\" className=\"mt-1\" />\r\n          ) : (\r\n            <div className=\"mt-1 flex items-center gap-1 text-gray-600\">\r\n              <Weight className=\"h-4 w-4\" />\r\n              <span>\r\n                {product.weight\r\n                  ? `${product.weight.value} ${product.weight.unit}`\r\n                  : \"Not specified\"}\r\n              </span>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Material */}\r\n        <div>\r\n          <Label htmlFor=\"material\">Material</Label>\r\n          {isEditing ? (\r\n            <Input\r\n              id=\"material\"\r\n              placeholder=\"e.g. Cotton, Polyester\"\r\n              className=\"mt-1\"\r\n            />\r\n          ) : (\r\n            <p className=\"mt-1 text-gray-600\">\r\n              {product.material || \"Not specified\"}\r\n            </p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Color */}\r\n        <div>\r\n          <Label htmlFor=\"color\">Color</Label>\r\n          {isEditing ? (\r\n            <Input id=\"color\" placeholder=\"e.g. Blue, Red\" className=\"mt-1\" />\r\n          ) : (\r\n            <p className=\"mt-1 text-gray-600\">\r\n              {product.color || \"Not specified\"}\r\n            </p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Care Instructions */}\r\n        {!isEditing && (\r\n          <div>\r\n            <Label>Care Instructions</Label>\r\n            <div className=\"mt-1 space-y-1 text-sm text-gray-600\">\r\n              <p>• Machine wash cold</p>\r\n              <p>• Do not bleach</p>\r\n              <p>• Tumble dry low</p>\r\n              <p>• Iron on low heat</p>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Additional Information */}\r\n        {!isEditing && (\r\n          <div className=\"space-y-2 rounded-md bg-gray-50 p-3\">\r\n            <h4 className=\"text-sm font-medium\">Additional Information</h4>\r\n            <div className=\"space-y-1 text-sm text-gray-600\">\r\n              {product.origin?.country && (\r\n                <div className=\"flex justify-between\">\r\n                  <span>Country of Origin:</span>\r\n                  <span>{product.origin.country}</span>\r\n                </div>\r\n              )}\r\n              {product.warranty?.duration && (\r\n                <div className=\"flex justify-between\">\r\n                  <span>Warranty:</span>\r\n                  <span>\r\n                    {product.warranty.duration}{\" \"}\r\n                    {product.warranty.type || \"months\"}\r\n                  </span>\r\n                </div>\r\n              )}\r\n              {product.returnPolicy?.period && (\r\n                <div className=\"flex justify-between\">\r\n                  <span>Return Policy:</span>\r\n                  <span>{product.returnPolicy.period} Days</span>\r\n                </div>\r\n              )}\r\n              {product.yearMade && (\r\n                <div className=\"flex justify-between\">\r\n                  <span>Year Made:</span>\r\n                  <span>{product.yearMade}</span>\r\n                </div>\r\n              )}\r\n              {product.condition && (\r\n                <div className=\"flex justify-between\">\r\n                  <span>Condition:</span>\r\n                  <span className=\"capitalize\">{product.condition}</span>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AATA;;;;;;;;AAkBO,MAAM,wBAAwB,CAAC,EACpC,OAAO,EACP,SAAS,EACT,eAAe,EACK;IACpB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,IAAI,IAAI,EAAE;IAEnD,MAAM,oBAAoB,CAAC,OAAe;QACxC,iBAAiB,CAAC,OAAS,CAAC;gBAC1B,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,kBAAkB,CAAC;QACvB,QAAQ,KAAK,MAAM,CAAC,CAAC,MAAQ,QAAQ;IACvC;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,UAAU,CAAC,KAAK,QAAQ,CAAC,SAAS;YACpC,QAAQ;mBAAI;gBAAM;aAAO;QAC3B;IACF;IAEA,qBACE,6WAAC,yHAAA,CAAA,OAAI;;0BACH,6WAAC,yHAAA,CAAA,aAAU;0BACT,cAAA,6WAAC,yHAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,6WAAC,sRAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBAAY;;;;;;;;;;;;0BAIhC,6WAAC,yHAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6WAAC;;0CACC,6WAAC,0HAAA,CAAA,QAAK;0CAAC;;;;;;4BACN,0BACC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACZ,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,6WAAC,0HAAA,CAAA,QAAK;gDAEJ,SAAQ;gDACR,WAAU;gDACV,SAAS,IAAM,gBAAgB;;oDAE9B;oDAAI;;+CALA;;;;;;;;;;kDASX,6WAAC,0HAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,YAAY,CAAC;4CACX,IAAI,EAAE,GAAG,KAAK,SAAS;gDACrB,aAAa,EAAE,aAAa,CAAC,KAAK;gDAClC,EAAE,aAAa,CAAC,KAAK,GAAG;4CAC1B;wCACF;;;;;;;;;;;qDAIJ,6WAAC;gCAAI,WAAU;0CACZ,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,6WAAC,0HAAA,CAAA,QAAK;wCAAa,SAAQ;;0DACzB,6WAAC,oRAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CACd;;uCAFS;;;;;;;;;;;;;;;;kCAUpB,6WAAC;;0CACC,6WAAC,0HAAA,CAAA,QAAK;0CAAC;;;;;;4BACN,0BACC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,0HAAA,CAAA,QAAK;wCAAC,aAAY;;;;;;kDACnB,6WAAC,0HAAA,CAAA,QAAK;wCAAC,aAAY;;;;;;kDACnB,6WAAC,0HAAA,CAAA,QAAK;wCAAC,aAAY;;;;;;;;;;;qDAGrB,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,wRAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6WAAC;kDACE,QAAQ,UAAU,GACf,GAAG,QAAQ,UAAU,CAAC,KAAK,CAAC,GAAG,EAAE,QAAQ,UAAU,CAAC,MAAM,CAAC,GAAG,EAAE,QAAQ,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,UAAU,CAAC,IAAI,EAAE,GACrH;;;;;;;;;;;;;;;;;;kCAOZ,6WAAC;;0CACC,6WAAC,0HAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAS;;;;;;4BACvB,0BACC,6WAAC,0HAAA,CAAA,QAAK;gCAAC,IAAG;gCAAS,aAAY;gCAAe,WAAU;;;;;qDAExD,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,0RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6WAAC;kDACE,QAAQ,MAAM,GACX,GAAG,QAAQ,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,MAAM,CAAC,IAAI,EAAE,GAChD;;;;;;;;;;;;;;;;;;kCAOZ,6WAAC;;0CACC,6WAAC,0HAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAW;;;;;;4BACzB,0BACC,6WAAC,0HAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,aAAY;gCACZ,WAAU;;;;;qDAGZ,6WAAC;gCAAE,WAAU;0CACV,QAAQ,QAAQ,IAAI;;;;;;;;;;;;kCAM3B,6WAAC;;0CACC,6WAAC,0HAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAQ;;;;;;4BACtB,0BACC,6WAAC,0HAAA,CAAA,QAAK;gCAAC,IAAG;gCAAQ,aAAY;gCAAiB,WAAU;;;;;qDAEzD,6WAAC;gCAAE,WAAU;0CACV,QAAQ,KAAK,IAAI;;;;;;;;;;;;oBAMvB,CAAC,2BACA,6WAAC;;0CACC,6WAAC,0HAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;kDAAE;;;;;;kDACH,6WAAC;kDAAE;;;;;;kDACH,6WAAC;kDAAE;;;;;;kDACH,6WAAC;kDAAE;;;;;;;;;;;;;;;;;;oBAMR,CAAC,2BACA,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAG,WAAU;0CAAsB;;;;;;0CACpC,6WAAC;gCAAI,WAAU;;oCACZ,QAAQ,MAAM,EAAE,yBACf,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;0DAAK;;;;;;0DACN,6WAAC;0DAAM,QAAQ,MAAM,CAAC,OAAO;;;;;;;;;;;;oCAGhC,QAAQ,QAAQ,EAAE,0BACjB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;0DAAK;;;;;;0DACN,6WAAC;;oDACE,QAAQ,QAAQ,CAAC,QAAQ;oDAAE;oDAC3B,QAAQ,QAAQ,CAAC,IAAI,IAAI;;;;;;;;;;;;;oCAI/B,QAAQ,YAAY,EAAE,wBACrB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;0DAAK;;;;;;0DACN,6WAAC;;oDAAM,QAAQ,YAAY,CAAC,MAAM;oDAAC;;;;;;;;;;;;;oCAGtC,QAAQ,QAAQ,kBACf,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;0DAAK;;;;;;0DACN,6WAAC;0DAAM,QAAQ,QAAQ;;;;;;;;;;;;oCAG1B,QAAQ,SAAS,kBAChB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;0DAAK;;;;;;0DACN,6WAAC;gDAAK,WAAU;0DAAc,QAAQ,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjE", "debugId": null}}, {"offset": {"line": 1392, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/products/details/ProductImageSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from \"react\";\r\n\r\nimport { Plus, Upload, X } from \"lucide-react\";\r\nimport Image from \"next/image\";\r\n\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { Product } from \"@/types/product\";\r\n\r\ntype ProductSectionProps = {\r\n  product: Product;\r\n  isEditing: boolean;\r\n  onProductUpdate: (product: Product) => void;\r\n};\r\n\r\nexport const ProductImageSection = ({\r\n  product,\r\n  isEditing,\r\n  onProductUpdate,\r\n}: ProductSectionProps) => {\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [selectedImageIndex, setSelectedImageIndex] = useState(0);\r\n  const [showImagePreview, setShowImagePreview] = useState(false);\r\n\r\n  // Get additional images from product data (excluding main image)\r\n  const mainImageUrl = product.mainImage || product.images;\r\n  const allProductImages = product.images || [];\r\n\r\n  // Filter out the main image from additional images to avoid duplicates\r\n  const additionalImages = allProductImages.filter(\r\n    (img) => img !== mainImageUrl\r\n  );\r\n\r\n  // Combine main image with additional images for preview\r\n  const allImages = [mainImageUrl, ...additionalImages].filter(Boolean);\r\n\r\n  const getStatusColor = (status: string) => {\r\n    switch (status) {\r\n      case \"active\":\r\n        return \"bg-green-100 text-green-700\";\r\n      case \"draft\":\r\n        return \"bg-yellow-100 text-yellow-700\";\r\n      case \"archived\":\r\n        return \"bg-gray-100 text-gray-700\";\r\n      default:\r\n        return \"bg-gray-100 text-gray-700\";\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle className=\"flex items-center justify-between\">\r\n          Product Images\r\n          {!isEditing && (\r\n            <div className=\"flex gap-2\">\r\n              {product.status && (\r\n                <Badge className={getStatusColor(product.status)}>\r\n                  {product.status.charAt(0).toUpperCase() +\r\n                    product.status.slice(1)}\r\n                </Badge>\r\n              )}\r\n              {product.isOnSale && (\r\n                <Badge className=\"bg-red-500 text-white\">Sale</Badge>\r\n              )}\r\n            </div>\r\n          )}\r\n        </CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        {/* Main Product Image */}\r\n        <div className=\"relative aspect-square overflow-hidden rounded-lg border\">\r\n          {(product.mainImage || product.images) &&\r\n          (product.mainImage || product.images).trim() !== \"\" ? (\r\n            <Image\r\n              src={product.mainImage || product.images}\r\n              alt={product.name}\r\n              fill\r\n              className=\"cursor-pointer object-cover transition-opacity duration-300 hover:opacity-90\"\r\n              onLoad={() => setIsLoading(false)}\r\n              onError={() => setIsLoading(false)}\r\n              onClick={() => {\r\n                setSelectedImageIndex(0);\r\n                setShowImagePreview(true);\r\n              }}\r\n            />\r\n          ) : (\r\n            <div className=\"flex h-full items-center justify-center bg-gray-100\">\r\n              <div className=\"text-center text-gray-500\">\r\n                <Upload className=\"mx-auto mb-2 h-12 w-12\" />\r\n                <p className=\"text-sm\">No image available</p>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {isEditing && (\r\n            <div className=\"absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 transition-opacity hover:opacity-100\">\r\n              <Button size=\"sm\" variant=\"secondary\">\r\n                <Upload className=\"mr-2 h-4 w-4\" />\r\n                Change Image\r\n              </Button>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Additional Images */}\r\n        <div>\r\n          <h4 className=\"mb-2 text-sm font-medium\">\r\n            Additional Images{\" \"}\r\n            {additionalImages.length > 0 && `(${additionalImages.length})`}\r\n          </h4>\r\n          {additionalImages.length === 0 ? (\r\n            <div className=\"rounded border border-dashed border-gray-300 p-4 text-center\">\r\n              <p className=\"text-sm text-gray-500\">No additional images</p>\r\n            </div>\r\n          ) : (\r\n            <div className=\"grid grid-cols-3 gap-2\">\r\n              {additionalImages.slice(0, 2).map((img, index) => (\r\n                <div\r\n                  key={index}\r\n                  className=\"group relative aspect-square overflow-hidden rounded border\"\r\n                >\r\n                  {img && img.trim() !== \"\" ? (\r\n                    <Image\r\n                      src={img}\r\n                      alt={`${product.name} ${index + 1}`}\r\n                      fill\r\n                      className=\"cursor-pointer object-cover transition-opacity hover:opacity-90\"\r\n                      onClick={() => {\r\n                        setSelectedImageIndex(index + 1);\r\n                        setShowImagePreview(true);\r\n                      }}\r\n                    />\r\n                  ) : (\r\n                    <div className=\"flex h-full items-center justify-center bg-gray-100\">\r\n                      <div className=\"text-center text-gray-400\">\r\n                        <Upload className=\"mx-auto h-6 w-6\" />\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                  {isEditing && (\r\n                    <div className=\"absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 transition-opacity group-hover:opacity-100\">\r\n                      <Button\r\n                        size=\"sm\"\r\n                        variant=\"ghost\"\r\n                        className=\"h-6 w-6 p-0 text-white\"\r\n                      >\r\n                        <X className=\"h-3 w-3\" />\r\n                      </Button>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              ))}\r\n\r\n              {/* Show more indicator if there are more than 2 additional images */}\r\n              {additionalImages.length > 2 && (\r\n                <div className=\"flex aspect-square items-center justify-center rounded border border-dashed border-gray-300 bg-gray-50\">\r\n                  <div className=\"text-center text-gray-500\">\r\n                    <span className=\"text-sm font-medium\">\r\n                      +{additionalImages.length - 2} more\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* Add Image Button */}\r\n              {isEditing && (\r\n                <div className=\"flex aspect-square items-center justify-center rounded border border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100\">\r\n                  <Button size=\"sm\" variant=\"ghost\" className=\"h-6 w-6 p-0\">\r\n                    <Plus className=\"h-3 w-3\" />\r\n                  </Button>\r\n                </div>\r\n              )}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Image Guidelines (when editing) */}\r\n        {isEditing && (\r\n          <div className=\"rounded-md bg-blue-50 p-3\">\r\n            <h5 className=\"text-sm font-medium text-blue-900\">\r\n              Image Guidelines\r\n            </h5>\r\n            <ul className=\"mt-1 text-xs text-blue-700\">\r\n              <li>• Use high-quality images (min 800x800px)</li>\r\n              <li>• Square aspect ratio recommended</li>\r\n              <li>• Maximum file size: 5MB</li>\r\n              <li>• Supported formats: JPG, PNG, WebP</li>\r\n            </ul>\r\n          </div>\r\n        )}\r\n\r\n        {/* Image Preview Modal */}\r\n        {showImagePreview && allImages.length > 0 && (\r\n          <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black/80\">\r\n            <div className=\"relative max-h-[90vh] max-w-[90vw]\">\r\n              {/* Close Button */}\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"sm\"\r\n                className=\"absolute -top-12 right-0 text-white hover:bg-white/20\"\r\n                onClick={() => setShowImagePreview(false)}\r\n              >\r\n                <X className=\"h-6 w-6\" />\r\n              </Button>\r\n\r\n              {/* Main Preview Image */}\r\n              <div className=\"relative h-[70vh] w-[80vw]\">\r\n                <Image\r\n                  src={allImages[selectedImageIndex]}\r\n                  alt={`${product.name} preview`}\r\n                  fill\r\n                  className=\"object-contain\"\r\n                />\r\n              </div>\r\n\r\n              {/* Navigation Arrows */}\r\n              {allImages.length > 1 && (\r\n                <>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"sm\"\r\n                    className=\"absolute left-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20\"\r\n                    onClick={() =>\r\n                      setSelectedImageIndex(\r\n                        selectedImageIndex === 0\r\n                          ? allImages.length - 1\r\n                          : selectedImageIndex - 1\r\n                      )\r\n                    }\r\n                  >\r\n                    ←\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"sm\"\r\n                    className=\"absolute right-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20\"\r\n                    onClick={() =>\r\n                      setSelectedImageIndex(\r\n                        selectedImageIndex === allImages.length - 1\r\n                          ? 0\r\n                          : selectedImageIndex + 1\r\n                      )\r\n                    }\r\n                  >\r\n                    →\r\n                  </Button>\r\n                </>\r\n              )}\r\n\r\n              {/* Image Counter */}\r\n              {allImages.length > 1 && (\r\n                <div className=\"absolute bottom-4 left-1/2 -translate-x-1/2 rounded-full bg-black/50 px-3 py-1 text-sm text-white\">\r\n                  {selectedImageIndex + 1} / {allImages.length}\r\n                </div>\r\n              )}\r\n\r\n              {/* Thumbnail Navigation */}\r\n              {allImages.length > 1 && (\r\n                <div className=\"mt-4 flex justify-center gap-2\">\r\n                  {allImages.map((img, index) => (\r\n                    <div\r\n                      key={index}\r\n                      className={`relative h-16 w-16 cursor-pointer overflow-hidden rounded border-2 ${\r\n                        index === selectedImageIndex\r\n                          ? \"border-white\"\r\n                          : \"border-transparent opacity-60 hover:opacity-80\"\r\n                      }`}\r\n                      onClick={() => setSelectedImageIndex(index)}\r\n                    >\r\n                      <Image\r\n                        src={img}\r\n                        alt={`Thumbnail ${index + 1}`}\r\n                        fill\r\n                        className=\"object-cover\"\r\n                      />\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AATA;;;;;;;;AAkBO,MAAM,sBAAsB,CAAC,EAClC,OAAO,EACP,SAAS,EACT,eAAe,EACK;IACpB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,iEAAiE;IACjE,MAAM,eAAe,QAAQ,SAAS,IAAI,QAAQ,MAAM;IACxD,MAAM,mBAAmB,QAAQ,MAAM,IAAI,EAAE;IAE7C,uEAAuE;IACvE,MAAM,mBAAmB,iBAAiB,MAAM,CAC9C,CAAC,MAAQ,QAAQ;IAGnB,wDAAwD;IACxD,MAAM,YAAY;QAAC;WAAiB;KAAiB,CAAC,MAAM,CAAC;IAE7D,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6WAAC,yHAAA,CAAA,OAAI;;0BACH,6WAAC,yHAAA,CAAA,aAAU;0BACT,cAAA,6WAAC,yHAAA,CAAA,YAAS;oBAAC,WAAU;;wBAAoC;wBAEtD,CAAC,2BACA,6WAAC;4BAAI,WAAU;;gCACZ,QAAQ,MAAM,kBACb,6WAAC,0HAAA,CAAA,QAAK;oCAAC,WAAW,eAAe,QAAQ,MAAM;8CAC5C,QAAQ,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KACnC,QAAQ,MAAM,CAAC,KAAK,CAAC;;;;;;gCAG1B,QAAQ,QAAQ,kBACf,6WAAC,0HAAA,CAAA,QAAK;oCAAC,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;0BAMnD,6WAAC,yHAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6WAAC;wBAAI,WAAU;;4BACZ,CAAC,QAAQ,SAAS,IAAI,QAAQ,MAAM,KACrC,CAAC,QAAQ,SAAS,IAAI,QAAQ,MAAM,EAAE,IAAI,OAAO,mBAC/C,6WAAC,4PAAA,CAAA,UAAK;gCACJ,KAAK,QAAQ,SAAS,IAAI,QAAQ,MAAM;gCACxC,KAAK,QAAQ,IAAI;gCACjB,IAAI;gCACJ,WAAU;gCACV,QAAQ,IAAM,aAAa;gCAC3B,SAAS,IAAM,aAAa;gCAC5B,SAAS;oCACP,sBAAsB;oCACtB,oBAAoB;gCACtB;;;;;qDAGF,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;;sDACb,6WAAC,0RAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6WAAC;4CAAE,WAAU;sDAAU;;;;;;;;;;;;;;;;;4BAK5B,2BACC,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC,2HAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,SAAQ;;sDACxB,6WAAC,0RAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;kCAQ3C,6WAAC;;0CACC,6WAAC;gCAAG,WAAU;;oCAA2B;oCACrB;oCACjB,iBAAiB,MAAM,GAAG,KAAK,CAAC,CAAC,EAAE,iBAAiB,MAAM,CAAC,CAAC,CAAC;;;;;;;4BAE/D,iBAAiB,MAAM,KAAK,kBAC3B,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;qDAGvC,6WAAC;gCAAI,WAAU;;oCACZ,iBAAiB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBACtC,6WAAC;4CAEC,WAAU;;gDAET,OAAO,IAAI,IAAI,OAAO,mBACrB,6WAAC,4PAAA,CAAA,UAAK;oDACJ,KAAK;oDACL,KAAK,GAAG,QAAQ,IAAI,CAAC,CAAC,EAAE,QAAQ,GAAG;oDACnC,IAAI;oDACJ,WAAU;oDACV,SAAS;wDACP,sBAAsB,QAAQ;wDAC9B,oBAAoB;oDACtB;;;;;yEAGF,6WAAC;oDAAI,WAAU;8DACb,cAAA,6WAAC;wDAAI,WAAU;kEACb,cAAA,6WAAC,0RAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;;;;;;gDAIvB,2BACC,6WAAC;oDAAI,WAAU;8DACb,cAAA,6WAAC,2HAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,WAAU;kEAEV,cAAA,6WAAC,gRAAA,CAAA,IAAC;4DAAC,WAAU;;;;;;;;;;;;;;;;;2CA5Bd;;;;;oCAoCR,iBAAiB,MAAM,GAAG,mBACzB,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC;4CAAI,WAAU;sDACb,cAAA,6WAAC;gDAAK,WAAU;;oDAAsB;oDAClC,iBAAiB,MAAM,GAAG;oDAAE;;;;;;;;;;;;;;;;;oCAOrC,2BACC,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,2HAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,SAAQ;4CAAQ,WAAU;sDAC1C,cAAA,6WAAC,sRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAS3B,2BACC,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAGlD,6WAAC;gCAAG,WAAU;;kDACZ,6WAAC;kDAAG;;;;;;kDACJ,6WAAC;kDAAG;;;;;;kDACJ,6WAAC;kDAAG;;;;;;kDACJ,6WAAC;kDAAG;;;;;;;;;;;;;;;;;;oBAMT,oBAAoB,UAAU,MAAM,GAAG,mBACtC,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC;4BAAI,WAAU;;8CAEb,6WAAC,2HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,oBAAoB;8CAEnC,cAAA,6WAAC,gRAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;8CAIf,6WAAC;oCAAI,WAAU;8CACb,cAAA,6WAAC,4PAAA,CAAA,UAAK;wCACJ,KAAK,SAAS,CAAC,mBAAmB;wCAClC,KAAK,GAAG,QAAQ,IAAI,CAAC,QAAQ,CAAC;wCAC9B,IAAI;wCACJ,WAAU;;;;;;;;;;;gCAKb,UAAU,MAAM,GAAG,mBAClB;;sDACE,6WAAC,2HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS,IACP,sBACE,uBAAuB,IACnB,UAAU,MAAM,GAAG,IACnB,qBAAqB;sDAG9B;;;;;;sDAGD,6WAAC,2HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS,IACP,sBACE,uBAAuB,UAAU,MAAM,GAAG,IACtC,IACA,qBAAqB;sDAG9B;;;;;;;;gCAOJ,UAAU,MAAM,GAAG,mBAClB,6WAAC;oCAAI,WAAU;;wCACZ,qBAAqB;wCAAE;wCAAI,UAAU,MAAM;;;;;;;gCAK/C,UAAU,MAAM,GAAG,mBAClB,6WAAC;oCAAI,WAAU;8CACZ,UAAU,GAAG,CAAC,CAAC,KAAK,sBACnB,6WAAC;4CAEC,WAAW,CAAC,mEAAmE,EAC7E,UAAU,qBACN,iBACA,kDACJ;4CACF,SAAS,IAAM,sBAAsB;sDAErC,cAAA,6WAAC,4PAAA,CAAA,UAAK;gDACJ,KAAK;gDACL,KAAK,CAAC,UAAU,EAAE,QAAQ,GAAG;gDAC7B,IAAI;gDACJ,WAAU;;;;;;2CAZP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwB3B", "debugId": null}}, {"offset": {"line": 1908, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Textarea = React.forwardRef<\r\n  HTMLTextAreaElement,\r\n  React.ComponentProps<\"textarea\">\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <textarea\r\n      className={cn(\r\n        \"flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className\r\n      )}\r\n      ref={ref}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nTextarea.displayName = \"Textarea\"\r\n\r\nexport { Textarea }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6WAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6QACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AACA,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1936, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/products/details/ProductInfoSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from \"react\";\r\n\r\nimport { Edit, Save, Star, X } from \"lucide-react\";\r\n\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport { Product } from \"@/types/product\";\r\n\r\ntype ProductSectionProps = {\r\n  product: Product;\r\n  isEditing: boolean;\r\n  onProductUpdate: (product: Product) => void;\r\n};\r\n\r\nexport const ProductInfoSection = ({\r\n  product,\r\n  isEditing,\r\n  onProductUpdate,\r\n}: ProductSectionProps) => {\r\n  const [editedProduct, setEditedProduct] = useState(product);\r\n\r\n  const handleInputChange = (field: string, value: string) => {\r\n    setEditedProduct((prev) => ({\r\n      ...prev,\r\n      [field]: value,\r\n    }));\r\n  };\r\n\r\n  const rating = product.rating || 0;\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle>Product Information</CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        {/* Product Name - Only show in edit mode */}\r\n        {isEditing && (\r\n          <div>\r\n            <Label htmlFor=\"product-name\">Product Name</Label>\r\n            <Input\r\n              id=\"product-name\"\r\n              value={editedProduct.name}\r\n              onChange={(e) => handleInputChange(\"name\", e.target.value)}\r\n              className=\"mt-1\"\r\n            />\r\n          </div>\r\n        )}\r\n\r\n        {/* Category and Brand */}\r\n        <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\r\n          <div>\r\n            <Label htmlFor=\"category\">Category</Label>\r\n            {isEditing ? (\r\n              <Input\r\n                id=\"category\"\r\n                value={editedProduct.category || \"\"}\r\n                onChange={(e) => handleInputChange(\"category\", e.target.value)}\r\n                className=\"mt-1\"\r\n                placeholder=\"e.g. Clothing\"\r\n              />\r\n            ) : (\r\n              <p className=\"mt-1 text-gray-600\">\r\n                {product.category || \"No category\"}\r\n              </p>\r\n            )}\r\n          </div>\r\n\r\n          <div>\r\n            <Label htmlFor=\"brand\">Brand</Label>\r\n            {isEditing ? (\r\n              <Input\r\n                id=\"brand\"\r\n                value={editedProduct.brand || \"\"}\r\n                onChange={(e) => handleInputChange(\"brand\", e.target.value)}\r\n                className=\"mt-1\"\r\n                placeholder=\"e.g. Nike\"\r\n              />\r\n            ) : (\r\n              <p className=\"mt-1 text-gray-600\">\r\n                {product.brand || \"No brand\"}\r\n              </p>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Rating */}\r\n        {!isEditing && rating > 0 && (\r\n          <div>\r\n            <Label>Customer Rating</Label>\r\n            <div className=\"mt-1 flex items-center gap-2\">\r\n              <div className=\"flex\">\r\n                {[...Array(5)].map((_, i) => (\r\n                  <Star\r\n                    key={i}\r\n                    className={`h-4 w-4 ${\r\n                      i < rating\r\n                        ? \"fill-yellow-400 text-yellow-400\"\r\n                        : \"text-gray-300\"\r\n                    }`}\r\n                  />\r\n                ))}\r\n              </div>\r\n              <span className=\"text-sm text-gray-600\">({rating}/5)</span>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Product Type */}\r\n        {!isEditing && product.productType && (\r\n          <div>\r\n            <Label>Product Type</Label>\r\n            <p className=\"mt-1 capitalize text-gray-600\">\r\n              {product.productType}\r\n            </p>\r\n          </div>\r\n        )}\r\n\r\n        {/* Model */}\r\n        {!isEditing && product.model && (\r\n          <div>\r\n            <Label>Model</Label>\r\n            <p className=\"mt-1 text-gray-600\">{product.model}</p>\r\n          </div>\r\n        )}\r\n\r\n        {/* Year Made */}\r\n        {!isEditing && product.yearMade && (\r\n          <div>\r\n            <Label>Year Made</Label>\r\n            <p className=\"mt-1 text-gray-600\">{product.yearMade}</p>\r\n          </div>\r\n        )}\r\n\r\n        {/* Description */}\r\n        <div>\r\n          <Label htmlFor=\"description\">Description</Label>\r\n          {isEditing ? (\r\n            <Textarea\r\n              id=\"description\"\r\n              placeholder=\"Enter product description...\"\r\n              className=\"mt-1\"\r\n              rows={4}\r\n            />\r\n          ) : (\r\n            <p className=\"mt-1 text-gray-600\">\r\n              {product.description || \"No description available\"}\r\n            </p>\r\n          )}\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAGA;AACA;AACA;AACA;AAVA;;;;;;;;AAmBO,MAAM,qBAAqB,CAAC,EACjC,OAAO,EACP,SAAS,EACT,eAAe,EACK;IACpB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,oBAAoB,CAAC,OAAe;QACxC,iBAAiB,CAAC,OAAS,CAAC;gBAC1B,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,SAAS,QAAQ,MAAM,IAAI;IAEjC,qBACE,6WAAC,yHAAA,CAAA,OAAI;;0BACH,6WAAC,yHAAA,CAAA,aAAU;0BACT,cAAA,6WAAC,yHAAA,CAAA,YAAS;8BAAC;;;;;;;;;;;0BAEb,6WAAC,yHAAA,CAAA,cAAW;gBAAC,WAAU;;oBAEpB,2BACC,6WAAC;;0CACC,6WAAC,0HAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAe;;;;;;0CAC9B,6WAAC,0HAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,OAAO,cAAc,IAAI;gCACzB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;gCACzD,WAAU;;;;;;;;;;;;kCAMhB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;;kDACC,6WAAC,0HAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAW;;;;;;oCACzB,0BACC,6WAAC,0HAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,OAAO,cAAc,QAAQ,IAAI;wCACjC,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;wCAC7D,WAAU;wCACV,aAAY;;;;;6DAGd,6WAAC;wCAAE,WAAU;kDACV,QAAQ,QAAQ,IAAI;;;;;;;;;;;;0CAK3B,6WAAC;;kDACC,6WAAC,0HAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAQ;;;;;;oCACtB,0BACC,6WAAC,0HAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,OAAO,cAAc,KAAK,IAAI;wCAC9B,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;wCAC1D,WAAU;wCACV,aAAY;;;;;6DAGd,6WAAC;wCAAE,WAAU;kDACV,QAAQ,KAAK,IAAI;;;;;;;;;;;;;;;;;;oBAOzB,CAAC,aAAa,SAAS,mBACtB,6WAAC;;0CACC,6WAAC,0HAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACZ;+CAAI,MAAM;yCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6WAAC,sRAAA,CAAA,OAAI;gDAEH,WAAW,CAAC,QAAQ,EAClB,IAAI,SACA,oCACA,iBACJ;+CALG;;;;;;;;;;kDASX,6WAAC;wCAAK,WAAU;;4CAAwB;4CAAE;4CAAO;;;;;;;;;;;;;;;;;;;oBAMtD,CAAC,aAAa,QAAQ,WAAW,kBAChC,6WAAC;;0CACC,6WAAC,0HAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,6WAAC;gCAAE,WAAU;0CACV,QAAQ,WAAW;;;;;;;;;;;;oBAMzB,CAAC,aAAa,QAAQ,KAAK,kBAC1B,6WAAC;;0CACC,6WAAC,0HAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,6WAAC;gCAAE,WAAU;0CAAsB,QAAQ,KAAK;;;;;;;;;;;;oBAKnD,CAAC,aAAa,QAAQ,QAAQ,kBAC7B,6WAAC;;0CACC,6WAAC,0HAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,6WAAC;gCAAE,WAAU;0CAAsB,QAAQ,QAAQ;;;;;;;;;;;;kCAKvD,6WAAC;;0CACC,6WAAC,0HAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAc;;;;;;4BAC5B,0BACC,6WAAC,6HAAA,CAAA,WAAQ;gCACP,IAAG;gCACH,aAAY;gCACZ,WAAU;gCACV,MAAM;;;;;qDAGR,6WAAC;gCAAE,WAAU;0CACV,QAAQ,WAAW,IAAI;;;;;;;;;;;;;;;;;;;;;;;;AAOtC", "debugId": null}}, {"offset": {"line": 2257, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/products/details/ProductInventorySection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from \"react\";\r\n\r\nimport { AlertTriangle, CheckCircle, Package, XCircle } from \"lucide-react\";\r\n\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { Product } from \"@/types/product\";\r\n\r\ntype ProductSectionProps = {\r\n  product: Product;\r\n  isEditing: boolean;\r\n  onProductUpdate: (product: Product) => void;\r\n};\r\n\r\nexport const ProductInventorySection = ({\r\n  product,\r\n  isEditing,\r\n  onProductUpdate,\r\n}: ProductSectionProps) => {\r\n  const [editedProduct, setEditedProduct] = useState(product);\r\n\r\n  const handleInputChange = (field: string, value: string) => {\r\n    setEditedProduct((prev) => ({\r\n      ...prev,\r\n      [field]: field === \"stock\" ? parseInt(value) || 0 : value,\r\n    }));\r\n  };\r\n\r\n  const stock = product.stock || 0;\r\n\r\n  const getStockStatus = (stock: number) => {\r\n    if (stock === 0)\r\n      return {\r\n        text: \"Out of Stock\",\r\n        color: \"text-red-600\",\r\n        bgColor: \"bg-red-100\",\r\n        icon: XCircle,\r\n      };\r\n    if (stock < 10)\r\n      return {\r\n        text: \"Low Stock\",\r\n        color: \"text-orange-600\",\r\n        bgColor: \"bg-orange-100\",\r\n        icon: AlertTriangle,\r\n      };\r\n    return {\r\n      text: \"In Stock\",\r\n      color: \"text-green-600\",\r\n      bgColor: \"bg-green-100\",\r\n      icon: CheckCircle,\r\n    };\r\n  };\r\n\r\n  const stockStatus = getStockStatus(stock);\r\n  const StockIcon = stockStatus.icon;\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle className=\"flex items-center gap-2\">\r\n          <Package className=\"h-5 w-5\" />\r\n          Inventory\r\n        </CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        {/* Stock Quantity */}\r\n        <div>\r\n          <Label htmlFor=\"stock\">Stock Quantity</Label>\r\n          {isEditing ? (\r\n            <Input\r\n              id=\"stock\"\r\n              type=\"number\"\r\n              value={editedProduct.stock || 0}\r\n              onChange={(e) => handleInputChange(\"stock\", e.target.value)}\r\n              className=\"mt-1\"\r\n              min=\"0\"\r\n            />\r\n          ) : (\r\n            <div className=\"mt-1 flex items-center gap-2\">\r\n              <span className=\"text-2xl font-bold\">{stock}</span>\r\n              <span className=\"text-gray-500\">units</span>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Stock Status */}\r\n        {!isEditing && (\r\n          <div>\r\n            <Label>Stock Status</Label>\r\n            <div className=\"mt-1 flex items-center gap-2\">\r\n              <Badge\r\n                className={`${stockStatus.bgColor} ${stockStatus.color} hover:${stockStatus.bgColor}`}\r\n              >\r\n                <StockIcon className=\"mr-1 h-3 w-3\" />\r\n                {stockStatus.text}\r\n              </Badge>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* SKU */}\r\n        <div>\r\n          <Label htmlFor=\"sku\">SKU</Label>\r\n          {isEditing ? (\r\n            <Input id=\"sku\" placeholder=\"Enter SKU\" className=\"mt-1\" />\r\n          ) : (\r\n            <p className=\"mt-1 font-mono text-sm text-gray-600\">\r\n              {product.sku ||\r\n                `SKU-${(product.id || product._id || \"UNKNOWN\").toString().padStart(6, \"0\")}`}\r\n            </p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Inventory Alerts */}\r\n        {!isEditing && (\r\n          <div className=\"space-y-2 rounded-md bg-gray-50 p-3\">\r\n            <h4 className=\"text-sm font-medium\">Inventory Alerts</h4>\r\n            <div className=\"space-y-1 text-sm\">\r\n              {stock === 0 ? (\r\n                <div className=\"flex items-center gap-1 text-red-600\">\r\n                  <XCircle className=\"h-3 w-3\" />\r\n                  <span>Product is out of stock</span>\r\n                </div>\r\n              ) : stock < 10 ? (\r\n                <div className=\"flex items-center gap-1 text-orange-600\">\r\n                  <AlertTriangle className=\"h-3 w-3\" />\r\n                  <span>Low stock warning</span>\r\n                </div>\r\n              ) : (\r\n                <div className=\"flex items-center gap-1 text-green-600\">\r\n                  <CheckCircle className=\"h-3 w-3\" />\r\n                  <span>Stock levels are healthy</span>\r\n                </div>\r\n              )}\r\n              {product.lowStockThreshold && (\r\n                <div className=\"text-gray-500\">\r\n                  Reorder point: {product.lowStockThreshold} units\r\n                </div>\r\n              )}\r\n              {product.stockManagement && (\r\n                <div className=\"text-gray-500\">\r\n                  Management: {product.stockManagement}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Quick Actions */}\r\n        {isEditing && (\r\n          <div className=\"space-y-2\">\r\n            <Label>Quick Stock Actions</Label>\r\n            <div className=\"flex gap-2\">\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => handleInputChange(\"stock\", \"0\")}\r\n                className=\"rounded bg-red-100 px-2 py-1 text-xs text-red-700 hover:bg-red-200\"\r\n              >\r\n                Mark Out of Stock\r\n              </button>\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => handleInputChange(\"stock\", \"50\")}\r\n                className=\"rounded bg-green-100 px-2 py-1 text-xs text-green-700 hover:bg-green-200\"\r\n              >\r\n                Restock (50)\r\n              </button>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AATA;;;;;;;;AAkBO,MAAM,0BAA0B,CAAC,EACtC,OAAO,EACP,SAAS,EACT,eAAe,EACK;IACpB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,oBAAoB,CAAC,OAAe;QACxC,iBAAiB,CAAC,OAAS,CAAC;gBAC1B,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE,UAAU,UAAU,SAAS,UAAU,IAAI;YACtD,CAAC;IACH;IAEA,MAAM,QAAQ,QAAQ,KAAK,IAAI;IAE/B,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GACZ,OAAO;YACL,MAAM;YACN,OAAO;YACP,SAAS;YACT,MAAM,gSAAA,CAAA,UAAO;QACf;QACF,IAAI,QAAQ,IACV,OAAO;YACL,MAAM;YACN,OAAO;YACP,SAAS;YACT,MAAM,4SAAA,CAAA,gBAAa;QACrB;QACF,OAAO;YACL,MAAM;YACN,OAAO;YACP,SAAS;YACT,MAAM,+SAAA,CAAA,cAAW;QACnB;IACF;IAEA,MAAM,cAAc,eAAe;IACnC,MAAM,YAAY,YAAY,IAAI;IAElC,qBACE,6WAAC,yHAAA,CAAA,OAAI;;0BACH,6WAAC,yHAAA,CAAA,aAAU;0BACT,cAAA,6WAAC,yHAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,6WAAC,4RAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAAY;;;;;;;;;;;;0BAInC,6WAAC,yHAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6WAAC;;0CACC,6WAAC,0HAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAQ;;;;;;4BACtB,0BACC,6WAAC,0HAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAK;gCACL,OAAO,cAAc,KAAK,IAAI;gCAC9B,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;gCAC1D,WAAU;gCACV,KAAI;;;;;qDAGN,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAK,WAAU;kDAAsB;;;;;;kDACtC,6WAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;oBAMrC,CAAC,2BACA,6WAAC;;0CACC,6WAAC,0HAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC,0HAAA,CAAA,QAAK;oCACJ,WAAW,GAAG,YAAY,OAAO,CAAC,CAAC,EAAE,YAAY,KAAK,CAAC,OAAO,EAAE,YAAY,OAAO,EAAE;;sDAErF,6WAAC;4CAAU,WAAU;;;;;;wCACpB,YAAY,IAAI;;;;;;;;;;;;;;;;;;kCAOzB,6WAAC;;0CACC,6WAAC,0HAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAM;;;;;;4BACpB,0BACC,6WAAC,0HAAA,CAAA,QAAK;gCAAC,IAAG;gCAAM,aAAY;gCAAY,WAAU;;;;;qDAElD,6WAAC;gCAAE,WAAU;0CACV,QAAQ,GAAG,IACV,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE,IAAI,QAAQ,GAAG,IAAI,SAAS,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;;;;;;;;;;;;oBAMpF,CAAC,2BACA,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAG,WAAU;0CAAsB;;;;;;0CACpC,6WAAC;gCAAI,WAAU;;oCACZ,UAAU,kBACT,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,gSAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,6WAAC;0DAAK;;;;;;;;;;;+CAEN,QAAQ,mBACV,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,4SAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,6WAAC;0DAAK;;;;;;;;;;;6DAGR,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,+SAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,6WAAC;0DAAK;;;;;;;;;;;;oCAGT,QAAQ,iBAAiB,kBACxB,6WAAC;wCAAI,WAAU;;4CAAgB;4CACb,QAAQ,iBAAiB;4CAAC;;;;;;;oCAG7C,QAAQ,eAAe,kBACtB,6WAAC;wCAAI,WAAU;;4CAAgB;4CAChB,QAAQ,eAAe;;;;;;;;;;;;;;;;;;;oBAQ7C,2BACC,6WAAC;wBAAI,WAAU;;0CACb,6WAAC,0HAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCACC,MAAK;wCACL,SAAS,IAAM,kBAAkB,SAAS;wCAC1C,WAAU;kDACX;;;;;;kDAGD,6WAAC;wCACC,MAAK;wCACL,SAAS,IAAM,kBAAkB,SAAS;wCAC1C,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 2640, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Switch = React.forwardRef<\r\n  React.ElementRef<typeof SwitchPrimitives.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <SwitchPrimitives.Root\r\n    className={cn(\r\n      \"peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  >\r\n    <SwitchPrimitives.Thumb\r\n      className={cn(\r\n        \"pointer-events-none block h-4 w-4 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0\"\r\n      )}\r\n    />\r\n  </SwitchPrimitives.Root>\r\n))\r\nSwitch.displayName = SwitchPrimitives.Root.displayName\r\n\r\nexport { Switch }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,OAAqB;QACpB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+XACA;QAED,GAAG,KAAK;QACT,KAAK;kBAEL,cAAA,6WAAC,+QAAA,CAAA,QAAsB;YACrB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKR,OAAO,WAAW,GAAG,+QAAA,CAAA,OAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2676, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/products/details/ProductPricingSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from \"react\";\r\n\r\nimport { DollarSign, TrendingDown, TrendingUp } from \"lucide-react\";\r\n\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { Switch } from \"@/components/ui/switch\";\r\nimport { Product } from \"@/types/product\";\r\n\r\ntype ProductSectionProps = {\r\n  product: Product;\r\n  isEditing: boolean;\r\n  onProductUpdate: (product: Product) => void;\r\n};\r\n\r\nexport const ProductPricingSection = ({\r\n  product,\r\n  isEditing,\r\n  onProductUpdate,\r\n}: ProductSectionProps) => {\r\n  const [editedProduct, setEditedProduct] = useState(product);\r\n\r\n  const handleInputChange = (field: string, value: string | boolean) => {\r\n    setEditedProduct((prev) => ({\r\n      ...prev,\r\n      [field]: value,\r\n    }));\r\n  };\r\n\r\n  // Handle both string and number price formats\r\n  const currentPrice =\r\n    typeof product.price === \"string\"\r\n      ? parseFloat(product.price.replace(\"$\", \"\"))\r\n      : parseFloat(product.price);\r\n\r\n  const originalPrice = product.originalPrice\r\n    ? typeof product.originalPrice === \"string\"\r\n      ? parseFloat(product.originalPrice.replace(\"$\", \"\"))\r\n      : parseFloat(product.originalPrice)\r\n    : null;\r\n\r\n  const discount = originalPrice\r\n    ? Math.round(((originalPrice - currentPrice) / originalPrice) * 100)\r\n    : 0;\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle className=\"flex items-center gap-2\">\r\n          <DollarSign className=\"h-5 w-5\" />\r\n          Pricing\r\n        </CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        {/* Current Price */}\r\n        <div>\r\n          <Label htmlFor=\"current-price\">Current Price</Label>\r\n          {isEditing ? (\r\n            <Input\r\n              id=\"current-price\"\r\n              value={editedProduct.price}\r\n              onChange={(e) => handleInputChange(\"price\", e.target.value)}\r\n              className=\"mt-1\"\r\n              placeholder=\"$0.00\"\r\n            />\r\n          ) : (\r\n            <div className=\"mt-1 flex items-center gap-2\">\r\n              <span className=\"text-2xl font-bold text-green-600\">\r\n                {product.currency || \"EUR\"} {currentPrice.toFixed(2)}\r\n              </span>\r\n              {product.isOnSale && (\r\n                <Badge className=\"bg-red-500 text-white\">-{discount}%</Badge>\r\n              )}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Original Price */}\r\n        <div>\r\n          <Label htmlFor=\"original-price\">Original Price</Label>\r\n          {isEditing ? (\r\n            <Input\r\n              id=\"original-price\"\r\n              value={editedProduct.originalPrice || \"\"}\r\n              onChange={(e) =>\r\n                handleInputChange(\"originalPrice\", e.target.value)\r\n              }\r\n              className=\"mt-1\"\r\n              placeholder=\"$0.00 (optional)\"\r\n            />\r\n          ) : (\r\n            <div className=\"mt-1\">\r\n              {product.originalPrice ? (\r\n                <span className=\"text-gray-500 line-through\">\r\n                  {product.currency || \"EUR\"} {originalPrice?.toFixed(2)}\r\n                </span>\r\n              ) : (\r\n                <span className=\"text-gray-400\">No original price set</span>\r\n              )}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Sale Toggle */}\r\n        {isEditing && (\r\n          <div className=\"flex items-center justify-between\">\r\n            <Label htmlFor=\"on-sale\">On Sale</Label>\r\n            <Switch\r\n              id=\"on-sale\"\r\n              checked={editedProduct.isOnSale || false}\r\n              onCheckedChange={(checked) =>\r\n                handleInputChange(\"isOnSale\", checked)\r\n              }\r\n            />\r\n          </div>\r\n        )}\r\n\r\n        {/* Price Analysis */}\r\n        {!isEditing && (\r\n          <div className=\"space-y-2 rounded-md bg-gray-50 p-3\">\r\n            <h4 className=\"text-sm font-medium\">Price Analysis</h4>\r\n            <div className=\"space-y-1 text-sm\">\r\n              {product.isOnSale && originalPrice ? (\r\n                <div className=\"flex items-center gap-1 text-green-600\">\r\n                  <TrendingDown className=\"h-3 w-3\" />\r\n                  <span>\r\n                    Discounted by {product.currency || \"EUR\"}{\" \"}\r\n                    {(originalPrice - currentPrice).toFixed(2)}\r\n                  </span>\r\n                </div>\r\n              ) : (\r\n                <div className=\"flex items-center gap-1 text-gray-600\">\r\n                  <TrendingUp className=\"h-3 w-3\" />\r\n                  <span>Regular pricing</span>\r\n                </div>\r\n              )}\r\n              {product.costPrice && (\r\n                <div className=\"text-gray-500\">\r\n                  Cost: {product.currency || \"EUR\"}{\" \"}\r\n                  {product.costPrice.toFixed(2)}\r\n                </div>\r\n              )}\r\n              {product.profitMargin && (\r\n                <div className=\"text-gray-500\">\r\n                  Profit margin: {product.profitMargin.toFixed(1)}%\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;AAmBO,MAAM,wBAAwB,CAAC,EACpC,OAAO,EACP,SAAS,EACT,eAAe,EACK;IACpB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,oBAAoB,CAAC,OAAe;QACxC,iBAAiB,CAAC,OAAS,CAAC;gBAC1B,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,8CAA8C;IAC9C,MAAM,eACJ,OAAO,QAAQ,KAAK,KAAK,WACrB,WAAW,QAAQ,KAAK,CAAC,OAAO,CAAC,KAAK,OACtC,WAAW,QAAQ,KAAK;IAE9B,MAAM,gBAAgB,QAAQ,aAAa,GACvC,OAAO,QAAQ,aAAa,KAAK,WAC/B,WAAW,QAAQ,aAAa,CAAC,OAAO,CAAC,KAAK,OAC9C,WAAW,QAAQ,aAAa,IAClC;IAEJ,MAAM,WAAW,gBACb,KAAK,KAAK,CAAC,AAAC,CAAC,gBAAgB,YAAY,IAAI,gBAAiB,OAC9D;IAEJ,qBACE,6WAAC,yHAAA,CAAA,OAAI;;0BACH,6WAAC,yHAAA,CAAA,aAAU;0BACT,cAAA,6WAAC,yHAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,6WAAC,sSAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;wBAAY;;;;;;;;;;;;0BAItC,6WAAC,yHAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6WAAC;;0CACC,6WAAC,0HAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAgB;;;;;;4BAC9B,0BACC,6WAAC,0HAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,OAAO,cAAc,KAAK;gCAC1B,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;gCAC1D,WAAU;gCACV,aAAY;;;;;qDAGd,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAK,WAAU;;4CACb,QAAQ,QAAQ,IAAI;4CAAM;4CAAE,aAAa,OAAO,CAAC;;;;;;;oCAEnD,QAAQ,QAAQ,kBACf,6WAAC,0HAAA,CAAA,QAAK;wCAAC,WAAU;;4CAAwB;4CAAE;4CAAS;;;;;;;;;;;;;;;;;;;kCAO5D,6WAAC;;0CACC,6WAAC,0HAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAiB;;;;;;4BAC/B,0BACC,6WAAC,0HAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,OAAO,cAAc,aAAa,IAAI;gCACtC,UAAU,CAAC,IACT,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;gCAEnD,WAAU;gCACV,aAAY;;;;;qDAGd,6WAAC;gCAAI,WAAU;0CACZ,QAAQ,aAAa,iBACpB,6WAAC;oCAAK,WAAU;;wCACb,QAAQ,QAAQ,IAAI;wCAAM;wCAAE,eAAe,QAAQ;;;;;;yDAGtD,6WAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;;;;;;oBAOvC,2BACC,6WAAC;wBAAI,WAAU;;0CACb,6WAAC,0HAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAU;;;;;;0CACzB,6WAAC,2HAAA,CAAA,SAAM;gCACL,IAAG;gCACH,SAAS,cAAc,QAAQ,IAAI;gCACnC,iBAAiB,CAAC,UAChB,kBAAkB,YAAY;;;;;;;;;;;;oBAOrC,CAAC,2BACA,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAG,WAAU;0CAAsB;;;;;;0CACpC,6WAAC;gCAAI,WAAU;;oCACZ,QAAQ,QAAQ,IAAI,8BACnB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,0SAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;0DACxB,6WAAC;;oDAAK;oDACW,QAAQ,QAAQ,IAAI;oDAAO;oDACzC,CAAC,gBAAgB,YAAY,EAAE,OAAO,CAAC;;;;;;;;;;;;6DAI5C,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,sSAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6WAAC;0DAAK;;;;;;;;;;;;oCAGT,QAAQ,SAAS,kBAChB,6WAAC;wCAAI,WAAU;;4CAAgB;4CACtB,QAAQ,QAAQ,IAAI;4CAAO;4CACjC,QAAQ,SAAS,CAAC,OAAO,CAAC;;;;;;;oCAG9B,QAAQ,YAAY,kBACnB,6WAAC;wCAAI,WAAU;;4CAAgB;4CACb,QAAQ,YAAY,CAAC,OAAO,CAAC;4CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlE", "debugId": null}}, {"offset": {"line": 2995, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Select = SelectPrimitive.Root\r\n\r\nconst SelectGroup = SelectPrimitive.Group\r\n\r\nconst SelectValue = SelectPrimitive.Value\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n))\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronUp className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n))\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronDown className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n))\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ className, children, position = \"popper\", ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]\",\r\n        position === \"popper\" &&\r\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n        className\r\n      )}\r\n      position={position}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          \"p-1\",\r\n          position === \"popper\" &&\r\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n))\r\nSelectContent.displayName = SelectPrimitive.Content.displayName\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"px-2 py-1.5 text-sm font-semibold\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName\r\n\r\nconst SelectItem = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute right-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <SelectPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </SelectPrimitive.ItemIndicator>\r\n    </span>\r\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n  </SelectPrimitive.Item>\r\n))\r\nSelectItem.displayName = SelectPrimitive.Item.displayName\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\r\n\r\nexport {\r\n  Select,\r\n  SelectGroup,\r\n  SelectValue,\r\n  SelectTrigger,\r\n  SelectContent,\r\n  SelectLabel,\r\n  SelectItem,\r\n  SelectSeparator,\r\n  SelectScrollUpButton,\r\n  SelectScrollDownButton,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,+QAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,+QAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,+QAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6WAAC,+QAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kUACA;QAED,GAAG,KAAK;;YAER;0BACD,6WAAC,+QAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6WAAC,wSAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,+QAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6WAAC,oSAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,+QAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6WAAC,wSAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,+QAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,6WAAC,+QAAA,CAAA,SAAsB;kBACrB,cAAA,6WAAC,+QAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6WAAC;;;;;8BACD,6WAAC,+QAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6WAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,+QAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,+QAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6WAAC,+QAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,6WAAC;gBAAK,WAAU;0BACd,cAAA,6WAAC,+QAAA,CAAA,gBAA6B;8BAC5B,cAAA,6WAAC,wRAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGrB,6WAAC,+QAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,+QAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,+QAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3187, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/products/details/ProductStatusSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from \"react\";\r\n\r\nimport { Archive, Eye, EyeOff, Settings } from \"lucide-react\";\r\n\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { Switch } from \"@/components/ui/switch\";\r\nimport { Product } from \"@/types/product\";\r\n\r\ntype ProductSectionProps = {\r\n  product: Product;\r\n  isEditing: boolean;\r\n  onProductUpdate: (product: Product) => void;\r\n};\r\n\r\nexport const ProductStatusSection = ({\r\n  product,\r\n  isEditing,\r\n  onProductUpdate,\r\n}: ProductSectionProps) => {\r\n  const [editedProduct, setEditedProduct] = useState(product);\r\n\r\n  const handleStatusChange = (status: string) => {\r\n    setEditedProduct((prev) => ({\r\n      ...prev,\r\n      status: status as \"in-stock\" | \"draft\" | \"archived\",\r\n    }));\r\n  };\r\n\r\n  const handleVisibilityChange = (visible: boolean) => {\r\n    // This would control if the product is visible in the store\r\n    console.log(\"Visibility changed:\", visible);\r\n  };\r\n\r\n  const getStatusColor = (status: string) => {\r\n    switch (status) {\r\n      case \"in-stock\":\r\n        return \"bg-green-100 text-green-700\";\r\n      case \"draft\":\r\n        return \"bg-yellow-100 text-yellow-700\";\r\n      case \"archived\":\r\n        return \"bg-gray-100 text-gray-700\";\r\n      default:\r\n        return \"bg-gray-100 text-gray-700\";\r\n    }\r\n  };\r\n\r\n  const getStatusIcon = (status: string) => {\r\n    switch (status) {\r\n      case \"in-stock\":\r\n        return Eye;\r\n      case \"draft\":\r\n        return EyeOff;\r\n      case \"archived\":\r\n        return Archive;\r\n      default:\r\n        return Settings;\r\n    }\r\n  };\r\n\r\n  const status = product.status || \"active\";\r\n  const StatusIcon = getStatusIcon(status);\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle className=\"flex items-center gap-2\">\r\n          <Settings className=\"h-5 w-5\" />\r\n          Status & Visibility\r\n        </CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        {/* Product Status */}\r\n        <div>\r\n          <Label htmlFor=\"status\">Product Status</Label>\r\n          {isEditing ? (\r\n            <Select\r\n              value={editedProduct.status || \"in-stock\"}\r\n              onValueChange={handleStatusChange}\r\n            >\r\n              <SelectTrigger className=\"mt-1\">\r\n                <SelectValue />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                <SelectItem value=\"in-stock\">In Stock</SelectItem>\r\n                <SelectItem value=\"draft\">Draft</SelectItem>\r\n                <SelectItem value=\"archived\">Archived</SelectItem>\r\n              </SelectContent>\r\n            </Select>\r\n          ) : (\r\n            <div className=\"mt-1 flex items-center gap-2\">\r\n              <Badge className={getStatusColor(status)}>\r\n                <StatusIcon className=\"mr-1 h-3 w-3\" />\r\n                {status.charAt(0).toUpperCase() + status.slice(1)}\r\n              </Badge>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Visibility Toggle */}\r\n        <div className=\"flex items-center justify-between\">\r\n          <div>\r\n            <Label htmlFor=\"visibility\">Visible in Store</Label>\r\n            <p className=\"text-sm text-gray-500\">\r\n              Controls if customers can see this product\r\n            </p>\r\n          </div>\r\n          <Switch\r\n            id=\"visibility\"\r\n            checked={status === \"in-stock\"}\r\n            onCheckedChange={handleVisibilityChange}\r\n            disabled={!isEditing}\r\n          />\r\n        </div>\r\n\r\n        {/* Status Information */}\r\n        {!isEditing && (\r\n          <div className=\"space-y-2 rounded-md bg-gray-50 p-3\">\r\n            <h4 className=\"text-sm font-medium\">Status Information</h4>\r\n            <div className=\"space-y-1 text-sm text-gray-600\">\r\n              {status === \"in-stock\" && (\r\n                <>\r\n                  <p>• Product is live and visible to customers</p>\r\n                  <p>• Available for purchase</p>\r\n                  <p>• Included in search results</p>\r\n                </>\r\n              )}\r\n              {status === \"draft\" && (\r\n                <>\r\n                  <p>• Product is hidden from customers</p>\r\n                  <p>• Not available for purchase</p>\r\n                  <p>• Only visible to administrators</p>\r\n                </>\r\n              )}\r\n              {status === \"archived\" && (\r\n                <>\r\n                  <p>• Product is archived and hidden</p>\r\n                  <p>• Not available for purchase</p>\r\n                  <p>• Preserved for historical data</p>\r\n                </>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Created/Updated Info */}\r\n        {!isEditing && (\r\n          <div className=\"space-y-2 text-sm text-gray-500\">\r\n            <div>\r\n              <span className=\"font-medium\">Created:</span> Jan 15, 2024\r\n            </div>\r\n            <div>\r\n              <span className=\"font-medium\">Last Updated:</span> Jan 20, 2024\r\n            </div>\r\n            <div>\r\n              <span className=\"font-medium\">Views:</span> 1,234\r\n            </div>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AAOA;AAhBA;;;;;;;;;AAyBO,MAAM,uBAAuB,CAAC,EACnC,OAAO,EACP,SAAS,EACT,eAAe,EACK;IACpB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,qBAAqB,CAAC;QAC1B,iBAAiB,CAAC,OAAS,CAAC;gBAC1B,GAAG,IAAI;gBACP,QAAQ;YACV,CAAC;IACH;IAEA,MAAM,yBAAyB,CAAC;QAC9B,4DAA4D;QAC5D,QAAQ,GAAG,CAAC,uBAAuB;IACrC;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBA<PERSON>,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO,oRAAA,CAAA,MAAG;YACZ,KAAK;gBACH,OAAO,8RAAA,CAAA,SAAM;YACf,KAAK;gBACH,OAAO,4RAAA,CAAA,UAAO;YAChB;gBACE,OAAO,8RAAA,CAAA,WAAQ;QACnB;IACF;IAEA,MAAM,SAAS,QAAQ,MAAM,IAAI;IACjC,MAAM,aAAa,cAAc;IAEjC,qBACE,6WAAC,yHAAA,CAAA,OAAI;;0BACH,6WAAC,yHAAA,CAAA,aAAU;0BACT,cAAA,6WAAC,yHAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,6WAAC,8RAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAAY;;;;;;;;;;;;0BAIpC,6WAAC,yHAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6WAAC;;0CACC,6WAAC,0HAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAS;;;;;;4BACvB,0BACC,6WAAC,2HAAA,CAAA,SAAM;gCACL,OAAO,cAAc,MAAM,IAAI;gCAC/B,eAAe;;kDAEf,6WAAC,2HAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;;;;;;;;;;kDAEd,6WAAC,2HAAA,CAAA,gBAAa;;0DACZ,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAW;;;;;;0DAC7B,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAQ;;;;;;0DAC1B,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAW;;;;;;;;;;;;;;;;;qDAIjC,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC,0HAAA,CAAA,QAAK;oCAAC,WAAW,eAAe;;sDAC/B,6WAAC;4CAAW,WAAU;;;;;;wCACrB,OAAO,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,KAAK,CAAC;;;;;;;;;;;;;;;;;;kCAOvD,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;;kDACC,6WAAC,0HAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAa;;;;;;kDAC5B,6WAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAIvC,6WAAC,2HAAA,CAAA,SAAM;gCACL,IAAG;gCACH,SAAS,WAAW;gCACpB,iBAAiB;gCACjB,UAAU,CAAC;;;;;;;;;;;;oBAKd,CAAC,2BACA,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAG,WAAU;0CAAsB;;;;;;0CACpC,6WAAC;gCAAI,WAAU;;oCACZ,WAAW,4BACV;;0DACE,6WAAC;0DAAE;;;;;;0DACH,6WAAC;0DAAE;;;;;;0DACH,6WAAC;0DAAE;;;;;;;;oCAGN,WAAW,yBACV;;0DACE,6WAAC;0DAAE;;;;;;0DACH,6WAAC;0DAAE;;;;;;0DACH,6WAAC;0DAAE;;;;;;;;oCAGN,WAAW,4BACV;;0DACE,6WAAC;0DAAE;;;;;;0DACH,6WAAC;0DAAE;;;;;;0DACH,6WAAC;0DAAE;;;;;;;;;;;;;;;;;;;;oBAQZ,CAAC,2BACA,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;;kDACC,6WAAC;wCAAK,WAAU;kDAAc;;;;;;oCAAe;;;;;;;0CAE/C,6WAAC;;kDACC,6WAAC;wCAAK,WAAU;kDAAc;;;;;;oCAAoB;;;;;;;0CAEpD,6WAAC;;kDACC,6WAAC;wCAAK,WAAU;kDAAc;;;;;;oCAAa;;;;;;;;;;;;;;;;;;;;;;;;;AAOzD", "debugId": null}}, {"offset": {"line": 3592, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/products/details/ProductDetailsContent.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\n\r\nimport { Product } from \"@/types/product\";\r\n\r\nimport { ProductDetailsSection } from \"./ProductDetailsSection\";\r\nimport { ProductImageSection } from \"./ProductImageSection\";\r\nimport { ProductInfoSection } from \"./ProductInfoSection\";\r\nimport { ProductInventorySection } from \"./ProductInventorySection\";\r\nimport { ProductPricingSection } from \"./ProductPricingSection\";\r\nimport { ProductStatusSection } from \"./ProductStatusSection\";\r\n\r\ntype ProductDetailsContentProps = {\r\n  product: Product;\r\n  isEditing: boolean;\r\n  onProductUpdate: (product: Product) => void;\r\n  onEditToggle: () => void;\r\n  onSave: () => void;\r\n  onCancel: () => void;\r\n};\r\n\r\ntype ProductSectionProps = {\r\n  product: Product;\r\n  isEditing: boolean;\r\n  onProductUpdate: (product: Product) => void;\r\n};\r\n\r\nexport const ProductDetailsContent = ({\r\n  product,\r\n  isEditing,\r\n  onProductUpdate,\r\n  onEditToggle,\r\n  onSave,\r\n  onCancel,\r\n}: ProductDetailsContentProps) => {\r\n  return (\r\n    <div className=\"grid grid-cols-1 gap-6 lg:grid-cols-3\">\r\n      {/* Left Column - Product Image */}\r\n      <div className=\"lg:col-span-1\">\r\n        <ProductImageSection\r\n          product={product}\r\n          isEditing={isEditing}\r\n          onProductUpdate={onProductUpdate}\r\n        />\r\n      </div>\r\n\r\n      {/* Right Column - Product Information */}\r\n      <div className=\"space-y-6 lg:col-span-2\">\r\n        {/* Product Info */}\r\n        <ProductInfoSection\r\n          product={product}\r\n          isEditing={isEditing}\r\n          onProductUpdate={onProductUpdate}\r\n        />\r\n\r\n        {/* Grid for other sections */}\r\n        <div className=\"grid grid-cols-1 gap-6 md:grid-cols-2\">\r\n          {/* Pricing */}\r\n          <ProductPricingSection\r\n            product={product}\r\n            isEditing={isEditing}\r\n            onProductUpdate={onProductUpdate}\r\n          />\r\n\r\n          {/* Inventory */}\r\n          <ProductInventorySection\r\n            product={product}\r\n            isEditing={isEditing}\r\n            onProductUpdate={onProductUpdate}\r\n          />\r\n\r\n          {/* Status */}\r\n          <ProductStatusSection\r\n            product={product}\r\n            isEditing={isEditing}\r\n            onProductUpdate={onProductUpdate}\r\n          />\r\n\r\n          {/* Details */}\r\n          <ProductDetailsSection\r\n            product={product}\r\n            isEditing={isEditing}\r\n            onProductUpdate={onProductUpdate}\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAMA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;AA4BO,MAAM,wBAAwB,CAAC,EACpC,OAAO,EACP,SAAS,EACT,eAAe,EACf,YAAY,EACZ,MAAM,EACN,QAAQ,EACmB;IAC3B,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC,kKAAA,CAAA,sBAAmB;oBAClB,SAAS;oBACT,WAAW;oBACX,iBAAiB;;;;;;;;;;;0BAKrB,6WAAC;gBAAI,WAAU;;kCAEb,6WAAC,iKAAA,CAAA,qBAAkB;wBACjB,SAAS;wBACT,WAAW;wBACX,iBAAiB;;;;;;kCAInB,6WAAC;wBAAI,WAAU;;0CAEb,6WAAC,oKAAA,CAAA,wBAAqB;gCACpB,SAAS;gCACT,WAAW;gCACX,iBAAiB;;;;;;0CAInB,6WAAC,sKAAA,CAAA,0BAAuB;gCACtB,SAAS;gCACT,WAAW;gCACX,iBAAiB;;;;;;0CAInB,6WAAC,mKAAA,CAAA,uBAAoB;gCACnB,SAAS;gCACT,WAAW;gCACX,iBAAiB;;;;;;0CAInB,6WAAC,oKAAA,CAAA,wBAAqB;gCACpB,SAAS;gCACT,WAAW;gCACX,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;AAM7B", "debugId": null}}, {"offset": {"line": 3706, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/products/details/ProductDetailsWrapper.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useState } from \"react\";\r\n\r\nimport { AlertCircle, RefreshCw } from \"lucide-react\";\r\nimport { useSearchParams } from \"next/navigation\";\r\nimport { toast } from \"sonner\";\r\n\r\nimport { Alert, AlertDescription } from \"@/components/ui/alert\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport { useProduct, useProductMutations } from \"@/hooks/useProducts\";\r\n\r\nimport { ProductDetailsActions } from \"./ProductDetailsActions\";\r\nimport { ProductDetailsContent } from \"./ProductDetailsContent\";\r\n\r\ntype ProductDetailsWrapperProps = {\r\n  productId: string;\r\n};\r\n\r\nexport const ProductDetailsWrapper = ({\r\n  productId,\r\n}: ProductDetailsWrapperProps) => {\r\n  const searchParams = useSearchParams();\r\n  const [isEditing, setIsEditing] = useState(false);\r\n  const [localProduct, setLocalProduct] = useState(null);\r\n\r\n  // Fetch product data from API\r\n  const { product, loading, error, refetch } = useProduct(productId);\r\n  const { updateProduct, loading: updating } = useProductMutations();\r\n\r\n  // Check for edit mode in URL parameters\r\n  useEffect(() => {\r\n    const editMode = searchParams.get(\"edit\") === \"true\";\r\n    setIsEditing(editMode);\r\n  }, [searchParams]);\r\n\r\n  // Update local product state when API data changes\r\n  useEffect(() => {\r\n    if (product) {\r\n      setLocalProduct(product);\r\n    }\r\n  }, [product]);\r\n\r\n  const handleEditToggle = () => {\r\n    setIsEditing(!isEditing);\r\n  };\r\n\r\n  const handleSave = async () => {\r\n    if (!localProduct || !productId) return;\r\n\r\n    try {\r\n      await updateProduct(productId, localProduct);\r\n      setIsEditing(false);\r\n      // Refetch to get updated data\r\n      refetch();\r\n    } catch (error) {\r\n      console.error(\"Failed to save product:\", error);\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    // Reset to original product data\r\n    setLocalProduct(product);\r\n    setIsEditing(false);\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"space-y-6\">\r\n        <div className=\"rounded-lg border bg-white p-4\">\r\n          <Skeleton className=\"mb-2 h-6 w-1/3\" />\r\n          <Skeleton className=\"h-4 w-1/4\" />\r\n        </div>\r\n        <div className=\"grid grid-cols-1 gap-6 md:grid-cols-2\">\r\n          {Array.from({ length: 4 }).map((_, index) => (\r\n            <div key={index} className=\"rounded-lg border bg-white p-6\">\r\n              <Skeleton className=\"mb-4 h-5 w-1/4\" />\r\n              <div className=\"space-y-3\">\r\n                <Skeleton className=\"h-4 w-full\" />\r\n                <Skeleton className=\"h-4 w-3/4\" />\r\n                <Skeleton className=\"h-4 w-1/2\" />\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <Alert className=\"mx-auto max-w-md\">\r\n        <AlertCircle className=\"h-4 w-4\" />\r\n        <AlertDescription className=\"flex items-center justify-between\">\r\n          <span>Failed to load product: {error}</span>\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            onClick={refetch}\r\n            className=\"ml-2\"\r\n          >\r\n            <RefreshCw className=\"mr-1 h-4 w-4\" />\r\n            Retry\r\n          </Button>\r\n        </AlertDescription>\r\n      </Alert>\r\n    );\r\n  }\r\n\r\n  if (!product) {\r\n    return (\r\n      <div className=\"flex items-center justify-center py-12\">\r\n        <div className=\"text-center\">\r\n          <h3 className=\"text-lg font-medium text-gray-900\">\r\n            Product not found\r\n          </h3>\r\n          <p className=\"text-gray-500\">\r\n            The product you're looking for doesn't exist.\r\n          </p>\r\n          <Button variant=\"outline\" onClick={refetch} className=\"mt-4\">\r\n            <RefreshCw className=\"mr-2 h-4 w-4\" />\r\n            Try Again\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Action Buttons */}\r\n      <div className=\"flex items-center justify-between rounded-lg border bg-white p-4\">\r\n        <div>\r\n          <h2 className=\"text-lg font-semibold text-gray-900\">\r\n            {localProduct?.name || product.name}\r\n          </h2>\r\n          <p className=\"text-sm text-gray-500\">\r\n            Product ID: #\r\n            {(product._id || product.id || \"\")\r\n              .toString()\r\n              .slice(-6)\r\n              .padStart(6, \"0\")}\r\n          </p>\r\n        </div>\r\n        <ProductDetailsActions\r\n          productId={productId}\r\n          isEditing={isEditing}\r\n          onEditToggle={handleEditToggle}\r\n          onSave={handleSave}\r\n          onCancel={handleCancel}\r\n          loading={updating}\r\n        />\r\n      </div>\r\n\r\n      {/* Product Content */}\r\n      <ProductDetailsContent\r\n        product={localProduct || product}\r\n        isEditing={isEditing}\r\n        onProductUpdate={setLocalProduct}\r\n        onEditToggle={handleEditToggle}\r\n        onSave={handleSave}\r\n        onCancel={handleCancel}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AACA;AAGA;AACA;AACA;AACA;AAEA;AACA;AAdA;;;;;;;;;;;AAoBO,MAAM,wBAAwB,CAAC,EACpC,SAAS,EACkB;IAC3B,MAAM,eAAe,CAAA,GAAA,iQAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,8BAA8B;IAC9B,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,oHAAA,CAAA,aAAU,AAAD,EAAE;IACxD,MAAM,EAAE,aAAa,EAAE,SAAS,QAAQ,EAAE,GAAG,CAAA,GAAA,oHAAA,CAAA,sBAAmB,AAAD;IAE/D,wCAAwC;IACxC,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,aAAa,GAAG,CAAC,YAAY;QAC9C,aAAa;IACf,GAAG;QAAC;KAAa;IAEjB,mDAAmD;IACnD,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS;YACX,gBAAgB;QAClB;IACF,GAAG;QAAC;KAAQ;IAEZ,MAAM,mBAAmB;QACvB,aAAa,CAAC;IAChB;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,gBAAgB,CAAC,WAAW;QAEjC,IAAI;YACF,MAAM,cAAc,WAAW;YAC/B,aAAa;YACb,8BAA8B;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,eAAe;QACnB,iCAAiC;QACjC,gBAAgB;QAChB,aAAa;IACf;IAEA,IAAI,SAAS;QACX,qBACE,6WAAC;YAAI,WAAU;;8BACb,6WAAC;oBAAI,WAAU;;sCACb,6WAAC,6HAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,6WAAC,6HAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;8BAEtB,6WAAC;oBAAI,WAAU;8BACZ,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6WAAC;4BAAgB,WAAU;;8CACzB,6WAAC,6HAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6WAAC;oCAAI,WAAU;;sDACb,6WAAC,6HAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6WAAC,6HAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6WAAC,6HAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;2BALd;;;;;;;;;;;;;;;;IAYpB;IAEA,IAAI,OAAO;QACT,qBACE,6WAAC,0HAAA,CAAA,QAAK;YAAC,WAAU;;8BACf,6WAAC,wSAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,6WAAC,0HAAA,CAAA,mBAAgB;oBAAC,WAAU;;sCAC1B,6WAAC;;gCAAK;gCAAyB;;;;;;;sCAC/B,6WAAC,2HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;;8CAEV,6WAAC,oSAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;IAMhD;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,6WAAC;YAAI,WAAU;sBACb,cAAA,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAG,WAAU;kCAAoC;;;;;;kCAGlD,6WAAC;wBAAE,WAAU;kCAAgB;;;;;;kCAG7B,6WAAC,2HAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,SAAS;wBAAS,WAAU;;0CACpD,6WAAC,oSAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;IAMhD;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;;0CACC,6WAAC;gCAAG,WAAU;0CACX,cAAc,QAAQ,QAAQ,IAAI;;;;;;0CAErC,6WAAC;gCAAE,WAAU;;oCAAwB;oCAElC,CAAC,QAAQ,GAAG,IAAI,QAAQ,EAAE,IAAI,EAAE,EAC9B,QAAQ,GACR,KAAK,CAAC,CAAC,GACP,QAAQ,CAAC,GAAG;;;;;;;;;;;;;kCAGnB,6WAAC,oKAAA,CAAA,wBAAqB;wBACpB,WAAW;wBACX,WAAW;wBACX,cAAc;wBACd,QAAQ;wBACR,UAAU;wBACV,SAAS;;;;;;;;;;;;0BAKb,6WAAC,oKAAA,CAAA,wBAAqB;gBACpB,SAAS,gBAAgB;gBACzB,WAAW;gBACX,iBAAiB;gBACjB,cAAc;gBACd,QAAQ;gBACR,UAAU;;;;;;;;;;;;AAIlB", "debugId": null}}]}