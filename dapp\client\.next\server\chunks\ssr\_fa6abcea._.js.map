{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/lib/api/brands.ts"], "sourcesContent": ["// Import consolidated types\nimport { ApiResponse } from \"@/types/api\";\nimport {\n  Brand,\n  BrandFilters,\n  BrandsResponse,\n  CreateBrandDto,\n  UpdateBrandDto,\n} from \"@/types/brand\";\n\n// Get API base URL from environment or default to localhost\nconst getApiBaseUrl = (): string => {\n  if (typeof window !== \"undefined\") {\n    // Client-side: use current origin or environment variable\n    return (\n      process.env.NEXT_PUBLIC_API_URL ||\n      `${window.location.protocol}//${window.location.hostname}:3011`\n    );\n  }\n  // Server-side: use environment variable or default\n  return process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3011\";\n};\n\nconst API_BASE_URL = getApiBaseUrl();\n\n// Re-export types for backward compatibility\nexport type {\n  Brand,\n  CreateBrandDto,\n  UpdateBrandDto,\n  BrandFilters,\n  BrandsResponse,\n};\n\n// BrandFilters is imported from consolidated types\n\n/**\n * API service for brand management\n */\nexport class BrandApi {\n  private static readonly BASE_URL = `${API_BASE_URL}/api/brands`;\n\n  /**\n   * Get all brands with optional filtering\n   */\n  static async getBrands(\n    filters?: BrandFilters\n  ): Promise<ApiResponse<Brand[]>> {\n    try {\n      const params = new URLSearchParams();\n\n      if (filters?.isActive !== undefined) {\n        params.append(\"isActive\", filters.isActive.toString());\n      }\n\n      if (filters?.search) {\n        params.append(\"search\", filters.search);\n      }\n\n      const queryString = params.toString();\n      const url = queryString\n        ? `${this.BASE_URL}?${queryString}`\n        : this.BASE_URL;\n\n      const response = await fetch(url);\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.error(\"Error fetching brands:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get a brand by ID\n   */\n  static async getBrandById(id: string): Promise<ApiResponse<Brand>> {\n    try {\n      const response = await fetch(`${this.BASE_URL}/${id}`);\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.error(\"Error fetching brand:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get a brand by slug\n   */\n  static async getBrandBySlug(slug: string): Promise<ApiResponse<Brand>> {\n    try {\n      const response = await fetch(`${this.BASE_URL}/slug/${slug}`);\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.error(\"Error fetching brand by slug:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Create a new brand\n   */\n  static async createBrand(\n    brandData: CreateBrandDto\n  ): Promise<ApiResponse<Brand>> {\n    try {\n      const response = await fetch(this.BASE_URL, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(brandData),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(\n          errorData.message || `HTTP error! status: ${response.status}`\n        );\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.error(\"Error creating brand:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Update a brand\n   */\n  static async updateBrand(\n    id: string,\n    updateData: UpdateBrandDto\n  ): Promise<ApiResponse<Brand>> {\n    try {\n      const response = await fetch(`${this.BASE_URL}/${id}`, {\n        method: \"PUT\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(updateData),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(\n          errorData.message || `HTTP error! status: ${response.status}`\n        );\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.error(\"Error updating brand:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Delete a brand\n   */\n  static async deleteBrand(id: string): Promise<ApiResponse<null>> {\n    try {\n      const response = await fetch(`${this.BASE_URL}/${id}`, {\n        method: \"DELETE\",\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(\n          errorData.message || `HTTP error! status: ${response.status}`\n        );\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.error(\"Error deleting brand:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Recalculate product counts for all brands\n   */\n  static async recalculateProductCounts(): Promise<ApiResponse<null>> {\n    try {\n      const response = await fetch(`${this.BASE_URL}/recalculate-counts`, {\n        method: \"POST\",\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(\n          errorData.message || `HTTP error! status: ${response.status}`\n        );\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.error(\"Error recalculating brand product counts:\", error);\n      throw error;\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA,4BAA4B;;;;AAU5B,4DAA4D;AAC5D,MAAM,gBAAgB;IACpB,uCAAmC;;IAMnC;IACA,mDAAmD;IACnD,OAAO,6DAAmC;AAC5C;AAEA,MAAM,eAAe;AAgBd,MAAM;IACX,OAAwB,WAAW,GAAG,aAAa,WAAW,CAAC,CAAC;IAEhE;;GAEC,GACD,aAAa,UACX,OAAsB,EACS;QAC/B,IAAI;YACF,MAAM,SAAS,IAAI;YAEnB,IAAI,SAAS,aAAa,WAAW;gBACnC,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ,CAAC,QAAQ;YACrD;YAEA,IAAI,SAAS,QAAQ;gBACnB,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;YACxC;YAEA,MAAM,cAAc,OAAO,QAAQ;YACnC,MAAM,MAAM,cACR,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,aAAa,GACjC,IAAI,CAAC,QAAQ;YAEjB,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,aAAa,EAAU,EAA+B;QACjE,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI;YAErD,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,eAAe,IAAY,EAA+B;QACrE,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM;YAE5D,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,YACX,SAAyB,EACI;QAC7B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,QAAQ,EAAE;gBAC1C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MACR,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAEjE;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,YACX,EAAU,EACV,UAA0B,EACG;QAC7B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,EAAE;gBACrD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MACR,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAEjE;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,YAAY,EAAU,EAA8B;QAC/D,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,EAAE;gBACrD,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MACR,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAEjE;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,2BAAuD;QAClE,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;gBAClE,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MACR,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAEjE;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 163, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/lib/api/colors.ts"], "sourcesContent": ["// Import consolidated types\nimport { ApiResponse } from \"@/types/api\";\nimport {\n  Color,\n  ColorFilters,\n  ColorsResponse,\n  CreateColorDto,\n  UpdateColorDto,\n} from \"@/types/color\";\nimport { ColorFamily } from \"@/types/common\";\n\n// Get API base URL from environment or default to localhost\nconst getApiBaseUrl = (): string => {\n  if (typeof window !== \"undefined\") {\n    // Client-side: use current origin or environment variable\n    return (\n      process.env.NEXT_PUBLIC_API_URL ||\n      `${window.location.protocol}//${window.location.hostname}:3011`\n    );\n  }\n  // Server-side: use environment variable or default\n  return process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3011\";\n};\n\nconst API_BASE_URL = getApiBaseUrl();\n\n// Re-export types for backward compatibility\nexport type {\n  Color,\n  CreateColorDto,\n  UpdateColorDto,\n  ColorFilters,\n  ColorsResponse,\n  ColorFamily,\n};\n\n// All types are imported from consolidated type files\n\n/**\n * API service for color management\n */\nexport class ColorApi {\n  private static readonly BASE_URL = `${API_BASE_URL}/api/colors`;\n\n  /**\n   * Get all colors with optional filtering\n   */\n  static async getColors(\n    filters?: ColorFilters\n  ): Promise<ApiResponse<Color[]>> {\n    try {\n      const params = new URLSearchParams();\n\n      if (filters?.isActive !== undefined) {\n        params.append(\"isActive\", filters.isActive.toString());\n      }\n\n      if (filters?.search) {\n        params.append(\"search\", filters.search);\n      }\n\n      if (filters?.colorFamily) {\n        params.append(\"colorFamily\", filters.colorFamily);\n      }\n\n      if (filters?.hexValue) {\n        params.append(\"hexValue\", filters.hexValue);\n      }\n\n      const queryString = params.toString();\n      const url = queryString\n        ? `${this.BASE_URL}?${queryString}`\n        : this.BASE_URL;\n\n      const response = await fetch(url);\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.error(\"Error fetching colors:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get a color by ID\n   */\n  static async getColorById(id: string): Promise<ApiResponse<Color>> {\n    try {\n      const response = await fetch(`${this.BASE_URL}/${id}`);\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.error(\"Error fetching color:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get a color by slug\n   */\n  static async getColorBySlug(slug: string): Promise<ApiResponse<Color>> {\n    try {\n      const response = await fetch(`${this.BASE_URL}/slug/${slug}`);\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.error(\"Error fetching color by slug:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get a color by hex value\n   */\n  static async getColorByHex(hex: string): Promise<ApiResponse<Color>> {\n    try {\n      // Remove # if present for URL\n      const hexValue = hex.replace(\"#\", \"\");\n      const response = await fetch(`${this.BASE_URL}/hex/${hexValue}`);\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.error(\"Error fetching color by hex:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get colors grouped by color family\n   */\n  static async getColorsByFamily(): Promise<\n    ApiResponse<Record<ColorFamily, Color[]>>\n  > {\n    try {\n      const response = await fetch(`${this.BASE_URL}/families`);\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.error(\"Error fetching colors by family:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Create a new color\n   */\n  static async createColor(\n    colorData: CreateColorDto\n  ): Promise<ApiResponse<Color>> {\n    try {\n      const response = await fetch(this.BASE_URL, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(colorData),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(\n          errorData.message || `HTTP error! status: ${response.status}`\n        );\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.error(\"Error creating color:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Update a color\n   */\n  static async updateColor(\n    id: string,\n    updateData: UpdateColorDto\n  ): Promise<ApiResponse<Color>> {\n    try {\n      const response = await fetch(`${this.BASE_URL}/${id}`, {\n        method: \"PUT\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(updateData),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(\n          errorData.message || `HTTP error! status: ${response.status}`\n        );\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.error(\"Error updating color:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Delete a color\n   */\n  static async deleteColor(id: string): Promise<ApiResponse<null>> {\n    try {\n      const response = await fetch(`${this.BASE_URL}/${id}`, {\n        method: \"DELETE\",\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(\n          errorData.message || `HTTP error! status: ${response.status}`\n        );\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.error(\"Error deleting color:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Recalculate product counts for all colors\n   */\n  static async recalculateProductCounts(): Promise<ApiResponse<null>> {\n    try {\n      const response = await fetch(`${this.BASE_URL}/recalculate-counts`, {\n        method: \"POST\",\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(\n          errorData.message || `HTTP error! status: ${response.status}`\n        );\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.error(\"Error recalculating color product counts:\", error);\n      throw error;\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA,4BAA4B;;;;AAW5B,4DAA4D;AAC5D,MAAM,gBAAgB;IACpB,uCAAmC;;IAMnC;IACA,mDAAmD;IACnD,OAAO,6DAAmC;AAC5C;AAEA,MAAM,eAAe;AAiBd,MAAM;IACX,OAAwB,WAAW,GAAG,aAAa,WAAW,CAAC,CAAC;IAEhE;;GAEC,GACD,aAAa,UACX,OAAsB,EACS;QAC/B,IAAI;YACF,MAAM,SAAS,IAAI;YAEnB,IAAI,SAAS,aAAa,WAAW;gBACnC,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ,CAAC,QAAQ;YACrD;YAEA,IAAI,SAAS,QAAQ;gBACnB,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;YACxC;YAEA,IAAI,SAAS,aAAa;gBACxB,OAAO,MAAM,CAAC,eAAe,QAAQ,WAAW;YAClD;YAEA,IAAI,SAAS,UAAU;gBACrB,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ;YAC5C;YAEA,MAAM,cAAc,OAAO,QAAQ;YACnC,MAAM,MAAM,cACR,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,aAAa,GACjC,IAAI,CAAC,QAAQ;YAEjB,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,aAAa,EAAU,EAA+B;QACjE,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI;YAErD,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,eAAe,IAAY,EAA+B;QACrE,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM;YAE5D,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,cAAc,GAAW,EAA+B;QACnE,IAAI;YACF,8BAA8B;YAC9B,MAAM,WAAW,IAAI,OAAO,CAAC,KAAK;YAClC,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU;YAE/D,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,oBAEX;QACA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;YAExD,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,YACX,SAAyB,EACI;QAC7B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,QAAQ,EAAE;gBAC1C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MACR,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAEjE;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,YACX,EAAU,EACV,UAA0B,EACG;QAC7B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,EAAE;gBACrD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MACR,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAEjE;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,YAAY,EAAU,EAA8B;QAC/D,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,EAAE;gBACrD,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MACR,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAEjE;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,2BAAuD;QAClE,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;gBAClE,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MACR,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAEjE;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 357, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/lib/api/categoryApi.ts"], "sourcesContent": ["import { ApiResponse } from \"@/types/api\";\nimport {\n  Category,\n  CategoryFilters,\n  CreateCategoryDto,\n  UpdateCategoryDto,\n} from \"@/types/category\";\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3011\";\n\n/**\n * Category API service for frontend-backend communication\n */\nexport class CategoryApiService {\n  private static baseUrl = `${API_BASE_URL}/api/categories`;\n\n  /**\n   * Transform backend category to frontend format\n   */\n  private static transformCategory(\n    backendCategory: Record<string, unknown>\n  ): Category {\n    console.log(\"Transforming category:\", backendCategory); // Debug log\n    const transformed: Category = {\n      _id: (backendCategory._id || backendCategory.id) as string,\n      name: backendCategory.name as string,\n      description: backendCategory.description as string,\n      slug: backendCategory.slug as string,\n      icon: backendCategory.icon as string,\n      color: backendCategory.color as string,\n      isActive: backendCategory.isActive as boolean,\n      productCount: backendCategory.productCount as number,\n      parentId: backendCategory.parentId as string,\n      sortOrder: backendCategory.sortOrder as number,\n      createdAt: backendCategory.createdAt as string,\n      updatedAt: backendCategory.updatedAt as string,\n    };\n    console.log(\"Transformed category:\", transformed); // Debug log\n    return transformed;\n  }\n\n  /**\n   * Get all categories with optional filtering\n   */\n  static async getCategories(filters?: CategoryFilters): Promise<Category[]> {\n    try {\n      const params = new URLSearchParams();\n\n      if (filters?.isActive !== undefined) {\n        params.append(\"isActive\", filters.isActive.toString());\n      }\n      if (filters?.parentId) {\n        params.append(\"parentId\", filters.parentId);\n      }\n      if (filters?.search) {\n        params.append(\"search\", filters.search);\n      }\n\n      const url = `${this.baseUrl}${params.toString() ? `?${params.toString()}` : \"\"}`;\n      const response = await fetch(url);\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result: ApiResponse<Record<string, unknown>[]> =\n        await response.json();\n\n      if (!result.success) {\n        throw new Error(result.message || \"Failed to fetch categories\");\n      }\n\n      const categories = (result.data || []).map(this.transformCategory);\n      return categories;\n    } catch (error) {\n      console.error(\"Error fetching categories:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get a category by ID\n   */\n  static async getCategoryById(id: string): Promise<Category> {\n    try {\n      const response = await fetch(`${this.baseUrl}/${id}`);\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result: ApiResponse<any> = await response.json();\n\n      if (!result.success) {\n        throw new Error(result.message || \"Failed to fetch category\");\n      }\n\n      if (!result.data) {\n        throw new Error(\"Category not found\");\n      }\n\n      return this.transformCategory(result.data);\n    } catch (error) {\n      console.error(\"Error fetching category:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get a category by slug\n   */\n  static async getCategoryBySlug(slug: string): Promise<Category> {\n    try {\n      const response = await fetch(`${this.baseUrl}/slug/${slug}`);\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result: ApiResponse<any> = await response.json();\n\n      if (!result.success) {\n        throw new Error(result.message || \"Failed to fetch category\");\n      }\n\n      if (!result.data) {\n        throw new Error(\"Category not found\");\n      }\n\n      return this.transformCategory(result.data);\n    } catch (error) {\n      console.error(\"Error fetching category:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Create a new category\n   */\n  static async createCategory(\n    categoryData: CreateCategoryDto\n  ): Promise<Category> {\n    try {\n      const response = await fetch(this.baseUrl, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(categoryData),\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result: ApiResponse<any> = await response.json();\n\n      if (!result.success) {\n        throw new Error(result.message || \"Failed to create category\");\n      }\n\n      if (!result.data) {\n        throw new Error(\"No category data returned\");\n      }\n\n      return this.transformCategory(result.data);\n    } catch (error) {\n      console.error(\"Error creating category:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Update a category\n   */\n  static async updateCategory(\n    id: string,\n    updateData: UpdateCategoryDto\n  ): Promise<Category> {\n    try {\n      const response = await fetch(`${this.baseUrl}/${id}`, {\n        method: \"PUT\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(updateData),\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result: ApiResponse<any> = await response.json();\n\n      if (!result.success) {\n        throw new Error(result.message || \"Failed to update category\");\n      }\n\n      if (!result.data) {\n        throw new Error(\"No category data returned\");\n      }\n\n      return this.transformCategory(result.data);\n    } catch (error) {\n      console.error(\"Error updating category:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Delete a category\n   */\n  static async deleteCategory(id: string): Promise<void> {\n    try {\n      console.log(\"Deleting category with ID:\", id); // Debug log\n      if (!id || id === \"undefined\") {\n        throw new Error(\"Category ID is required for deletion\");\n      }\n\n      const response = await fetch(`${this.baseUrl}/${id}`, {\n        method: \"DELETE\",\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result: ApiResponse<null> = await response.json();\n\n      if (!result.success) {\n        throw new Error(result.message || \"Failed to delete category\");\n      }\n    } catch (error) {\n      console.error(\"Error deleting category:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Recalculate product counts for all categories\n   */\n  static async recalculateProductCounts(): Promise<void> {\n    try {\n      const response = await fetch(`${this.baseUrl}/recalculate-counts`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result: ApiResponse<any> = await response.json();\n\n      if (!result.success) {\n        throw new Error(\n          result.message || \"Failed to recalculate product counts\"\n        );\n      }\n    } catch (error) {\n      console.error(\"Error recalculating product counts:\", error);\n      throw error;\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAQA,MAAM,eAAe,6DAAmC;AAKjD,MAAM;IACX,OAAe,UAAU,GAAG,aAAa,eAAe,CAAC,CAAC;IAE1D;;GAEC,GACD,OAAe,kBACb,eAAwC,EAC9B;QACV,QAAQ,GAAG,CAAC,0BAA0B,kBAAkB,YAAY;QACpE,MAAM,cAAwB;YAC5B,KAAM,gBAAgB,GAAG,IAAI,gBAAgB,EAAE;YAC/C,MAAM,gBAAgB,IAAI;YAC1B,aAAa,gBAAgB,WAAW;YACxC,MAAM,gBAAgB,IAAI;YAC1B,MAAM,gBAAgB,IAAI;YAC1B,OAAO,gBAAgB,KAAK;YAC5B,UAAU,gBAAgB,QAAQ;YAClC,cAAc,gBAAgB,YAAY;YAC1C,UAAU,gBAAgB,QAAQ;YAClC,WAAW,gBAAgB,SAAS;YACpC,WAAW,gBAAgB,SAAS;YACpC,WAAW,gBAAgB,SAAS;QACtC;QACA,QAAQ,GAAG,CAAC,yBAAyB,cAAc,YAAY;QAC/D,OAAO;IACT;IAEA;;GAEC,GACD,aAAa,cAAc,OAAyB,EAAuB;QACzE,IAAI;YACF,MAAM,SAAS,IAAI;YAEnB,IAAI,SAAS,aAAa,WAAW;gBACnC,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ,CAAC,QAAQ;YACrD;YACA,IAAI,SAAS,UAAU;gBACrB,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ;YAC5C;YACA,IAAI,SAAS,QAAQ;gBACnB,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;YACxC;YAEA,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,OAAO,QAAQ,KAAK,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI,GAAG,IAAI;YAChF,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SACJ,MAAM,SAAS,IAAI;YAErB,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;YAEA,MAAM,aAAa,CAAC,OAAO,IAAI,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,iBAAiB;YACjE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,gBAAgB,EAAU,EAAqB;QAC1D,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI;YAEpD,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAA2B,MAAM,SAAS,IAAI;YAEpD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;YAEA,IAAI,CAAC,OAAO,IAAI,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,IAAI;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,kBAAkB,IAAY,EAAqB;QAC9D,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM;YAE3D,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAA2B,MAAM,SAAS,IAAI;YAEpD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;YAEA,IAAI,CAAC,OAAO,IAAI,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,IAAI;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,eACX,YAA+B,EACZ;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;gBACzC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAA2B,MAAM,SAAS,IAAI;YAEpD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;YAEA,IAAI,CAAC,OAAO,IAAI,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,IAAI;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,eACX,EAAU,EACV,UAA6B,EACV;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAA2B,MAAM,SAAS,IAAI;YAEpD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;YAEA,IAAI,CAAC,OAAO,IAAI,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,IAAI;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,eAAe,EAAU,EAAiB;QACrD,IAAI;YACF,QAAQ,GAAG,CAAC,8BAA8B,KAAK,YAAY;YAC3D,IAAI,CAAC,MAAM,OAAO,aAAa;gBAC7B,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE;gBACpD,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAA4B,MAAM,SAAS,IAAI;YAErD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,2BAA0C;QACrD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAAE;gBACjE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAA2B,MAAM,SAAS,IAAI;YAEpD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MACR,OAAO,OAAO,IAAI;YAEtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 562, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/lib/api/materials.ts"], "sourcesContent": ["// Import consolidated types\nimport { ApiResponse } from \"@/types/api\";\nimport {\n  CreateMaterialDto,\n  Material,\n  MaterialFilters,\n  MaterialsResponse,\n  UpdateMaterialDto,\n} from \"@/types/material\";\n\n// Get API base URL from environment or default to localhost\nconst getApiBaseUrl = (): string => {\n  if (typeof window !== \"undefined\") {\n    // Client-side: use current origin or environment variable\n    return (\n      process.env.NEXT_PUBLIC_API_URL ||\n      `${window.location.protocol}//${window.location.hostname}:3011`\n    );\n  }\n  // Server-side: use environment variable or default\n  return process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3011\";\n};\n\nconst API_BASE_URL = getApiBaseUrl();\n\n// Re-export types for backward compatibility\nexport type {\n  Material,\n  CreateMaterialDto,\n  UpdateMaterialDto,\n  MaterialFilters,\n  MaterialsResponse,\n};\n\n// All types are imported from consolidated type files\n\n/**\n * API service for material management\n */\nexport class MaterialApi {\n  private static readonly BASE_URL = `${API_BASE_URL}/api/materials`;\n\n  /**\n   * Get all materials with optional filtering\n   */\n  static async getMaterials(\n    filters?: MaterialFilters\n  ): Promise<ApiResponse<Material[]>> {\n    try {\n      const params = new URLSearchParams();\n\n      if (filters?.isActive !== undefined) {\n        params.append(\"isActive\", filters.isActive.toString());\n      }\n\n      if (filters?.search) {\n        params.append(\"search\", filters.search);\n      }\n\n      if (filters?.durability) {\n        params.append(\"durability\", filters.durability);\n      }\n\n      if (filters?.waterResistant !== undefined) {\n        params.append(\"waterResistant\", filters.waterResistant.toString());\n      }\n\n      if (filters?.recyclable !== undefined) {\n        params.append(\"recyclable\", filters.recyclable.toString());\n      }\n\n      if (filters?.weight) {\n        params.append(\"weight\", filters.weight);\n      }\n\n      const queryString = params.toString();\n      const url = queryString\n        ? `${this.BASE_URL}?${queryString}`\n        : this.BASE_URL;\n\n      const response = await fetch(url);\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.error(\"Error fetching materials:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get a material by ID\n   */\n  static async getMaterialById(id: string): Promise<ApiResponse<Material>> {\n    try {\n      const response = await fetch(`${this.BASE_URL}/${id}`);\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.error(\"Error fetching material:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get a material by slug\n   */\n  static async getMaterialBySlug(slug: string): Promise<ApiResponse<Material>> {\n    try {\n      const response = await fetch(`${this.BASE_URL}/slug/${slug}`);\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.error(\"Error fetching material by slug:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Create a new material\n   */\n  static async createMaterial(\n    materialData: CreateMaterialDto\n  ): Promise<ApiResponse<Material>> {\n    try {\n      const response = await fetch(this.BASE_URL, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(materialData),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(\n          errorData.message || `HTTP error! status: ${response.status}`\n        );\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.error(\"Error creating material:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Update a material\n   */\n  static async updateMaterial(\n    id: string,\n    updateData: UpdateMaterialDto\n  ): Promise<ApiResponse<Material>> {\n    try {\n      const response = await fetch(`${this.BASE_URL}/${id}`, {\n        method: \"PUT\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(updateData),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(\n          errorData.message || `HTTP error! status: ${response.status}`\n        );\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.error(\"Error updating material:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Delete a material\n   */\n  static async deleteMaterial(id: string): Promise<ApiResponse<null>> {\n    try {\n      const response = await fetch(`${this.BASE_URL}/${id}`, {\n        method: \"DELETE\",\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(\n          errorData.message || `HTTP error! status: ${response.status}`\n        );\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.error(\"Error deleting material:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Recalculate product counts for all materials\n   */\n  static async recalculateProductCounts(): Promise<ApiResponse<null>> {\n    try {\n      const response = await fetch(`${this.BASE_URL}/recalculate-counts`, {\n        method: \"POST\",\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(\n          errorData.message || `HTTP error! status: ${response.status}`\n        );\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.error(\"Error recalculating material product counts:\", error);\n      throw error;\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA,4BAA4B;;;;AAU5B,4DAA4D;AAC5D,MAAM,gBAAgB;IACpB,uCAAmC;;IAMnC;IACA,mDAAmD;IACnD,OAAO,6DAAmC;AAC5C;AAEA,MAAM,eAAe;AAgBd,MAAM;IACX,OAAwB,WAAW,GAAG,aAAa,cAAc,CAAC,CAAC;IAEnE;;GAEC,GACD,aAAa,aACX,OAAyB,EACS;QAClC,IAAI;YACF,MAAM,SAAS,IAAI;YAEnB,IAAI,SAAS,aAAa,WAAW;gBACnC,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ,CAAC,QAAQ;YACrD;YAEA,IAAI,SAAS,QAAQ;gBACnB,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;YACxC;YAEA,IAAI,SAAS,YAAY;gBACvB,OAAO,MAAM,CAAC,cAAc,QAAQ,UAAU;YAChD;YAEA,IAAI,SAAS,mBAAmB,WAAW;gBACzC,OAAO,MAAM,CAAC,kBAAkB,QAAQ,cAAc,CAAC,QAAQ;YACjE;YAEA,IAAI,SAAS,eAAe,WAAW;gBACrC,OAAO,MAAM,CAAC,cAAc,QAAQ,UAAU,CAAC,QAAQ;YACzD;YAEA,IAAI,SAAS,QAAQ;gBACnB,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;YACxC;YAEA,MAAM,cAAc,OAAO,QAAQ;YACnC,MAAM,MAAM,cACR,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,aAAa,GACjC,IAAI,CAAC,QAAQ;YAEjB,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,gBAAgB,EAAU,EAAkC;QACvE,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI;YAErD,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,kBAAkB,IAAY,EAAkC;QAC3E,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM;YAE5D,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,eACX,YAA+B,EACC;QAChC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,QAAQ,EAAE;gBAC1C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MACR,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAEjE;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,eACX,EAAU,EACV,UAA6B,EACG;QAChC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,EAAE;gBACrD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MACR,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAEjE;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,eAAe,EAAU,EAA8B;QAClE,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,EAAE;gBACrD,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MACR,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAEjE;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,2BAAuD;QAClE,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;gBAClE,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MACR,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAEjE;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gDAAgD;YAC9D,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 730, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/hooks/useBrands.ts"], "sourcesContent": ["\"use client\";\n\nimport { useCallback, useEffect, useState } from \"react\";\n\nimport { toast } from \"sonner\";\n\nimport {\n  Brand,\n  BrandApi,\n  BrandFilters,\n  CreateBrandDto,\n  UpdateBrandDto,\n} from \"@/lib/api/brands\";\n\n/**\n * Custom hook for managing brands data and operations\n */\nexport const useBrands = (initialFilters?: BrandFilters) => {\n  const [brands, setBrands] = useState<Brand[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [filters, setFilters] = useState<BrandFilters>(initialFilters || {});\n\n  /**\n   * Fetch brands from the API\n   */\n  const fetchBrands = useCallback(\n    async (currentFilters?: BrandFilters) => {\n      try {\n        setLoading(true);\n        setError(null);\n\n        const filtersToUse = currentFilters || filters;\n        const response = await BrandApi.getBrands(filtersToUse);\n\n        if (response.success) {\n          setBrands(response.data);\n        } else {\n          throw new Error(\"Failed to fetch brands\");\n        }\n      } catch (err) {\n        const errorMessage =\n          err instanceof Error ? err.message : \"Failed to fetch brands\";\n        setError(errorMessage);\n        console.error(\"Error fetching brands:\", err);\n      } finally {\n        setLoading(false);\n      }\n    },\n    [filters]\n  );\n\n  /**\n   * Create a new brand\n   */\n  const createBrand = useCallback(\n    async (brandData: CreateBrandDto): Promise<Brand | null> => {\n      try {\n        const response = await BrandApi.createBrand(brandData);\n\n        if (response.success) {\n          setBrands((prev) => [...prev, response.data]);\n          toast.success(\"Brand created successfully\");\n          return response.data;\n        } else {\n          throw new Error(\"Failed to create brand\");\n        }\n      } catch (err) {\n        const errorMessage =\n          err instanceof Error ? err.message : \"Failed to create brand\";\n        toast.error(errorMessage);\n        console.error(\"Error creating brand:\", err);\n        return null;\n      }\n    },\n    []\n  );\n\n  /**\n   * Update an existing brand\n   */\n  const updateBrand = useCallback(\n    async (id: string, updateData: UpdateBrandDto): Promise<Brand | null> => {\n      try {\n        const response = await BrandApi.updateBrand(id, updateData);\n\n        if (response.success) {\n          setBrands((prev) =>\n            prev.map((brand) => (brand._id === id ? response.data : brand))\n          );\n          toast.success(\"Brand updated successfully\");\n          return response.data;\n        } else {\n          throw new Error(\"Failed to update brand\");\n        }\n      } catch (err) {\n        const errorMessage =\n          err instanceof Error ? err.message : \"Failed to update brand\";\n        toast.error(errorMessage);\n        console.error(\"Error updating brand:\", err);\n        return null;\n      }\n    },\n    []\n  );\n\n  /**\n   * Delete a brand\n   */\n  const deleteBrand = useCallback(async (id: string): Promise<boolean> => {\n    try {\n      const response = await BrandApi.deleteBrand(id);\n\n      if (response.success) {\n        setBrands((prev) => prev.filter((brand) => brand._id !== id));\n        toast.success(\"Brand deleted successfully\");\n        return true;\n      } else {\n        throw new Error(\"Failed to delete brand\");\n      }\n    } catch (err) {\n      const errorMessage =\n        err instanceof Error ? err.message : \"Failed to delete brand\";\n      toast.error(errorMessage);\n      console.error(\"Error deleting brand:\", err);\n      return false;\n    }\n  }, []);\n\n  /**\n   * Get a brand by ID\n   */\n  const getBrandById = useCallback(\n    async (id: string): Promise<Brand | null> => {\n      try {\n        const response = await BrandApi.getBrandById(id);\n\n        if (response.success) {\n          return response.data;\n        } else {\n          throw new Error(\"Failed to fetch brand\");\n        }\n      } catch (err) {\n        const errorMessage =\n          err instanceof Error ? err.message : \"Failed to fetch brand\";\n        console.error(\"Error fetching brand by ID:\", err);\n        return null;\n      }\n    },\n    []\n  );\n\n  /**\n   * Get a brand by slug\n   */\n  const getBrandBySlug = useCallback(\n    async (slug: string): Promise<Brand | null> => {\n      try {\n        const response = await BrandApi.getBrandBySlug(slug);\n\n        if (response.success) {\n          return response.data;\n        } else {\n          throw new Error(\"Failed to fetch brand\");\n        }\n      } catch (err) {\n        const errorMessage =\n          err instanceof Error ? err.message : \"Failed to fetch brand\";\n        console.error(\"Error fetching brand by slug:\", err);\n        return null;\n      }\n    },\n    []\n  );\n\n  /**\n   * Recalculate product counts for all brands\n   */\n  const recalculateProductCounts = useCallback(async (): Promise<boolean> => {\n    try {\n      const response = await BrandApi.recalculateProductCounts();\n\n      if (response.success) {\n        // Refresh brands to get updated counts\n        await fetchBrands();\n        toast.success(\"Brand product counts recalculated successfully\");\n        return true;\n      } else {\n        throw new Error(\"Failed to recalculate product counts\");\n      }\n    } catch (err) {\n      const errorMessage =\n        err instanceof Error\n          ? err.message\n          : \"Failed to recalculate product counts\";\n      toast.error(errorMessage);\n      console.error(\"Error recalculating product counts:\", err);\n      return false;\n    }\n  }, [fetchBrands]);\n\n  /**\n   * Update filters and refetch data\n   */\n  const updateFilters = useCallback(\n    (newFilters: BrandFilters) => {\n      setFilters(newFilters);\n      fetchBrands(newFilters);\n    },\n    [fetchBrands]\n  );\n\n  /**\n   * Refresh brands data\n   */\n  const refreshBrands = useCallback(() => {\n    fetchBrands();\n  }, [fetchBrands]);\n\n  // Initial data fetch\n  useEffect(() => {\n    fetchBrands();\n  }, [fetchBrands]);\n\n  return {\n    brands,\n    loading,\n    error,\n    filters,\n    createBrand,\n    updateBrand,\n    deleteBrand,\n    getBrandById,\n    getBrandBySlug,\n    recalculateProductCounts,\n    updateFilters,\n    refreshBrands,\n    refetch: refreshBrands,\n  };\n};\n\n/**\n * Hook for brand CRUD operations (similar to useCategoryMutations)\n */\nexport function useBrandMutations() {\n  const [loading, setLoading] = useState(false);\n\n  const createBrand = useCallback(async (brandData: CreateBrandDto) => {\n    setLoading(true);\n    try {\n      const response = await BrandApi.createBrand(brandData);\n      if (response.success) {\n        toast.success(\"Brand created successfully\");\n        return response.data;\n      } else {\n        throw new Error(\"Failed to create brand\");\n      }\n    } catch (error) {\n      const errorMessage =\n        error instanceof Error ? error.message : \"Failed to create brand\";\n      toast.error(errorMessage);\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  const updateBrand = useCallback(\n    async (id: string, updateData: UpdateBrandDto) => {\n      setLoading(true);\n      try {\n        const response = await BrandApi.updateBrand(id, updateData);\n        if (response.success) {\n          toast.success(\"Brand updated successfully\");\n          return response.data;\n        } else {\n          throw new Error(\"Failed to update brand\");\n        }\n      } catch (error) {\n        const errorMessage =\n          error instanceof Error ? error.message : \"Failed to update brand\";\n        toast.error(errorMessage);\n        throw error;\n      } finally {\n        setLoading(false);\n      }\n    },\n    []\n  );\n\n  const deleteBrand = useCallback(async (id: string) => {\n    setLoading(true);\n    try {\n      const response = await BrandApi.deleteBrand(id);\n      if (response.success) {\n        toast.success(\"Brand deleted successfully\");\n        return true;\n      } else {\n        throw new Error(\"Failed to delete brand\");\n      }\n    } catch (error) {\n      const errorMessage =\n        error instanceof Error ? error.message : \"Failed to delete brand\";\n      toast.error(errorMessage);\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  return {\n    createBrand,\n    updateBrand,\n    deleteBrand,\n    loading,\n  };\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAEA;AANA;;;;AAiBO,MAAM,YAAY,CAAC;IACxB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAgB,kBAAkB,CAAC;IAExE;;GAEC,GACD,MAAM,cAAc,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAC5B,OAAO;QACL,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,eAAe,kBAAkB;YACvC,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,SAAS,CAAC;YAE1C,IAAI,SAAS,OAAO,EAAE;gBACpB,UAAU,SAAS,IAAI;YACzB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eACJ,eAAe,QAAQ,IAAI,OAAO,GAAG;YACvC,SAAS;YACT,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,WAAW;QACb;IACF,GACA;QAAC;KAAQ;IAGX;;GAEC,GACD,MAAM,cAAc,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAC5B,OAAO;QACL,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;YAE5C,IAAI,SAAS,OAAO,EAAE;gBACpB,UAAU,CAAC,OAAS;2BAAI;wBAAM,SAAS,IAAI;qBAAC;gBAC5C,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eACJ,eAAe,QAAQ,IAAI,OAAO,GAAG;YACvC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO;QACT;IACF,GACA,EAAE;IAGJ;;GAEC,GACD,MAAM,cAAc,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAC5B,OAAO,IAAY;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,WAAW,CAAC,IAAI;YAEhD,IAAI,SAAS,OAAO,EAAE;gBACpB,UAAU,CAAC,OACT,KAAK,GAAG,CAAC,CAAC,QAAW,MAAM,GAAG,KAAK,KAAK,SAAS,IAAI,GAAG;gBAE1D,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eACJ,eAAe,QAAQ,IAAI,OAAO,GAAG;YACvC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO;QACT;IACF,GACA,EAAE;IAGJ;;GAEC,GACD,MAAM,cAAc,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACrC,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;YAE5C,IAAI,SAAS,OAAO,EAAE;gBACpB,UAAU,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,QAAU,MAAM,GAAG,KAAK;gBACzD,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO;YACT,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eACJ,eAAe,QAAQ,IAAI,OAAO,GAAG;YACvC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO;QACT;IACF,GAAG,EAAE;IAEL;;GAEC,GACD,MAAM,eAAe,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAC7B,OAAO;QACL,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,YAAY,CAAC;YAE7C,IAAI,SAAS,OAAO,EAAE;gBACpB,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eACJ,eAAe,QAAQ,IAAI,OAAO,GAAG;YACvC,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO;QACT;IACF,GACA,EAAE;IAGJ;;GAEC,GACD,MAAM,iBAAiB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAC/B,OAAO;QACL,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,cAAc,CAAC;YAE/C,IAAI,SAAS,OAAO,EAAE;gBACpB,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eACJ,eAAe,QAAQ,IAAI,OAAO,GAAG;YACvC,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;QACT;IACF,GACA,EAAE;IAGJ;;GAEC,GACD,MAAM,2BAA2B,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE;QAC3C,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,wBAAwB;YAExD,IAAI,SAAS,OAAO,EAAE;gBACpB,uCAAuC;gBACvC,MAAM;gBACN,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO;YACT,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eACJ,eAAe,QACX,IAAI,OAAO,GACX;YACN,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,uCAAuC;YACrD,OAAO;QACT;IACF,GAAG;QAAC;KAAY;IAEhB;;GAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAC9B,CAAC;QACC,WAAW;QACX,YAAY;IACd,GACA;QAAC;KAAY;IAGf;;GAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE;QAChC;IACF,GAAG;QAAC;KAAY;IAEhB,qBAAqB;IACrB,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAY;IAEhB,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,SAAS;IACX;AACF;AAKO,SAAS;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,cAAc,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACrC,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;YAC5C,IAAI,SAAS,OAAO,EAAE;gBACpB,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC3C,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF,GAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAC5B,OAAO,IAAY;QACjB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,WAAW,CAAC,IAAI;YAChD,IAAI,SAAS,OAAO,EAAE;gBACpB,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC3C,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF,GACA,EAAE;IAGJ,MAAM,cAAc,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACrC,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;YAC5C,IAAI,SAAS,OAAO,EAAE;gBACpB,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO;YACT,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC3C,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 989, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/hooks/useColors.ts"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect, useCallback } from \"react\";\nimport { toast } from \"sonner\";\n\nimport { ColorApi, Color, ColorFilters, CreateColorDto, UpdateColorDto, ColorFamily } from \"@/lib/api/colors\";\n\n/**\n * Custom hook for managing colors data and operations\n */\nexport const useColors = (initialFilters?: ColorFilters) => {\n  const [colors, setColors] = useState<Color[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [filters, setFilters] = useState<ColorFilters>(initialFilters || {});\n\n  /**\n   * Fetch colors from the API\n   */\n  const fetchColors = useCallback(async (currentFilters?: ColorFilters) => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      const filtersToUse = currentFilters || filters;\n      const response = await ColorApi.getColors(filtersToUse);\n      \n      if (response.success) {\n        setColors(response.data);\n      } else {\n        throw new Error(\"Failed to fetch colors\");\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Failed to fetch colors\";\n      setError(errorMessage);\n      console.error(\"Error fetching colors:\", err);\n    } finally {\n      setLoading(false);\n    }\n  }, [filters]);\n\n  /**\n   * Create a new color\n   */\n  const createColor = useCallback(async (colorData: CreateColorDto): Promise<Color | null> => {\n    try {\n      const response = await ColorApi.createColor(colorData);\n      \n      if (response.success) {\n        setColors(prev => [...prev, response.data]);\n        toast.success(\"Color created successfully\");\n        return response.data;\n      } else {\n        throw new Error(\"Failed to create color\");\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Failed to create color\";\n      toast.error(errorMessage);\n      console.error(\"Error creating color:\", err);\n      return null;\n    }\n  }, []);\n\n  /**\n   * Update an existing color\n   */\n  const updateColor = useCallback(async (id: string, updateData: UpdateColorDto): Promise<Color | null> => {\n    try {\n      const response = await ColorApi.updateColor(id, updateData);\n      \n      if (response.success) {\n        setColors(prev => \n          prev.map(color => \n            color._id === id ? response.data : color\n          )\n        );\n        toast.success(\"Color updated successfully\");\n        return response.data;\n      } else {\n        throw new Error(\"Failed to update color\");\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Failed to update color\";\n      toast.error(errorMessage);\n      console.error(\"Error updating color:\", err);\n      return null;\n    }\n  }, []);\n\n  /**\n   * Delete a color\n   */\n  const deleteColor = useCallback(async (id: string): Promise<boolean> => {\n    try {\n      const response = await ColorApi.deleteColor(id);\n      \n      if (response.success) {\n        setColors(prev => prev.filter(color => color._id !== id));\n        toast.success(\"Color deleted successfully\");\n        return true;\n      } else {\n        throw new Error(\"Failed to delete color\");\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Failed to delete color\";\n      toast.error(errorMessage);\n      console.error(\"Error deleting color:\", err);\n      return false;\n    }\n  }, []);\n\n  /**\n   * Get a color by ID\n   */\n  const getColorById = useCallback(async (id: string): Promise<Color | null> => {\n    try {\n      const response = await ColorApi.getColorById(id);\n      \n      if (response.success) {\n        return response.data;\n      } else {\n        throw new Error(\"Failed to fetch color\");\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Failed to fetch color\";\n      console.error(\"Error fetching color by ID:\", err);\n      return null;\n    }\n  }, []);\n\n  /**\n   * Get a color by slug\n   */\n  const getColorBySlug = useCallback(async (slug: string): Promise<Color | null> => {\n    try {\n      const response = await ColorApi.getColorBySlug(slug);\n      \n      if (response.success) {\n        return response.data;\n      } else {\n        throw new Error(\"Failed to fetch color\");\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Failed to fetch color\";\n      console.error(\"Error fetching color by slug:\", err);\n      return null;\n    }\n  }, []);\n\n  /**\n   * Get a color by hex value\n   */\n  const getColorByHex = useCallback(async (hex: string): Promise<Color | null> => {\n    try {\n      const response = await ColorApi.getColorByHex(hex);\n      \n      if (response.success) {\n        return response.data;\n      } else {\n        throw new Error(\"Failed to fetch color\");\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Failed to fetch color\";\n      console.error(\"Error fetching color by hex:\", err);\n      return null;\n    }\n  }, []);\n\n  /**\n   * Get colors grouped by color family\n   */\n  const getColorsByFamily = useCallback(async (): Promise<Record<ColorFamily, Color[]> | null> => {\n    try {\n      const response = await ColorApi.getColorsByFamily();\n      \n      if (response.success) {\n        return response.data;\n      } else {\n        throw new Error(\"Failed to fetch colors by family\");\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Failed to fetch colors by family\";\n      console.error(\"Error fetching colors by family:\", err);\n      return null;\n    }\n  }, []);\n\n  /**\n   * Recalculate product counts for all colors\n   */\n  const recalculateProductCounts = useCallback(async (): Promise<boolean> => {\n    try {\n      const response = await ColorApi.recalculateProductCounts();\n      \n      if (response.success) {\n        // Refresh colors to get updated counts\n        await fetchColors();\n        toast.success(\"Color product counts recalculated successfully\");\n        return true;\n      } else {\n        throw new Error(\"Failed to recalculate product counts\");\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Failed to recalculate product counts\";\n      toast.error(errorMessage);\n      console.error(\"Error recalculating product counts:\", err);\n      return false;\n    }\n  }, [fetchColors]);\n\n  /**\n   * Update filters and refetch data\n   */\n  const updateFilters = useCallback((newFilters: ColorFilters) => {\n    setFilters(newFilters);\n    fetchColors(newFilters);\n  }, [fetchColors]);\n\n  /**\n   * Refresh colors data\n   */\n  const refreshColors = useCallback(() => {\n    fetchColors();\n  }, [fetchColors]);\n\n  // Initial data fetch\n  useEffect(() => {\n    fetchColors();\n  }, [fetchColors]);\n\n  return {\n    colors,\n    loading,\n    error,\n    filters,\n    createColor,\n    updateColor,\n    deleteColor,\n    getColorById,\n    getColorBySlug,\n    getColorByHex,\n    getColorsByFamily,\n    recalculateProductCounts,\n    updateFilters,\n    refreshColors,\n  };\n};\n"], "names": [], "mappings": ";;;AAEA;AACA;AAEA;AALA;;;;AAUO,MAAM,YAAY,CAAC;IACxB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAgB,kBAAkB,CAAC;IAExE;;GAEC,GACD,MAAM,cAAc,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACrC,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,eAAe,kBAAkB;YACvC,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,SAAS,CAAC;YAE1C,IAAI,SAAS,OAAO,EAAE;gBACpB,UAAU,SAAS,IAAI;YACzB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;KAAQ;IAEZ;;GAEC,GACD,MAAM,cAAc,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACrC,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;YAE5C,IAAI,SAAS,OAAO,EAAE;gBACpB,UAAU,CAAA,OAAQ;2BAAI;wBAAM,SAAS,IAAI;qBAAC;gBAC1C,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO;QACT;IACF,GAAG,EAAE;IAEL;;GAEC,GACD,MAAM,cAAc,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO,IAAY;QACjD,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,WAAW,CAAC,IAAI;YAEhD,IAAI,SAAS,OAAO,EAAE;gBACpB,UAAU,CAAA,OACR,KAAK,GAAG,CAAC,CAAA,QACP,MAAM,GAAG,KAAK,KAAK,SAAS,IAAI,GAAG;gBAGvC,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO;QACT;IACF,GAAG,EAAE;IAEL;;GAEC,GACD,MAAM,cAAc,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACrC,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;YAE5C,IAAI,SAAS,OAAO,EAAE;gBACpB,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,GAAG,KAAK;gBACrD,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO;YACT,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO;QACT;IACF,GAAG,EAAE;IAEL;;GAEC,GACD,MAAM,eAAe,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACtC,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,YAAY,CAAC;YAE7C,IAAI,SAAS,OAAO,EAAE;gBACpB,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO;QACT;IACF,GAAG,EAAE;IAEL;;GAEC,GACD,MAAM,iBAAiB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACxC,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,cAAc,CAAC;YAE/C,IAAI,SAAS,OAAO,EAAE;gBACpB,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;QACT;IACF,GAAG,EAAE;IAEL;;GAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACvC,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,aAAa,CAAC;YAE9C,IAAI,SAAS,OAAO,EAAE;gBACpB,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO;QACT;IACF,GAAG,EAAE;IAEL;;GAEC,GACD,MAAM,oBAAoB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE;QACpC,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,iBAAiB;YAEjD,IAAI,SAAS,OAAO,EAAE;gBACpB,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;QACT;IACF,GAAG,EAAE;IAEL;;GAEC,GACD,MAAM,2BAA2B,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE;QAC3C,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,wBAAwB;YAExD,IAAI,SAAS,OAAO,EAAE;gBACpB,uCAAuC;gBACvC,MAAM;gBACN,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO;YACT,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,uCAAuC;YACrD,OAAO;QACT;IACF,GAAG;QAAC;KAAY;IAEhB;;GAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,WAAW;QACX,YAAY;IACd,GAAG;QAAC;KAAY;IAEhB;;GAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE;QAChC;IACF,GAAG;QAAC;KAAY;IAEhB,qBAAqB;IACrB,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAY;IAEhB,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1217, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/hooks/useCategories.ts"], "sourcesContent": ["/**\n * React hooks for category data management\n * Provides easy-to-use hooks for CRUD operations on categories\n */\n\nimport { useCallback, useEffect, useState } from 'react';\nimport { toast } from 'sonner';\n\nimport { Category } from '@/components/pages/management/CategoryManager';\nimport {\n  CategoryApiService,\n  type CategoryFilters,\n  type CreateCategoryDto,\n  type UpdateCategoryDto,\n} from '@/lib/api/categoryApi';\n\n// Hook state types\ninterface UseCategoriesState {\n  categories: Category[];\n  loading: boolean;\n  error: string | null;\n}\n\ninterface UseCategoryState {\n  category: Category | null;\n  loading: boolean;\n  error: string | null;\n}\n\n/**\n * Hook for fetching and managing categories list\n */\nexport function useCategories(initialFilters: CategoryFilters = {}) {\n  const [state, setState] = useState<UseCategoriesState>({\n    categories: [],\n    loading: true,\n    error: null,\n  });\n\n  const [filters, setFilters] = useState<CategoryFilters>(initialFilters);\n\n  const fetchCategories = useCallback(async (newFilters?: CategoryFilters) => {\n    setState(prev => ({ ...prev, loading: true, error: null }));\n    \n    try {\n      const filtersToUse = newFilters || filters;\n      const categories = await CategoryApiService.getCategories(filtersToUse);\n      \n      setState({\n        categories,\n        loading: false,\n        error: null,\n      });\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch categories';\n      setState(prev => ({\n        ...prev,\n        loading: false,\n        error: errorMessage,\n      }));\n      toast.error('Failed to load categories');\n    }\n  }, [filters]);\n\n  // Initial fetch\n  useEffect(() => {\n    fetchCategories();\n  }, [fetchCategories]);\n\n  const updateFilters = useCallback((newFilters: CategoryFilters) => {\n    setFilters(newFilters);\n    fetchCategories(newFilters);\n  }, [fetchCategories]);\n\n  return {\n    ...state,\n    filters,\n    updateFilters,\n    refetch: fetchCategories,\n  };\n}\n\n/**\n * Hook for fetching and managing a single category\n */\nexport function useCategory(id: string | null) {\n  const [state, setState] = useState<UseCategoryState>({\n    category: null,\n    loading: true,\n    error: null,\n  });\n\n  const fetchCategory = useCallback(async () => {\n    if (!id) {\n      setState({ category: null, loading: false, error: null });\n      return;\n    }\n\n    setState(prev => ({ ...prev, loading: true, error: null }));\n    \n    try {\n      const category = await CategoryApiService.getCategoryById(id);\n      setState({\n        category,\n        loading: false,\n        error: null,\n      });\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch category';\n      setState(prev => ({\n        ...prev,\n        loading: false,\n        error: errorMessage,\n      }));\n      toast.error('Failed to load category');\n    }\n  }, [id]);\n\n  useEffect(() => {\n    fetchCategory();\n  }, [fetchCategory]);\n\n  return {\n    ...state,\n    refetch: fetchCategory,\n  };\n}\n\n/**\n * Hook for category CRUD operations\n */\nexport function useCategoryMutations() {\n  const [loading, setLoading] = useState(false);\n\n  const createCategory = useCallback(async (categoryData: CreateCategoryDto) => {\n    setLoading(true);\n    try {\n      const category = await CategoryApiService.createCategory(categoryData);\n      toast.success('Category created successfully');\n      return category;\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to create category';\n      toast.error(errorMessage);\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  const updateCategory = useCallback(async (id: string, updateData: UpdateCategoryDto) => {\n    setLoading(true);\n    try {\n      const category = await CategoryApiService.updateCategory(id, updateData);\n      toast.success('Category updated successfully');\n      return category;\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to update category';\n      toast.error(errorMessage);\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  const deleteCategory = useCallback(async (id: string) => {\n    setLoading(true);\n    try {\n      await CategoryApiService.deleteCategory(id);\n      toast.success('Category deleted successfully');\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to delete category';\n      toast.error(errorMessage);\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  return {\n    createCategory,\n    updateCategory,\n    deleteCategory,\n    loading,\n  };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAED;AACA;AAGA;;;;AAuBO,SAAS,cAAc,iBAAkC,CAAC,CAAC;IAChE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAsB;QACrD,YAAY,EAAE;QACd,SAAS;QACT,OAAO;IACT;IAEA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAmB;IAExD,MAAM,kBAAkB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACzC,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK,CAAC;QAEzD,IAAI;YACF,MAAM,eAAe,cAAc;YACnC,MAAM,aAAa,MAAM,yHAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC;YAE1D,SAAS;gBACP;gBACA,SAAS;gBACT,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,SAAS;oBACT,OAAO;gBACT,CAAC;YACD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF,GAAG;QAAC;KAAQ;IAEZ,gBAAgB;IAChB,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAgB;IAEpB,MAAM,gBAAgB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,WAAW;QACX,gBAAgB;IAClB,GAAG;QAAC;KAAgB;IAEpB,OAAO;QACL,GAAG,KAAK;QACR;QACA;QACA,SAAS;IACX;AACF;AAKO,SAAS,YAAY,EAAiB;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAoB;QACnD,UAAU;QACV,SAAS;QACT,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE;QAChC,IAAI,CAAC,IAAI;YACP,SAAS;gBAAE,UAAU;gBAAM,SAAS;gBAAO,OAAO;YAAK;YACvD;QACF;QAEA,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK,CAAC;QAEzD,IAAI;YACF,MAAM,WAAW,MAAM,yHAAA,CAAA,qBAAkB,CAAC,eAAe,CAAC;YAC1D,SAAS;gBACP;gBACA,SAAS;gBACT,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,SAAS;oBACT,OAAO;gBACT,CAAC;YACD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF,GAAG;QAAC;KAAG;IAEP,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAc;IAElB,OAAO;QACL,GAAG,KAAK;QACR,SAAS;IACX;AACF;AAKO,SAAS;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,iBAAiB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACxC,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,yHAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;YACzD,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO,IAAY;QACpD,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,yHAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC,IAAI;YAC7D,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACxC,WAAW;QACX,IAAI;YACF,MAAM,yHAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;YACxC,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1388, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/hooks/useMaterials.ts"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect, useCallback } from \"react\";\nimport { toast } from \"sonner\";\n\nimport { MaterialApi, Material, MaterialFilters, CreateMaterialDto, UpdateMaterialDto } from \"@/lib/api/materials\";\n\n/**\n * Custom hook for managing materials data and operations\n */\nexport const useMaterials = (initialFilters?: MaterialFilters) => {\n  const [materials, setMaterials] = useState<Material[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [filters, setFilters] = useState<MaterialFilters>(initialFilters || {});\n\n  /**\n   * Fetch materials from the API\n   */\n  const fetchMaterials = useCallback(async (currentFilters?: MaterialFilters) => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      const filtersToUse = currentFilters || filters;\n      const response = await MaterialApi.getMaterials(filtersToUse);\n      \n      if (response.success) {\n        setMaterials(response.data);\n      } else {\n        throw new Error(\"Failed to fetch materials\");\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Failed to fetch materials\";\n      setError(errorMessage);\n      console.error(\"Error fetching materials:\", err);\n    } finally {\n      setLoading(false);\n    }\n  }, [filters]);\n\n  /**\n   * Create a new material\n   */\n  const createMaterial = useCallback(async (materialData: CreateMaterialDto): Promise<Material | null> => {\n    try {\n      const response = await MaterialApi.createMaterial(materialData);\n      \n      if (response.success) {\n        setMaterials(prev => [...prev, response.data]);\n        toast.success(\"Material created successfully\");\n        return response.data;\n      } else {\n        throw new Error(\"Failed to create material\");\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Failed to create material\";\n      toast.error(errorMessage);\n      console.error(\"Error creating material:\", err);\n      return null;\n    }\n  }, []);\n\n  /**\n   * Update an existing material\n   */\n  const updateMaterial = useCallback(async (id: string, updateData: UpdateMaterialDto): Promise<Material | null> => {\n    try {\n      const response = await MaterialApi.updateMaterial(id, updateData);\n      \n      if (response.success) {\n        setMaterials(prev => \n          prev.map(material => \n            material._id === id ? response.data : material\n          )\n        );\n        toast.success(\"Material updated successfully\");\n        return response.data;\n      } else {\n        throw new Error(\"Failed to update material\");\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Failed to update material\";\n      toast.error(errorMessage);\n      console.error(\"Error updating material:\", err);\n      return null;\n    }\n  }, []);\n\n  /**\n   * Delete a material\n   */\n  const deleteMaterial = useCallback(async (id: string): Promise<boolean> => {\n    try {\n      const response = await MaterialApi.deleteMaterial(id);\n      \n      if (response.success) {\n        setMaterials(prev => prev.filter(material => material._id !== id));\n        toast.success(\"Material deleted successfully\");\n        return true;\n      } else {\n        throw new Error(\"Failed to delete material\");\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Failed to delete material\";\n      toast.error(errorMessage);\n      console.error(\"Error deleting material:\", err);\n      return false;\n    }\n  }, []);\n\n  /**\n   * Get a material by ID\n   */\n  const getMaterialById = useCallback(async (id: string): Promise<Material | null> => {\n    try {\n      const response = await MaterialApi.getMaterialById(id);\n      \n      if (response.success) {\n        return response.data;\n      } else {\n        throw new Error(\"Failed to fetch material\");\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Failed to fetch material\";\n      console.error(\"Error fetching material by ID:\", err);\n      return null;\n    }\n  }, []);\n\n  /**\n   * Get a material by slug\n   */\n  const getMaterialBySlug = useCallback(async (slug: string): Promise<Material | null> => {\n    try {\n      const response = await MaterialApi.getMaterialBySlug(slug);\n      \n      if (response.success) {\n        return response.data;\n      } else {\n        throw new Error(\"Failed to fetch material\");\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Failed to fetch material\";\n      console.error(\"Error fetching material by slug:\", err);\n      return null;\n    }\n  }, []);\n\n  /**\n   * Recalculate product counts for all materials\n   */\n  const recalculateProductCounts = useCallback(async (): Promise<boolean> => {\n    try {\n      const response = await MaterialApi.recalculateProductCounts();\n      \n      if (response.success) {\n        // Refresh materials to get updated counts\n        await fetchMaterials();\n        toast.success(\"Material product counts recalculated successfully\");\n        return true;\n      } else {\n        throw new Error(\"Failed to recalculate product counts\");\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Failed to recalculate product counts\";\n      toast.error(errorMessage);\n      console.error(\"Error recalculating product counts:\", err);\n      return false;\n    }\n  }, [fetchMaterials]);\n\n  /**\n   * Update filters and refetch data\n   */\n  const updateFilters = useCallback((newFilters: MaterialFilters) => {\n    setFilters(newFilters);\n    fetchMaterials(newFilters);\n  }, [fetchMaterials]);\n\n  /**\n   * Refresh materials data\n   */\n  const refreshMaterials = useCallback(() => {\n    fetchMaterials();\n  }, [fetchMaterials]);\n\n  // Initial data fetch\n  useEffect(() => {\n    fetchMaterials();\n  }, [fetchMaterials]);\n\n  return {\n    materials,\n    loading,\n    error,\n    filters,\n    createMaterial,\n    updateMaterial,\n    deleteMaterial,\n    getMaterialById,\n    getMaterialBySlug,\n    recalculateProductCounts,\n    updateFilters,\n    refreshMaterials,\n  };\n};\n"], "names": [], "mappings": ";;;AAEA;AACA;AAEA;AALA;;;;AAUO,MAAM,eAAe,CAAC;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAmB,kBAAkB,CAAC;IAE3E;;GAEC,GACD,MAAM,iBAAiB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACxC,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,eAAe,kBAAkB;YACvC,MAAM,WAAW,MAAM,uHAAA,CAAA,cAAW,CAAC,YAAY,CAAC;YAEhD,IAAI,SAAS,OAAO,EAAE;gBACpB,aAAa,SAAS,IAAI;YAC5B,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;KAAQ;IAEZ;;GAEC,GACD,MAAM,iBAAiB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACxC,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,cAAW,CAAC,cAAc,CAAC;YAElD,IAAI,SAAS,OAAO,EAAE;gBACpB,aAAa,CAAA,OAAQ;2BAAI;wBAAM,SAAS,IAAI;qBAAC;gBAC7C,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;QACT;IACF,GAAG,EAAE;IAEL;;GAEC,GACD,MAAM,iBAAiB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO,IAAY;QACpD,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,cAAW,CAAC,cAAc,CAAC,IAAI;YAEtD,IAAI,SAAS,OAAO,EAAE;gBACpB,aAAa,CAAA,OACX,KAAK,GAAG,CAAC,CAAA,WACP,SAAS,GAAG,KAAK,KAAK,SAAS,IAAI,GAAG;gBAG1C,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;QACT;IACF,GAAG,EAAE;IAEL;;GAEC,GACD,MAAM,iBAAiB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACxC,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,cAAW,CAAC,cAAc,CAAC;YAElD,IAAI,SAAS,OAAO,EAAE;gBACpB,aAAa,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,WAAY,SAAS,GAAG,KAAK;gBAC9D,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO;YACT,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;QACT;IACF,GAAG,EAAE;IAEL;;GAEC,GACD,MAAM,kBAAkB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACzC,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,cAAW,CAAC,eAAe,CAAC;YAEnD,IAAI,SAAS,OAAO,EAAE;gBACpB,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO;QACT;IACF,GAAG,EAAE;IAEL;;GAEC,GACD,MAAM,oBAAoB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC3C,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,cAAW,CAAC,iBAAiB,CAAC;YAErD,IAAI,SAAS,OAAO,EAAE;gBACpB,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;QACT;IACF,GAAG,EAAE;IAEL;;GAEC,GACD,MAAM,2BAA2B,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE;QAC3C,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,cAAW,CAAC,wBAAwB;YAE3D,IAAI,SAAS,OAAO,EAAE;gBACpB,0CAA0C;gBAC1C,MAAM;gBACN,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO;YACT,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,uCAAuC;YACrD,OAAO;QACT;IACF,GAAG;QAAC;KAAe;IAEnB;;GAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,WAAW;QACX,eAAe;IACjB,GAAG;QAAC;KAAe;IAEnB;;GAEC,GACD,MAAM,mBAAmB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE;QACnC;IACF,GAAG;QAAC;KAAe;IAEnB,qBAAqB;IACrB,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAe;IAEnB,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}]}