import { Document } from "mongoose";

/**
 * Brand type matching the frontend Brand type
 */
export type IBrand = {
  name: string;
  description: string;
  slug: string;
  logo?: string;
  website?: string;
  color?: string;
  isActive: boolean;
  productCount: number;
  sortOrder: number;
};

/**
 * Brand document interface for MongoDB (keeping as interface for Document extension)
 */
export interface BrandDocument extends IBrand, Document {
  _id: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * DTO for creating a new brand
 */
export type CreateBrandDto = {
  name: string;
  description?: string;
  logo?: string;
  website?: string;
  color?: string;
  isActive?: boolean;
  sortOrder?: number;
};

/**
 * DTO for updating a brand
 */
export type UpdateBrandDto = {
  name?: string;
  description?: string;
  slug?: string;
  logo?: string;
  website?: string;
  color?: string;
  isActive?: boolean;
  sortOrder?: number;
};

/**
 * Brand filters for querying
 */
export type BrandFilters = {
  isActive?: boolean;
  search?: string;
};
