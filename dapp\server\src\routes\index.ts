import { Express, Request, Response } from "express";
import mongoose from "mongoose";

import brandRoutes from "./brand.routes";
import { categoryRoutes } from "./category.routes";
import colorRoutes from "./color.routes";
import materialRoutes from "./material.routes";
import orderRoutes from "./order.routes";
import { productRoutes } from "./product.routes";

/**
 * Register all API routes
 * @param app - Express application instance
 */
export const registerRoutes = (app: Express): void => {
  // Base API route - provides information about available endpoints
  app.get("/api", (req: Request, res: Response) => {
    res.json({
      success: true,
      message: "Welcome to the E-Commerce API",
      version: "1.0.0",
      endpoints: {
        products: {
          base: "/api/products",
          methods: {
            GET: "Get all products or a specific product by ID",
            POST: "Create a new product",
            PUT: "Update an existing product",
            DELETE: "Delete a product",
          },
        },
        categories: {
          base: "/api/categories",
          methods: {
            GET: "Get all categories or a specific category by ID/slug",
            POST: "Create a new category",
            PUT: "Update an existing category",
            DELETE: "Delete a category",
          },
        },
        brands: {
          base: "/api/brands",
          methods: {
            GET: "Get all brands or a specific brand by ID/slug",
            POST: "Create a new brand",
            PUT: "Update an existing brand",
            DELETE: "Delete a brand",
          },
        },
        materials: {
          base: "/api/materials",
          methods: {
            GET: "Get all materials or a specific material by ID/slug",
            POST: "Create a new material",
            PUT: "Update an existing material",
            DELETE: "Delete a material",
          },
        },
        colors: {
          base: "/api/colors",
          methods: {
            GET: "Get all colors or a specific color by ID/slug/hex",
            POST: "Create a new color",
            PUT: "Update an existing color",
            DELETE: "Delete a color",
          },
        },
        orders: {
          base: "/api/orders",
          methods: {
            GET: "Get all orders or a specific order by ID/number",
            POST: "Create a new order",
            PUT: "Update an existing order or order status",
            DELETE: "Cancel an order",
          },
        },
      },
      documentation: `${req.protocol}://${req.get("host")}/api-docs`,
    });
  });

  // API routes
  app.use("/api/products", productRoutes);
  app.use("/api/categories", categoryRoutes);
  app.use("/api/brands", brandRoutes);
  app.use("/api/materials", materialRoutes);
  app.use("/api/colors", colorRoutes);
  app.use("/api/orders", orderRoutes);
  // Add more routes as needed
  // app.use('/api/users', userRoutes);

  // Debug route to check database connection and collections
  app.get(
    "/api/debug/db",
    async (_req: Request, res: Response): Promise<void> => {
      try {
        // Check if the database connection is established
        if (!mongoose.connection) {
          res.status(500).json({
            success: false,
            message: "Database connection not established",
            connectionState: "unknown",
          });
          return;
        }

        // Return basic connection info
        res.json({
          success: true,
          data: {
            connectionState: mongoose.connection.readyState,
            connected: mongoose.connection.readyState === 1,
            databaseName: mongoose.connection.name,
          },
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          message:
            error instanceof Error
              ? error.message
              : "Failed to get database debug info",
        });
      }
    }
  );
};
