import { Schema, model } from "mongoose";

import { OrderDocument } from "../types/order.types";

/**
 * Order item sub-schema
 */
const orderItemSchema = new Schema(
  {
    productId: {
      type: String,
      required: true,
      index: true,
    },
    name: {
      type: String,
      required: true,
      trim: true,
    },
    quantity: {
      type: Number,
      required: true,
      min: 1,
    },
    price: {
      type: Number,
      required: true,
      min: 0,
    },
    currency: {
      type: String,
      enum: ["EUR", "USD"],
      default: "EUR",
    },
    image: {
      type: String,
      default: "",
    },
    sku: {
      type: String,
      default: "",
    },
    variant: {
      type: String,
      default: "",
    },
  },
  { _id: false }
);

/**
 * Customer sub-schema
 */
const customerSchema = new Schema(
  {
    customerId: {
      type: String,
      index: true,
    },
    name: {
      type: String,
      required: true,
      trim: true,
    },
    email: {
      type: String,
      required: true,
      lowercase: true,
      trim: true,
      index: true,
    },
    phone: {
      type: String,
      trim: true,
    },
    avatar: {
      type: String,
      default: "",
    },
  },
  { _id: false }
);

/**
 * Address sub-schema
 */
const addressSchema = new Schema(
  {
    street: {
      type: String,
      required: true,
      trim: true,
    },
    city: {
      type: String,
      required: true,
      trim: true,
    },
    state: {
      type: String,
      trim: true,
    },
    postalCode: {
      type: String,
      required: true,
      trim: true,
    },
    country: {
      type: String,
      required: true,
      trim: true,
    },
    coordinates: {
      latitude: Number,
      longitude: Number,
    },
  },
  { _id: false }
);

/**
 * Shipping sub-schema
 */
const shippingSchema = new Schema(
  {
    method: {
      type: String,
      required: true,
      trim: true,
    },
    cost: {
      type: Number,
      required: true,
      min: 0,
    },
    trackingNumber: {
      type: String,
      trim: true,
    },
    estimatedDelivery: Date,
    shippedAt: Date,
    deliveredAt: Date,
  },
  { _id: false }
);

/**
 * Payment sub-schema
 */
const paymentSchema = new Schema(
  {
    method: {
      type: String,
      enum: ["card", "paypal", "bank-transfer", "cash-on-delivery", "crypto"],
      required: true,
    },
    status: {
      type: String,
      enum: ["pending", "paid", "failed", "refunded", "partially-refunded"],
      default: "pending",
      index: true,
    },
    transactionId: {
      type: String,
      trim: true,
    },
    providerTransactionId: {
      type: String,
      trim: true,
    },
    cardLast4: {
      type: String,
      trim: true,
    },
    cardType: {
      type: String,
      trim: true,
    },
    receiptUrl: {
      type: String,
      trim: true,
    },
    paidAt: Date,
  },
  { _id: false }
);

/**
 * MongoDB schema for Order
 */
const orderSchema = new Schema<OrderDocument>(
  {
    orderNumber: {
      type: String,
      unique: true,
      index: true,
    },
    customer: {
      type: customerSchema,
      required: true,
    },
    items: {
      type: [orderItemSchema],
      required: true,
      validate: {
        validator: function (items: unknown[]) {
          return items && items.length > 0;
        },
        message: "Order must have at least one item",
      },
    },

    // Pricing
    subtotal: {
      type: Number,
      required: true,
      min: 0,
    },
    shippingCost: {
      type: Number,
      required: true,
      min: 0,
      default: 0,
    },
    tax: {
      type: Number,
      required: true,
      min: 0,
      default: 0,
    },
    discount: {
      type: Number,
      min: 0,
      default: 0,
    },
    totalAmount: {
      type: Number,
      required: true,
      min: 0,
    },
    currency: {
      type: String,
      enum: ["EUR", "USD"],
      default: "EUR",
    },

    // Status
    status: {
      type: String,
      enum: [
        "pending",
        "paid",
        "processing",
        "shipped",
        "delivered",
        "cancelled",
        "refunded",
        "returned",
        "failed",
      ],
      default: "pending",
      index: true,
    },
    paymentStatus: {
      type: String,
      enum: ["pending", "paid", "failed", "refunded", "partially-refunded"],
      default: "pending",
      index: true,
    },
    shippingStatus: {
      type: String,
      enum: [
        "pending",
        "preparing",
        "shipped",
        "in-transit",
        "delivered",
        "returned",
        "cancelled",
      ],
      default: "pending",
      index: true,
    },

    // Addresses
    shippingAddress: {
      type: addressSchema,
      required: true,
    },
    billingAddress: addressSchema,

    // Payment & Shipping
    payment: {
      type: paymentSchema,
      required: true,
    },
    shipping: {
      type: shippingSchema,
      required: true,
    },

    // Additional info
    notes: {
      type: String,
      trim: true,
    },
    internalNotes: {
      type: String,
      trim: true,
    },
    tags: {
      type: [String],
      default: [],
    },

    // Timestamps
    placedAt: {
      type: Date,
      default: Date.now,
      index: true,
    },
    estimatedDelivery: Date,
  },
  {
    timestamps: true, // Adds createdAt and updatedAt
    versionKey: false, // Removes __v field
  }
);

// Indexes for better query performance
orderSchema.index({ "customer.email": 1, placedAt: -1 });
orderSchema.index({ status: 1, placedAt: -1 });
orderSchema.index({ paymentStatus: 1, placedAt: -1 });
orderSchema.index({ shippingStatus: 1, placedAt: -1 });
orderSchema.index({ totalAmount: 1 });
orderSchema.index({ placedAt: -1 });

// Pre-save middleware to generate order number
orderSchema.pre("save", async function (next) {
  if (this.isNew && !this.orderNumber) {
    try {
      const count = await (this.constructor as any).countDocuments();
      this.orderNumber = `ORD-${String(count + 1).padStart(6, "0")}`;
    } catch (error) {
      return next(error as Error);
    }
  }
  next();
});

// Virtual for order age in days
orderSchema.virtual("ageInDays").get(function () {
  return Math.floor(
    (Date.now() - this.placedAt.getTime()) / (1000 * 60 * 60 * 24)
  );
});

// Virtual for total items count
orderSchema.virtual("totalItems").get(function () {
  return this.items.reduce((sum, item) => sum + item.quantity, 0);
});

export const Order = model<OrderDocument>("Order", orderSchema);
