import { Document } from "mongoose";

/**
 * RGB color value type
 */
export type RgbValue = {
  r: number;
  g: number;
  b: number;
};

/**
 * HSL color value type
 */
export type HslValue = {
  h: number;
  s: number;
  l: number;
};

/**
 * Color family type
 */
export type ColorFamily =
  | "red"
  | "orange"
  | "yellow"
  | "green"
  | "blue"
  | "purple"
  | "pink"
  | "brown"
  | "gray"
  | "black"
  | "white";

/**
 * Color type matching the frontend Color type
 */
export type IColor = {
  name: string;
  description: string;
  slug: string;
  hexValue: string;
  rgbValue: RgbValue;
  hslValue: HslValue;
  colorFamily: ColorFamily;
  isActive: boolean;
  productCount: number;
  sortOrder: number;
};

/**
 * Color document interface for MongoDB (keeping as interface for Document extension)
 */
export interface ColorDocument extends IColor, Document {
  _id: string;
  createdAt: Date;
  updatedAt: Date;
  hexToRgb(hex: string): RgbValue;
  rgbToHsl(r: number, g: number, b: number): HslValue;
}

/**
 * DTO for creating a new color
 */
export type CreateColorDto = {
  name: string;
  description?: string;
  hexValue: string;
  rgbValue?: RgbValue;
  hslValue?: HslValue;
  colorFamily: ColorFamily;
  isActive?: boolean;
  sortOrder?: number;
};

/**
 * DTO for updating a color
 */
export type UpdateColorDto = {
  name?: string;
  description?: string;
  slug?: string;
  hexValue?: string;
  rgbValue?: RgbValue;
  hslValue?: HslValue;
  colorFamily?: ColorFamily;
  isActive?: boolean;
  sortOrder?: number;
};

/**
 * Color filters for querying
 */
export interface ColorFilters {
  isActive?: boolean;
  search?: string;
  colorFamily?: ColorFamily;
  hexValue?: string;
}
