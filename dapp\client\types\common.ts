export type ISODateString = string;
export type UUID = string;

// Updated to match server-side currency options
export type CurrencyUnit = "EUR" | "USD";

export type WeightUnit = "g" | "kg";
export type DimensionUnit = "mm" | "cm";

export type AgeRestriction = "none" | "18+" | "21+";

export type Condition =
  | "new"
  | "like-new"
  | "excellent"
  | "good"
  | "fair"
  | "used"
  | "refurbished"
  | "vintage"
  | "antique"
  | "damaged";

// Order-related types
export type OrderStatus =
  | "pending"
  | "paid"
  | "processing"
  | "shipped"
  | "delivered"
  | "cancelled"
  | "refunded"
  | "returned"
  | "failed";

export type ShippingStatus =
  | "pending"
  | "preparing"
  | "shipped"
  | "in-transit"
  | "delivered"
  | "returned"
  | "cancelled";

export type PaymentStatus =
  | "pending"
  | "paid"
  | "failed"
  | "refunded"
  | "partially-refunded";

export type PaymentMethod =
  | "card"
  | "paypal"
  | "bank-transfer"
  | "cash-on-delivery"
  | "crypto";

// Color-related types
export type RgbValue = {
  r: number;
  g: number;
  b: number;
};

export type HslValue = {
  h: number;
  s: number;
  l: number;
};

export type ColorFamily =
  | "red"
  | "orange"
  | "yellow"
  | "green"
  | "blue"
  | "purple"
  | "pink"
  | "brown"
  | "gray"
  | "black"
  | "white";

// Material-related types
export type MaterialProperties = {
  durability: "low" | "medium" | "high";
  waterResistant: boolean;
  recyclable: boolean;
  weight: "light" | "medium" | "heavy";
};
